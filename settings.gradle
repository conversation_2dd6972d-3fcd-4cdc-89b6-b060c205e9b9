pluginManagement {
    repositories {
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
        mavenLocal()
        maven {
            url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
        maven {
            url = 'http://localhost:8080/repository/merged/'
            allowInsecureProtocol = true
        }
        gradlePluginPortal()
    }
    resolutionStrategy {
        eachPlugin {
            if (requested.id.id == 'productivity-plugin') {
                useModule('za.co.discovery.health.productivity:productivity-plugin:1.0.16-SNAPSHOT')
            }
        }
    }
}

rootProject.name = 'hs-personal-journey'
