pluginManagement {

    repositories {
        mavenLocal()
        maven {
            url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
        maven {
            url = 'http://localhost:8080/repository/merged/'
            allowInsecureProtocol = true
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
        gradlePluginPortal()
    }
    resolutionStrategy {
        eachPlugin {
            switch (requested.id.id) {
                case "productivity-plugin":
                    useModule('za.co.discovery.health.productivity:productivity-plugin:20240830.0.0-RELEASE')
                    break
            }
        }
    }
}

buildCache {
    local {
        enabled = false
    }
}

rootProject.name = 'hs-journey-importer'
