version: '3.8'

services:
  db:
    image: postgres:15
    container_name: db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=JOURNEY
    ports:
      - "5432:5432"
  dbadmin:
    image: dpage/pgadmin4
    container_name: dbadmin
    environment:
      - <PERSON><PERSON><PERSON>_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    ports:
      - "5050:80"
    depends_on:
      - db
  entity-service:
    image: wiremock/wiremock:2.32.0
    container_name: entity-service
    volumes:
      - ./local-setup:/home/<USER>
    ports:
      - 32000:8080
      - 8080:8080
      - 33671:8080
      - 8891:8080
      - 8333:8080
      - 33666:8080
      - 9191:8080
      - 34567:8080
