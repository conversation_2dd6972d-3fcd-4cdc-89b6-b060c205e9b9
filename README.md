# Journey Management System - QA Testing Guide

## 🔄 Key Concepts

### Journey States
- **ENROLLED**: User is actively participating in the journey
- **COMPLETED**: User has finished all requirements
- **UPCOMING**: Journey will start in the future
- **CAN_ENROLL**: User is eligible but not yet enrolled
- **EXPIRED**: Journey enrollment window has closed

### Milestone States
- **ACTIVE**: Current milestone user is working on
- **LOCKED**: Future milestone not yet accessible
- **COMPLETED**: Successfully finished milestone
- **NOT_ACHIEVED**: Failed to complete milestone requirements
- **SKIP**: Special milestone with no activities (SKIP week)
- **SKIPPED**: Past SKIP milestone that has been passed

### Activity Types
- **ACTIVITY**: Regular completion task
- **APPOINTMENT**: Scheduled appointment booking
- **SKIP**: No activity required (placeholder)

## 🎯 Core Testing Endpoints

### 1. Journey Discovery & Enrollment

#### Get All Available Journeys
```http
GET /api/v1/journey/categories?entityId={entityId}&localDateTime={optional}
```
**Purpose**: Retrieve all journeys available to user
**Returns**: Array of journey summaries with states and enrollment windows

#### Get Journey Details
```http
GET /api/v1/journey/categories/{categoryId}/details?entityId={entityId}&localDateTime={optional}
```
**Purpose**: Get comprehensive journey information including programs, milestones, and activities
**Returns**: Full journey details with current progress

#### Get Active Journeys Only
```http
GET /api/v1/journey/categories/active?entityId={entityId}&localDateTime={optional}
```
**Purpose**: Retrieve only active and monitoring-period journeys
**Returns**: Active journeys and completed journeys still in monitoring period

### 2. Journey Progress Simulation

#### Complete Activities (Test Utility)
```http
POST /test/complete-activities
Content-Type: application/json

{
  "mnemonic": "ACTIVITY_MNEMONIC_OR_APPOINTMENT_ID",
  "activityTnxId": **********,
  "entityId": 12345,
  "completedAt": "2025-08-01T20:58:27.992Z"
}
```
**Purpose**: Simulate activity completion for testing
**⚠️ WARNING**: Affects ALL user journeys simultaneously

#### Activity Completion (Production Endpoint)
```http
POST /api/activities/complete
Content-Type: application/json

{
  "entityId": "12345",
  "enrolmentId": 67890,
  "activityTransactionId": **********,
  "createdDate": "2025-08-01",
  "transactionDate": "2025-08-01",
  "allocationStatusEnum": "ALLOCATED",
  "groupProgramActivity": false
}
```

#### Appointment Completion
```http
POST /api/activities/appointments/complete
Content-Type: application/json

{
  "consultId": 12345,
  "appointmentId": 67890,
  "sessionId": 11111,
  "entityNo": 22222,
  "role": "PATIENT"
}
```

## 🕒 Time Manipulation for Testing

### localDateTime Parameter
Add `localDateTime` parameter to fast-forward user progress:
```http
GET /api/v1/journey/categories?entityId=12345&localDateTime=2025-12-01T10:00:00
```

**⚠️ CRITICAL WARNING**:
- This parameter affects **ALL** user journeys
- Use with caution as it simulates the user being at that specific date/time
- All journey calculations (milestones, deadlines, monitoring periods) will be recalculated

## 🧪 Test Scenarios

### Scenario 1: SKIP Week Testing (Using Journey ID 10027)

This journey has a perfect SKIP week setup:
- **Milestone 1** (Aug 4-11): 2 activities (appointment + assessment)
- **Milestone 2** (Aug 11-18): **SKIP WEEK** (empty activities array)
- **Milestone 3** (Aug 18-25): 2 activities (appointment + assessment)

```bash
# 1. Start journey - fast forward to journey start
GET /api/v1/journey/categories/10027/details?entityId=12345&localDateTime=2025-08-04T10:00:00

# Expected Results:
# - state: "ENROLLED" 
# - currentMilestone.iteration: 1
# - currentMilestone.status: "ACTIVE"
# - milestone 2 and 3 status: "LOCKED"

# 2. Complete Milestone 1 activities
POST /test/complete-activities
{
  "mnemonic": "4422",  # Appointment
  "activityTnxId": 20250804100001,
  "entityId": 12345,
  "completedAt": "2025-08-05T10:00:00.000Z"
}

POST /test/complete-activities
{
  "mnemonic": "NSBP",  # Assessment
  "activityTnxId": 20250804100002,
  "entityId": 12345,
  "completedAt": "2025-08-05T11:00:00.000Z"
}

# 3. Verify Milestone 1 completion
GET /api/v1/journey/categories/10027/details?entityId=12345&localDateTime=2025-08-05T12:00:00

# Expected Results:
# - Milestone 1 status: "COMPLETED"
# - Both activities in milestone 1: status "COMPLETED", activityCompletionCount: 1

# 4. Fast forward to SKIP week (Milestone 2)
GET /api/v1/journey/categories/10027/details?entityId=12345&localDateTime=2025-08-12T10:00:00

# Expected Results:
# - currentMilestone.iteration: 2
# - currentMilestone.status: "SKIP"
# - currentMilestone.activities: [] (empty array)
# - Milestone 1 status: "COMPLETED"
# - Milestone 3 status: "LOCKED"

# 5. Fast forward past SKIP week to Milestone 3
GET /api/v1/journey/categories/10027/details?entityId=12345&localDateTime=2025-08-19T10:00:00

# Expected Results:
# - currentMilestone.iteration: 3
# - currentMilestone.status: "ACTIVE"
# - Milestone 1 status: "COMPLETED"
# - Milestone 2 status: "SKIPPED" (automatically progressed)
# - Milestone 3 activities status: "ACTIVE" or equivalent

# 6. Complete final milestone activities
POST /test/complete-activities
{
  "mnemonic": "4417",  # Final appointment
  "activityTnxId": 20250819100001,
  "entityId": 12345,
  "completedAt": "2025-08-20T10:00:00.000Z"
}

POST /test/complete-activities
{
  "mnemonic": "NSBM",  # Final assessment
  "activityTnxId": 20250819100002,
  "entityId": 12345,
  "completedAt": "2025-08-20T11:00:00.000Z"
}

# 7. Verify journey completion
GET /api/v1/journey/categories/10027/details?entityId=12345&localDateTime=2025-08-25T10:00:00

# Expected Results:
# - state: "COMPLETED"
# - All milestones status: "COMPLETED", "SKIPPED", "COMPLETED"
# - monitoringPeriodEndTime: should now have a value (not null)
# - Program reward status should change to "AWARDED"
```

### Scenario 2: Monitoring Period Testing

```bash
# 1. After completing journey (continue from Scenario 1), verify monitoring period
GET /api/v1/journey/categories/10027/details?entityId=12345&localDateTime=2025-08-25T10:00:00

# Expected Results:
# - state: "COMPLETED"
# - monitoringPeriodEndTime: should be set (e.g., 14 days after completion for this journey)

# 2. Check journey appears in active list during monitoring
GET /api/v1/journey/categories/active?entityId=12345&localDateTime=2025-08-25T10:00:00

# Expected Results:
# - Journey 10027 should appear in the list
# - state: "COMPLETED"
# - Should have monitoring period details

# 3. Fast forward during monitoring period (but before it ends)
GET /api/v1/journey/categories/active?entityId=12345&localDateTime=2025-09-07T10:00:00

# Expected Results:
# - Journey 10027 still appears (monitoring ongoing)
# - Can verify remaining monitoring time

# 4. Fast forward past monitoring period end
# (Assuming 30-day monitoring: monitoringPeriodEndTime + 1 day)
GET /api/v1/journey/categories/active?entityId=12345&localDateTime=2025-09-25T10:00:00

# Expected Results:
# - Journey 10027 should NOT appear in active list
# - Monitoring period has ended

# 5. Verify journey still accessible via main categories endpoint
GET /api/v1/journey/categories?entityId=12345&localDateTime=2025-09-25T10:00:00

# Expected Results:
# - Journey 10027 appears with state: "COMPLETED"
# - No longer in monitoring period
```

### Scenario 4: New User Journey Enrollment
```bash
# 1. Check available journeys
GET /api/v1/journey/categories?entityId=12345

# 2. Verify journey state is CAN_ENROLL
# Expected: state = "CAN_ENROLL"

# 3. Fast-forward to enrollment start
GET /api/v1/journey/categories?entityId=12345&localDateTime=2025-09-01T00:00:00

# 4. Verify state changed to ENROLLED
# Expected: state = "ENROLLED"
```

### Scenario 2: Complete Journey Activities
```bash
# 1. Get journey details to see current milestone
GET /api/v1/journey/categories/1001/details?entityId=12345

# 2. Note current milestone activities and their mnemonics

# 3. Complete first activity
POST /test/complete-activities
{
  "mnemonic": "WEEK1_ACTIVITY1",
  "activityTnxId": 111111,
  "entityId": 12345,
  "completedAt": "2025-08-01T10:00:00.000Z"
}

# 4. Verify activity status changed
GET /api/v1/journey/categories/1001/details?entityId=12345
# Expected: activity status = "COMPLETED"
```

### Scenario 3: SKIP Week Testing
```bash
# 1. Fast-forward to a SKIP milestone
GET /api/v1/journey/categories/1001/details?entityId=12345&localDateTime=2025-09-15T00:00:00

# 2. Verify milestone status
# Expected: currentMilestone.status = "SKIP"

# 3. Fast-forward past SKIP week
GET /api/v1/journey/categories/1001/details?entityId=12345&localDateTime=2025-09-22T00:00:00

# 4. Verify previous milestone changed
# Expected: Previous milestone status = "SKIPPED"
# Expected: New current milestone is active
```

### Scenario 4: Journey Completion and Monitoring
```bash
# 1. Complete all required activities in journey
# (Use multiple POST /test/complete-activities calls)

# 2. Fast-forward to journey end
GET /api/v1/journey/categories/1001/details?entityId=12345&localDateTime=2025-12-01T00:00:00

# 3. Verify journey completed
# Expected: state = "COMPLETED"

# 4. Check if still appears in active list (monitoring period)
GET /api/v1/journey/categories/active?entityId=12345&localDateTime=2025-12-01T00:00:00

# 5. Fast-forward past monitoring period
GET /api/v1/journey/categories/active?entityId=12345&localDateTime=2026-01-01T00:00:00
# Expected: Journey should not appear in active list
```

## 🔍 Validation Points

### SKIP Week Validation Checklist
✅ **Milestone with empty activities array**
- `currentMilestone.activities: []`
- `currentMilestone.status: "SKIP"`
- No manual completion required

✅ **Automatic progression**
- SKIP milestone automatically becomes "SKIPPED" when time window passes
- Next milestone becomes active without user intervention
- No activities to complete in SKIP milestone

✅ **Journey progress calculation**
- SKIP weeks count toward overall journey progress
- Journey completion percentage includes SKIP milestones
- Rewards are still eligible despite skipped milestone

### Monitoring Period Validation Checklist
✅ **Monitoring period activation**
- `monitoringPeriodEndTime` changes from `null` to actual date upon completion
- Typically 30-90 days after journey completion
- Journey state remains "COMPLETED"

✅ **Active journey list behavior**
- Completed journeys appear in `/categories/active` during monitoring
- Completed journeys do NOT appear in `/categories/active` after monitoring ends
- Journey still accessible via main `/categories` endpoint after monitoring

### Common Test Data Patterns
- CAN_ENROLL → ENROLLED (within enrollment window)
- ENROLLED → COMPLETED (all activities done)
- ENROLLED → EXPIRED (past deadline without completion)
- UPCOMING → CAN_ENROLL (enrollment window opens)

### Milestone Progression
- LOCKED → ACTIVE (when previous milestone completed)
- ACTIVE → COMPLETED (all activities finished)
- ACTIVE → NOT_ACHIEVED (deadline passed without completion)
- SKIP → SKIPPED (automatic progression)

### Activity Status Flow
- Initial state varies by activity type
- PENDING → COMPLETED (via completion endpoints)
- Check `activityCompletionCount` increments properly

## 🚨 Testing Precautions

### SKIP Week Specific Warnings
1. **No Manual Completion**: Never try to complete activities in SKIP milestones (they have none)
2. **Time-Based Progression**: SKIP milestones progress automatically based on date ranges
3. **Status Validation**: Ensure SKIP → SKIPPED transition happens correctly
4. **Activity Arrays**: Always verify `activities: []` for SKIP milestones

### Monitoring Period Specific Warnings
1. **Monitoring Period Setup**: Not all journeys may have monitoring periods configured
2. **Active List Changes**: Behavior difference between active enrollments vs monitoring period completions
3. **Date Sensitivity**: Monitoring periods are highly date-dependent, use precise `localDateTime`
4. **Cleanup**: Monitoring periods affect system performance, clean up test data appropriately

### General Testing Precautions

1. **Isolated Testing Environment**: Always use dedicated test entities
2. **Data Reset**: Clear test data between scenarios when needed
3. **Time Synchronization**: Be aware that `localDateTime` affects all user journeys
4. **Activity Transaction IDs**: Use unique `activityTnxId` values to avoid conflicts
5. **Monitoring Impact**: Remember completed journeys may still appear in active lists during monitoring periods

## 📊 Common Test Data Patterns

### Entity IDs
Use consistent test entity IDs across scenarios:
- Primary test user: `entityId: 12345`
- Secondary test user: `entityId: 12346`

### Activity Transaction IDs
Use timestamp-based IDs to ensure uniqueness:
- Format: `YYYYMMDDHHmmss` (e.g., `20250801143000`)

### Date Formats
- API dates: `YYYY-MM-DDTHH:mm:ss.sssZ`
- LocalDateTime: `YYYY-MM-DDTHH:mm:ss`

## 🔧 Troubleshooting Guide

### SKIP Week Issues
**Problem**: SKIP milestone not progressing to SKIPPED  
**Solution**: Check time windows - ensure `localDateTime` is past `milestoneTo` date

**Problem**: SKIP milestone showing activities  
**Solution**: Verify journey configuration - SKIP milestones should have `activities: []`

**Problem**: Unable to progress past SKIP milestone  
**Solution**: Ensure previous milestone is COMPLETED, not just activities completed

### Monitoring Period Issues
**Problem**: `monitoringPeriodEndTime` remains null after completion  
**Solution**: Check journey configuration - not all journeys have monitoring periods enabled

**Problem**: Completed journey not appearing in active list  
**Solution**: Verify journey has monitoring period and current date is within monitoring window

**Problem**: Journey still in active list after monitoring should end  
**Solution**: Check exact monitoring end time calculation and timezone handling
