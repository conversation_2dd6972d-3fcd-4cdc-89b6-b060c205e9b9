# HS Journey Importer

A Spring Boot application that processes and imports journey data for health and wellness programs. This service handles bulk data imports, transformations, and integrations with downstream systems.

## Overview

The HS Journey Importer streamlines the process of importing member journey data from various sources into the health system platform. It provides a robust pipeline for data validation, transformation, and storage.

## Key Features

- **File Upload Processing**: Accepts CSV and other structured data formats via REST API
- **Data Validation**: Ensures data integrity before processing
- **Batch Processing**: Handles large datasets efficiently
- **Journey Mapping**: Transforms raw data into structured journey records
- **Member Management**: Associates journey data with member profiles
- **Database Integration**: Stores processed data in SAP HANA database
- **Job Monitoring**: Tracks import job status and progress

## High-Level Process Flow

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌────────────────────┐
│   CSV File  │───▶│  Staging Data   │───▶│ Normalized Data │───▶│ Data Enrichment │───▶│    Trigger      │───▶│   Process Journey  │
│             │    │ (Unstructured)  │    │  (Structured)   │    │ (Entity No)     │    │  Journey        │    │    Enrollment      │
│             │    │                 │    │                 │    │                 │    │  Enrollment     │    │                    │
└─────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘    └────────────────────┘

                 └──────────────────────────── Journey Importer Service ────────────────────────────────────┘ └────Journey Service───┘
```

1. **Data Ingestion**
   - Upload journey data files through REST endpoints
   - Support for multiple file formats and batch uploads

2. **Data Validation**
   - Validate file structure and required fields
   - Check data quality and consistency
   - Store staging data (unstructured) to database
   - Generate validation reports

3. **Data Transformation**
   - Map raw data to journey domain models
   - Apply business rules and transformations
   - Store normalized data (structured) to database

4. **Data Enrichment**
   - Determine user entity_no for member identification
   - Enrich data with additional member information
   - Store enriched data to database

5. **Trigger Journey Enrollment**
   - Trigger journey enrollment via API call to Journey Service
   - External service handles process journey enrollment

6. **Job Management**
   - Monitor import job progress
   - Handle error scenarios and retries
   - Provide status updates and notifications

## Getting Started

### Prerequisites
- Java 17+
- Gradle
- SAP HANA database access
- AWS CodeArtifact credentials

### Running the Application
```bash
./gradlew bootRun
```

### Docker Deployment
The application includes Jib configuration for containerized deployments.

## API Endpoints

- **POST /import** - Upload and process journey data files
- **GET /import/jobs** - Monitor import job status
- **POST /staging** - Manage staging data operations
- **POST /generate** - Generate test data for development

## Configuration

The application supports multiple profiles:
- `local` - Local development with H2 database
- `h2-db` - H2 database configuration
- `data-generator` - Test data generation capabilities

## Deployment

AWS CodeBuild integration is configured via `buildspec.yml` for automated CI/CD pipeline deployment.