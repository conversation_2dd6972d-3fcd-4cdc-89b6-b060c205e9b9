# @todo: add this pipeline to all services using Terraform.

name: Test

on:
  pull_request:
    branches:
      - master
      - main
      - stage
    types:
      - opened
      - edited
      - synchronize
      - reopened

jobs:
  build:
    name: Test
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::905860299560:role/github-actions-role
          role-session-name: testcoverage-gradle-build
      - name: Get AWS CodeArtefact Token
        run: echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain d2hp-v1 --domain-owner 905860299560 --query authorizationToken --output text --region us-east-1)" >> $GITHUB_ENV
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: 'temurin'
          cache: 'gradle'
      - name: <PERSON><PERSON> Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle') }}
          restore-keys: ${{ runner.os }}-gradle
      - name: Run Tests and check Code Coverage
        run: ./gradlew clean test jacocoTestCoverageVerification
