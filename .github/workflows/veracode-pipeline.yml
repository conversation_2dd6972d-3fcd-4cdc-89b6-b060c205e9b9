name: Veracode Pipeline

on:
  push:
    branches:
      - 'feature/**'
      - 'bugfix/**'
      - 'hotfix/**'
      - 'refactor/**'
      - 'build/**'
      - 'docs/**'
      - 'chore/**'
  pull_request:
    branches-ignore:
      - '*'

jobs:
  Veracode-Scan:
    # instead of using @main, we can reference a specific tag to a version, etc.
    uses: discovery-ltd/veracode-workflows/.github/workflows/gradle_withBaseline_pipelineScan_uploadScan_teamsSummary_githubIssues.yml@main
    with:

      # artifactName must include extension
      artifactName: 'hs-journey-importer-0.0.1-SNAPSHOT.jar'


      # veracodeAppName = name of application within Veracode UI
      veracodeAppName: 'hs-journey-importer'


      # contextPath = path to the application within the repository
      contextPath: '.'

      # libPath = path prefix to the jar libraries within the repository
      libPath: '.'

      # skipTests = true to skip tests, false to run tests
      skipTests: false

      # When true, pipeline scan will fail when flaws of defined severity are detected
      # When false or not provided, pipeline scan will continue when flaws of defined severity are detected
      # Flaw severity defined in shared workflow
      breakBuildOnFlawDetected: true

      # javaVersion = version of Java used in the application
      # The value should be either 11 or 21
      javaVersion: '21'

    secrets:
      VERACODE_API_ID: ${{ secrets.VERACODE_API_ID }}
      VERACODE_API_KEY: ${{ secrets.VERACODE_API_KEY }}
      MS_TEAMS_WEBHOOK_URI: ${{ secrets.MS_TEAMS_WEBHOOK_URI }}
