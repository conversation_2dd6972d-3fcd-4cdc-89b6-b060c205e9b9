name: Stage PR Check

on:
  pull_request:
    branches:
      - main
      - stage
jobs:
  main:
    name: Stage PR Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Check for stage pull request
        run: |
          STAGE_BRANCH="stage"
          HEAD_BRANCH=${{ github.event.pull_request.head.ref }}
          BASE_BRANCH=${{ github.event.pull_request.base.ref }}
          echo "Head branch: $HEAD_BRANCH"
          echo "Base branch: $BASE_BRANCH"

          # Check if there is an open pull request from current branch
          STAGE_PR_COUNT=$(gh pr list --state open -H $HEAD_BRANCH -B $STAGE_BRANCH | wc -l)
          if [[ $STAGE_PR_COUNT -eq 0 ]]; then
            echo "No open pull request found from $HEAD_BRANCH to $STAGE_BRANCH. Blocking merge to main branch."
            exit 1
          fi

          echo "Open pull requests found for $HEAD_BRANCH to both main and $STAGE_BRANCH. Allow merge."
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}