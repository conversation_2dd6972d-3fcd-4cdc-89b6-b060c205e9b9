buildscript {
    repositories {
        mavenLocal()
        maven {
            name = 'd2hp-v1-d2hp-v1-artifacts'
            url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
        maven {
            url = 'http://localhost:8080/repository/merged/'
            allowInsecureProtocol = true
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
    }


    ext {
        flywayVersion = '8.4.1'
        cloudConfigVersion = "3.1.4"
        sapHanaDriverVersion = "2.12.9"
    }

    dependencies {
        classpath "jakarta.xml.bind:jakarta.xml.bind-api:2.3.2"
    }
}

plugins {
    id "jacoco"
    id "org.sonarqube" version "4.0.0.2929"
    id 'pmd'
    id 'idea'
    id 'java'
    id 'checkstyle'
    id 'productivity-plugin'
    id "se.solrike.sonarlint" version "2.0.0"
    id "org.openapi.generator" version "6.2.0"
    id "com.diffplug.spotless" version "6.25.0"
    id 'org.springframework.boot' version '2.7.12'
    id 'com.google.cloud.tools.jib' version '3.3.1'
    id 'za.co.discovery.publisher.aws-codeartifact-publisher' version '1.0.0'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
}

ext {
    junitVersion = '5.9.0'
    flywayVersion = '8.4.1'
    mockitoVersion = '4.2.0'
    camundaVersion = '7.15.0'
    cucumberVersion = '6.10.4'
    cloudConfigVersion = "3.1.4"
}

group = 'za.co.discovery.health.journey'
version = '0.0.1-SNAPSHOT'
//sourceCompatibility = JavaVersion.VERSION_11

idea {
    module { generatedSourceDirs += file('build/generated/src/main/java') }
}

sourceSets {
    main { java { srcDirs += file('build/generated/src/main/java') } }
}

configurations {
    compileOnly { extendsFrom annotationProcessor }
}

jar {
    enabled = true
}
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(11) // Replace with your desired Java version
    }
}

bootJar {
    manifest {
        attributes 'Start-Class': 'za.co.discovery.health.journey.JourneyApplication'
    }
}

repositories {
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
    mavenLocal()
    maven {
        name = 'd2hp-v1-d2hp-v1-artifacts'
        url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
        credentials {
            username "aws"
            password System.env.CODEARTIFACT_AUTH_TOKEN
        }
    }
    maven {
        url = 'http://localhost:8080/repository/merged/'
        allowInsecureProtocol = true
    }

}

dependencies {
    implementation "org.springframework.cloud:spring-cloud-starter:$cloudConfigVersion"
    implementation "org.springframework.cloud:spring-cloud-starter-config:$cloudConfigVersion"
    implementation("org.springframework.cloud:spring-cloud-starter-bootstrap:$cloudConfigVersion")
    implementation 'org.springframework.statemachine:spring-statemachine-core:4.0.0'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    //Spring
    implementation('org.springframework.boot:spring-boot-starter-jdbc') { exclude group: 'com.zaxxer', module: 'HikariCP' }
    implementation('org.springframework.boot:spring-boot-starter-data-jpa') { exclude group: 'com.zaxxer', module: 'HikariCP' }
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.hibernate:hibernate-core'
    implementation 'org.hibernate:hibernate-java8'
    implementation 'org.hibernate:hibernate-entitymanager'
    implementation 'org.hibernate:hibernate-ehcache'
    implementation 'org.ehcache:ehcache'

    implementation 'javax.cache:cache-api'
    implementation group: 'org.hibernate.common', name: 'hibernate-commons-annotations', version: '5.1.2.Final'
    implementation 'org.apache.tomcat:tomcat-jdbc'
    implementation 'com.h2database:h2'
    implementation 'org.postgresql:postgresql:42.6.2'
    implementation "org.flywaydb:flyway-core:${flywayVersion}"
    implementation 'com.lmax:disruptor:3.3.6'
//    implementation 'org.camunda.bpm.dmn:camunda-engine-dmn:7.15.0'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
    implementation 'commons-io:commons-io:2.6'
    implementation 'commons-validator:commons-validator:1.6'
    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'commons-validator:commons-validator:1.7'
    // Apache POI for Excel reading
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
    implementation 'commons-io:commons-io:2.11.0'
    implementation 'org.apache.commons:commons-jexl3:3.3'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.11.4'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-joda:2.11.4'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.11.4'
    implementation 'com.google.guava:guava:31.1-jre'
    implementation 'org.apache.commons:commons-text:1.9'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation "com.sap.cloud.db.jdbc:ngdbc:2.12.9"
    implementation 'commons-codec:commons-codec:1.15'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'za.co.discovery.health.hs:hs-starter-webclient:20241211'
    implementation 'org.postgresql:postgresql'

    // swagger
    implementation 'org.openapitools:jackson-databind-nullable:0.2.3'
    implementation "org.springdoc:springdoc-openapi-ui:1.6.11"
    implementation 'io.swagger:swagger-annotations:1.6.11'
    implementation 'io.springfox:springfox-swagger2:2.9.1'

    // pmd
    pmd 'io.github.dgroup:arch4u-pmd:0.1.0'
    pmd 'commons-io:commons-io:2.11.0'

    // mapstruct
    implementation 'org.mapstruct:mapstruct:1.4.2.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.2.Final'

    //logging
    //To send logging to a json file or to a TCP port (on kubernetes):
    implementation "net.logstash.logback:logstash-logback-encoder:7.2"
    //Conditionals in the logback configs:
    implementation 'org.codehaus.janino:janino'
    //Security
    implementation 'za.co.discovery.health.hs:hs-starter-resource-server:20240905.1'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation('org.springframework.security.oauth.boot:spring-security-oauth2-autoconfigure:2.2.8.RELEASE') {
        exclude group: 'org.bouncycastle', module: 'bcpkix-jdk15on'
    }
    implementation 'org.hibernate.validator:hibernate-validator'

    // exception handling
    implementation 'za.co.discovery.health.hs:hs-starter-exception-handler:20230413'
    //Camunda
    implementation "org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:$camundaVersion"
    implementation("de.odysseus.juel:juel-api:2.2.7")
    implementation("de.odysseus.juel:juel-impl:2.2.7")
    implementation("de.odysseus.juel:juel-spi:2.2.7")
    // Unique Lexicographic Sortable Identifier
    implementation 'de.huxhorn.sulky:de.huxhorn.sulky.ulid:8.2.0'

    // Tomcat Pool Configuration
    implementation 'za.co.discovery.health.hs:hs-database-tomcat-pool-configuration:20231225.7'

    // Shedlock
    implementation 'za.co.discovery.health.hs:hs-starter-shedlock:20240530'
    implementation 'net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.19.1'

    //Testing
    testImplementation group: 'org.hamcrest', name: 'hamcrest', version: '2.1'
    testImplementation 'com.shazam:shazamcrest:0.11'
    testAnnotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.restdocs:spring-restdocs-mockmvc'
    testImplementation 'org.springframework.kafka:spring-kafka-test'
    testImplementation 'org.awaitility:awaitility:3.1.0'

    testImplementation "junit:junit:4.13.2"
    testImplementation "org.junit.jupiter:junit-jupiter:${junitVersion}"
    testRuntimeOnly "org.junit.vintage:junit-vintage-engine:${junitVersion}"
    testRuntimeOnly 'org.hamcrest:hamcrest-all:1.3'
    testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVersion}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVersion}"

    //Mockito
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
    testImplementation "org.mockito:mockito-junit-jupiter:${mockitoVersion}"

    //Cucumber
    testImplementation "io.cucumber:cucumber-java:${cucumberVersion}"
    testImplementation "io.cucumber:cucumber-spring:${cucumberVersion}"
    testImplementation "io.cucumber:cucumber-junit:${cucumberVersion}"
    testImplementation "io.cucumber:cucumber-junit-platform-engine:${cucumberVersion}"

    //wiremock
    testImplementation "org.springframework.cloud:spring-cloud-contract-wiremock:${cloudConfigVersion}"

    //Spring Test Elements
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.jupiter', module: 'junit-jupiter'
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
        exclude group: 'junit', module: 'junit'
        exclude group: 'org.junit.jupiter', module: 'junit-jupiter-api'
        exclude group: 'org.junit.jupiter', module: 'junit-jupiter-engine'
    }

    //Testcontainers
    testImplementation 'org.testcontainers:junit-jupiter:1.18.0'
    testImplementation 'org.testcontainers:postgresql:1.18.0'
}

test {
    maxHeapSize = "2048m"
    testLogging.showStandardStreams = true
    useJUnit()
}

springBoot {
    buildInfo {
        properties {
            time = null
        }
    }
}

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/adoptopenjdk:11.0.8_10-jre-hotspot"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-personal-journey:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['8892']
        jvmFlags = [
            '-XX:MinRAMPercentage=60.0',
            '-XX:MaxRAMPercentage=80.0',
            '-XX:+PrintFlagsFinal',
            '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'za.co.discovery.health.journey.JourneyApplication'
    }
    allowInsecureRegistries = true
}

checkstyle {
    configFile = file("${rootDir}/config/checkstyle/checkstyle.xml")
    checkstyleTest.enabled = false
}

checkstyleMain {
    source = 'src/main/java'
}

pmd {
    consoleOutput = true
    toolVersion = "6.21.0"
    rulesMinimumPriority = 5
    ruleSets = ["${rootDir}/config/pmd/pmd-rulesets.xml"]
    sourceSets = [sourceSets.main]
    pmdMain {
        source = files(fileTree('src/main/java'))
        exclude '**/build/**'
    }
}

spotless {
    java {
        target fileTree('.') {
            include '**/*.java'
            exclude '**/build/**', '**/build-*/**'
        }
        googleJavaFormat().reorderImports(true).skipJavadocFormatting().aosp()
        toggleOffOn()
        palantirJavaFormat()
        removeUnusedImports()
        indentWithSpaces()
        trimTrailingWhitespace()
        endWithNewline()
        importOrder('', 'javax', 'java', 'com.acme', '\\#')
            .wildcardsLast()
    }
}

productivityGeneratorTask {

    productivitySetting {
        rootPackageName = 'za.co.discovery.health.journey.database'
        generatedDestination = 'build/generated/src/main/java'
        profiles = ['database']
        applicationName = 'journey'

        databaseSetting {
            cascadeDetails = "ALL"
            foreignKeyCollectionInverse = true
            foreignKeyCollectionLazy = true
            //removes both instances of collections, FK - PK - FK
            excludeForeignKeyAsCollection = false
            //Removed the Object References
            excludeForeignKeyAsManytoOne = false
//      additionalRepository = 'za.co.discovery.health.points.database.repository'
        }
    }
}

import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

tasks.register("config-flag-aggregator", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/config-flag-aggregator.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "com.za.disocvery.health.configflag.domain"
    apiPackage = "com.za.disocvery.health.configflag.api"
    validateSpec = false
    typeMappings = [
        Integer      : "Long",
        int          : 'Long',
//            multipartFile: 'org.springframework.web.multipart.MultipartFile'
        multipartFile: 'org.springframework.core.io.Resource',
        localDate    : 'java.time.LocalDate'
    ]
    configOptions = [
        interfaceOnly: 'true',
        library      : 'resttemplate',
        useTags      : "true",
        dateLibrary  : "legacy"
    ]
}

tasks.register("hs-pac-man", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/hs-pac-man.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.pacman.domain"
    apiPackage = "za.co.discovery.health.pacman.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "webclient",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
    typeMappings = [
        'java8-date-time': 'java.time.LocalDateTime'
    ]
    importMappings = [
        'PageableObject': 'org.springframework.data.domain.Pageable',
        'Pageable'      : 'org.springframework.data.domain.Pageable'
    ]
}

tasks.register("vap", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/vap.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "za.co.discovery.vap.domain"
    apiPackage = "za.co.discovery.vap.api"
    configOptions = [
        dateLibrary       : "legacy",
        useTags           : "true",
        library           : "resttemplate",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
}

tasks.register("hs-group-coaching", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/hs-group-coaching.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.group.coaching.domain"
    apiPackage = "za.co.discovery.health.group.coaching.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "webclient",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
}

tasks.register("hs-entity-data", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/hs-entity-data.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.entity.domain"
    apiPackage = "za.co.discovery.health.entity.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "webclient",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
}

tasks.register("hs-data-attribute-service", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/hs-data-attribute-service.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.dataattribute.model"
    apiPackage = "za.co.discovery.health.dataattribute.api"
    configOptions = [
        dateLibrary       : "legacy",
        useTags           : "true",
        library           : "webclient",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
}

tasks.register("hs-journey-importer-models", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/hs-journey-importer.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "com.vitality.journey.importer.domain"
    apiPackage = "com.vitality.journey.importer.api"

    globalProperties = [
        apis           : "false",
        models         : "", // empty string means "all models"
        supportingFiles: "false",
        modelDocs      : "false",
        apiDocs        : "false",
        apiTests       : "false",
        modelTests     : "false"
    ]

    configOptions = [
        dateLibrary            : "java8",
        useTags                : "true",
        library                : "webclient",
        generateApiTests       : "false",
        generateModelTests     : "false",
        hideGenerationTimestamp: "true"
    ]

    typeMappings = [
        'OffsetDateTime': 'java.time.LocalDateTime'
    ]
}

productivityGeneratorTask.dependsOn tasks."config-flag-aggregator", tasks."hs-pac-man", tasks."vap",
    tasks."hs-group-coaching", tasks."hs-entity-data", tasks."hs-data-attribute-service", tasks."hs-journey-importer-models"

compileJava.dependsOn productivityGeneratorTask

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        failOnViolation = true
        rule {
            limit {
                minimum = 0.1
            }
        }
    }
}

tasks.withType(JacocoReportBase).configureEach {
    classDirectories.setFrom files(
        classDirectories.files.collect { classesDir ->
            fileTree(dir: classesDir, includes: [
                "za/co/discovery/health/journey/**"
            ])
        }
    )
}
