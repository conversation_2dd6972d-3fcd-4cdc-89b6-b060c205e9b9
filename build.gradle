buildscript {
    repositories {
        mavenLocal()
        maven {
            name = 'd2hp-v1-d2hp-v1-artifacts'
            url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
    }

    ext {
        springCloudVersion = "2025.0.0"
        sapHanaDriverVersion = "2.12.9"
        cucumberVersion = "7.20.1"
        junitVersion = "5.11.4"
    }
}

plugins {
    id 'java'
    id 'idea'
    id 'productivity-plugin'
    id 'org.springframework.boot' version '3.5.4'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'com.google.cloud.tools.jib' version '3.4.5'
    id "org.openapi.generator" version "7.13.0"
    id 'za.co.discovery.publisher.aws-codeartifact-publisher' version '1.0.0'
    id "jacoco"
    id "org.sonarqube" version "6.1.0.5360"
}

sourceCompatibility = JavaVersion.VERSION_21
targetCompatibility = JavaVersion.VERSION_21

group = 'com.vitality.journey'
version = '0.0.1-SNAPSHOT'
description = 'Journeys data importer'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

sourceSets {
    main { java { srcDirs += file('src/generated/java') } }
    main { java { srcDirs += file('build/generated/src/main/java') } }
}

idea {
    module { generatedSourceDirs += file('src/generated/java') }
    module { generatedSourceDirs += file('build/generated/src/main/java') }
}

jar {
    enabled = true
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

springBoot {
    mainClass = 'com.vitality.journey.importer.JourneyImporter'
    buildInfo {
        properties {
            time = null
        }
    }
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

repositories {
    mavenLocal()
    maven {
        name = 'd2hp-v1-d2hp-v1-artifacts'
        url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
        credentials {
            username "aws"
            password System.env.CODEARTIFACT_AUTH_TOKEN ?: System.getProperty("codeArtifactAuthToken")
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
}

ext {
    set('cucumberVersion', "7.20.1")
    set('junitVersion', "5.11.4")
    set('mockitoVersion', "4.2.0")
    set('flywayVersion', '11.1.0')
    set('springFox', '3.0.0')
}

test {
    useJUnitPlatform()
}

tasks.withType(Checkstyle).configureEach {
    reports {
        xml.enabled false
        html.enabled true
    }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

productivityGeneratorTask {
    productivitySetting {
        rootPackageName = 'com.vitality.journey.importer.database'
        generatedDestination = 'build/generated/src/main/java'
        profiles = ['database']
        applicationName = 'hs-journey-importer'

        databaseSetting {
            cascadeDetails = "ALL"
            foreignKeyCollectionInverse = true
            foreignKeyCollectionLazy = true
            //removes both instances of collections, FK - PK - FK
            excludeForeignKeyAsCollection = false
            //Removed the Object References
            excludeForeignKeyAsManytoOne = false
        }
    }
}

configurations.configureEach {
    exclude group: 'com.zaxxer', module: 'HikariCP'
}

dependencies {
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'io.swagger:swagger-annotations:1.6.11'

    // MapStruct
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'

    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-batch'
    implementation 'org.springframework.boot:spring-boot-starter-validation'


    //Security
    implementation 'za.co.discovery.health.hs:hs-starter-resource-server-sb3:20250916.1'

    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

    implementation 'com.h2database:h2'
    implementation "org.flywaydb:flyway-core"
    implementation 'org.apache.tomcat:tomcat-jdbc'
    implementation "com.sap.cloud.db.jdbc:ngdbc:${sapHanaDriverVersion}"

    implementation 'org.apache.httpcomponents.client5:httpclient5'
    implementation 'org.apache.commons:commons-csv:1.14.1'

    // Swagger
    implementation 'za.co.discovery.health.hs:hs-starter-swagger:20240902'
    implementation "io.swagger.core.v3:swagger-annotations:2.2.15"
    implementation 'org.openapitools:jackson-databind-nullable:0.2.6'
    implementation 'io.swagger:swagger-annotations:1.6.11'

    implementation 'javax.annotation:javax.annotation-api:1.3.2'

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        testImplementation 'org.springframework.batch:spring-batch-test'
        exclude group: 'org.mockito', module: 'mockito-inline'
    }
    testImplementation 'org.wiremock.integrations:wiremock-spring-boot:3.10.6'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // Data generation
    implementation 'net.datafaker:datafaker:2.3.1'
}

configurations.implementation {
    exclude group: 'commons-logging', module: 'commons-logging'
}

tasks.named('test') {
    useJUnitPlatform()
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        failOnViolation = true
        rule {
            limit {
                minimum = 0.15
            }
        }
    }
}

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-journey-importer:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['33772']
        jvmFlags = [
            '-XX:MinRAMPercentage=60.0',
            '-XX:MaxRAMPercentage=80.0',
            '-XX:+PrintFlagsFinal',
            '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.journey.importer.JourneyImporter'
    }
    allowInsecureRegistries = true
}

import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

tasks.register("hs-entity-data-service", GenerateTask) {
    generatorName = "java"
    inputSpec = "$rootDir/src/main/resources/openapi/hs-entity-data-service.json".toString()
    outputDir = "$rootDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.entity.domain"
    apiPackage = "za.co.discovery.health.entity.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "restclient",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
}

productivityGeneratorTask.dependsOn tasks."hs-entity-data-service"

compileJava.dependsOn productivityGeneratorTask
