package za.co.discovery.health.journey.dto;

import lombok.Data;
import org.springframework.util.StringUtils;
import za.co.discovery.health.journey.model.bo.dmn.DecisionRule;

import java.util.Map;
import java.util.Objects;

@Data
public class ActivityRecommendationRule {

    // Input conditions
    private Integer milestoneValue;
    private String alliance;
    private String group;
    private String branch;

    // Outputs
    private String activityId;
    private String activityName;
    private String activityIcon;
    private String activityType;
    private Integer frequency;
    private String completionType; // "Mandatory" or "Optional"
    private String description;
    private Boolean isSkipMilestone;

    public DecisionRule toDecisionRule() {
        DecisionRule decisionRule = new DecisionRule();

        // Set inputs
        decisionRule.getInputs().put("milestoneValue", milestoneValue);
        decisionRule
                .getInputs()
                .put("alliance", StringUtils.hasText(alliance) && !Objects.equals(alliance, "-") ? alliance : null);
        decisionRule.getInputs().put("group", StringUtils.hasText(group) && !Objects.equals(group, "-") ? group : null);
        decisionRule
                .getInputs()
                .put("branch", StringUtils.hasText(branch) && !Objects.equals(branch, "-") ? branch : null);

        // Set outputs
        decisionRule.getOutputs().put("ActivityId", activityId);
        decisionRule.getOutputs().put("activityName", StringUtils.hasText(activityName) ? activityName : null);
        decisionRule.getOutputs().put("activityIcon", StringUtils.hasText(activityIcon) ? activityIcon : null);
        decisionRule.getOutputs().put("activityType", activityType);
        decisionRule.getOutputs().put("frequency", frequency);
        decisionRule.getOutputs().put("completionType", completionType);
        decisionRule.getOutputs().put("description", description);
        decisionRule.getOutputs().put("isSkipMilestone", isSkipMilestone);

        return decisionRule;
    }

    public static ActivityRecommendationRule of(final DecisionRule rule) {
        ActivityRecommendationRule activityRecommendationRule = new ActivityRecommendationRule();

        // Set inputs
        activityRecommendationRule.setMilestoneValue((Integer) rule.getInputs().get("milestoneValue"));
        String alliance1 = (String) rule.getInputs().get("alliance");
        if (!StringUtils.hasText(alliance1) || Objects.equals(alliance1, "-")) {
            alliance1 = null;
        }
        activityRecommendationRule.setAlliance(alliance1);

        String group1 = (String) rule.getInputs().get("group");
        if (!StringUtils.hasText(group1) || Objects.equals(group1, "-")) {
            group1 = null;
        }

        activityRecommendationRule.setGroup(group1);

        String branch1 = (String) rule.getInputs().get("branch");

        if (!StringUtils.hasText(branch1) || Objects.equals(branch1, "-")) {
            branch1 = null;
        }
        activityRecommendationRule.setBranch(branch1);

        // Set outputs
        final Map<String, Object> outputs = rule.getOutputs();
        activityRecommendationRule.setActivityId((String) outputs.get("ActivityId"));
        activityRecommendationRule.setActivityName((String) outputs.get("activityName"));
        activityRecommendationRule.setActivityIcon((String) outputs.get("activityIcon"));
        activityRecommendationRule.setActivityType((String) outputs.get("activityType"));
        activityRecommendationRule.setFrequency((Integer) outputs.get("frequency"));
        activityRecommendationRule.setCompletionType((String) outputs.get("completionType"));
        activityRecommendationRule.setDescription((String) outputs.get("description"));
        final Object isSkipMilestone1 = outputs.get("isSkipMilestone");
        activityRecommendationRule.setIsSkipMilestone(
                Boolean.parseBoolean(isSkipMilestone1 != null ? isSkipMilestone1.toString() : "false"));

        return activityRecommendationRule;
    }
}
