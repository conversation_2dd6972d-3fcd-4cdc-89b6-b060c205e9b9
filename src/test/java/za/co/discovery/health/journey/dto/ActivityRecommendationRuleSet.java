package za.co.discovery.health.journey.dto;

import lombok.Data;
import za.co.discovery.health.journey.model.bo.dmn.DmnDecisionTable;

import java.util.ArrayList;
import java.util.List;

@Data
public class ActivityRecommendationRuleSet {
    private String decisionId = "decision";
    private String decisionName = "Activity Recommendation";
    private String hitPolicy = "COLLECT";
    //    private String aggregation = "SUM";
    private List<ActivityRecommendationRule> rules;

    public ActivityRecommendationRuleSet() {
        rules = new ArrayList<>();
    }

    public DmnDecisionTable toDmnDecisionTable() {
        DmnDecisionTable decisionTable = new DmnDecisionTable();
        decisionTable.setId(decisionId);
        decisionTable.setName(decisionName);
        decisionTable.setHitPolicy(hitPolicy);
        //        decisionTable.setAggregation(aggregation);

        // Define input and output columns
        // Set inputs
        decisionTable.getInputColumns().put("alliance", "string");
        decisionTable.getInputColumns().put("group", "string");
        decisionTable.getInputColumns().put("branch", "string");

        decisionTable.getInputColumns().put("milestoneValue", "number");

        // Set outputs
        decisionTable.getOutputColumns().put("ActivityId", "string");
        decisionTable.getOutputColumns().put("activityName", "string");
        decisionTable.getOutputColumns().put("activityIcon", "string");
        decisionTable.getOutputColumns().put("activityType", "string");
        decisionTable.getOutputColumns().put("frequency", "number");
        decisionTable.getOutputColumns().put("completionType", "string");
        decisionTable.getOutputColumns().put("description", "string");
        decisionTable.getOutputColumns().put("isSkipMilestone", "boolean");
        // Add rules
        rules.forEach(it -> decisionTable.getRules().add(it.toDecisionRule()));

        return decisionTable;
    }

    public static ActivityRecommendationRuleSet of(DmnDecisionTable decisionTable) {
        ActivityRecommendationRuleSet ruleSet = new ActivityRecommendationRuleSet();
        ruleSet.setDecisionId(decisionTable.getId());
        ruleSet.setDecisionName(decisionTable.getName());
        ruleSet.setHitPolicy(decisionTable.getHitPolicy());
        //        ruleSet.setAggregation(decisionTable.getAggregation());

        // Convert rules
        decisionTable.getRules().forEach(rule -> {
            ActivityRecommendationRule activityRecommendationRule = ActivityRecommendationRule.of(rule);
            ruleSet.getRules().add(activityRecommendationRule);
        });

        return ruleSet;
    }
}
