package za.co.discovery.health.journey.steps;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import za.co.discovery.health.journey.SpringIntegrationTest;

@Slf4j
public class SmokeTestSteps extends SpringIntegrationTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Given("a test")
    public void aTest() {
        log.info("a test");
    }

    @When("a test is called")
    public void aTestIsCalled() {
        log.info("a test is called");
    }

    @Then("we get a test called")
    public void weGetATestCalled() {
        log.info("we got a test called");
    }

    @And("a wait is executed for {int} seconds")
    public void aWaitIsExecutedForSeconds(final int seconds) throws InterruptedException {
        Thread.sleep(seconds * 1_000L);
    }

    // NEW STEP: verifying the DB is reachable
    @Then("the database is up")
    public void theDatabaseIsUp() {
        // Simple query to ensure we can talk to the DB
        final Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
        if (result == null || result != 1) {
            throw new IllegalStateException("Database check failed (expected 1 but got " + result + ")");
        }

        log.info("Database is up and responded with '{}'", result);
    }
}
