package za.co.discovery.health.journey.dto;

import lombok.Data;
import za.co.discovery.health.journey.model.bo.dmn.DecisionRule;

@Data
public class JourneyActivationRule {

    // Input conditions
    private String alliance;
    private String group;
    private String branch;

    // Output
    private String journeyCategory;

    public static JourneyActivationRule of(final DecisionRule rule) {
        JourneyActivationRule activationRule = new JourneyActivationRule();
        activationRule.setAlliance((String) rule.getInputs().get("alliance"));
        activationRule.setGroup((String) rule.getInputs().get("group"));
        activationRule.setBranch((String) rule.getInputs().get("branch"));
        activationRule.setJourneyCategory((String) rule.getOutputs().get("journeyCategory"));
        return activationRule;
    }

    public DecisionRule toDecisionRule() {
        DecisionRule decisionRule = new DecisionRule();
        decisionRule.getInputs().put("alliance", alliance);
        decisionRule.getInputs().put("group", group);
        decisionRule.getInputs().put("branch", branch);
        decisionRule.getOutputs().put("journeyCategory", journeyCategory);
        return decisionRule;
    }
}
