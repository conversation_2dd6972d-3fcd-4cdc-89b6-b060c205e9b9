package za.co.discovery.health.journey.world;

import lombok.Data;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.test.web.servlet.MockMvc;
import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;

import java.util.List;

@Component
@Scope("cucumber-glue")
@Data
public class JourneyWorld {

    private MockMvc mockMvc;
    private List<UserCategoryEnrollmentDto> userEnrollments;
}
