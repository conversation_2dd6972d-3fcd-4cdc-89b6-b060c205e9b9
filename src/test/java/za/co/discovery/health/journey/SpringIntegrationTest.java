package za.co.discovery.health.journey;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shazam.shazamcrest.matcher.CustomisableMatcher;
import io.cucumber.spring.CucumberContextConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.util.ReflectionUtils;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;
import za.co.discovery.health.JourneyApplicationTests;
import za.co.discovery.health.hs.resourceserver.service.LoggedInUserService;
import za.co.discovery.health.journey.config.TestKafkaConfig;
import za.co.discovery.health.journey.world.JourneyWorld;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import static com.shazam.shazamcrest.matcher.Matchers.sameBeanAs;
import static org.hamcrest.MatcherAssert.assertThat;

@EmbeddedKafka(
        controlledShutdown = true,
        partitions = 1,
        brokerProperties = {"listeners=PLAINTEXT://localhost:9092", "port=9092"},
        topics = {
            //            KafkaTopics.COMPLIANCE_CALCULATED_EVENT,
            //            KafkaTopics.ENROLLMENT_COMPLETION_EVENT,
            //            KafkaTopics.ACTIVITY_TRANSACTION_EVENT,
            //            KafkaTopics.REWARD_PROCESSED_EVENT,
            //            KafkaTopics.PERSONAL_PROGRAM_COMPLETE_EVENT,
            //            KafkaTopics.RANKING_EVENT
        })
@SpringBootTest(
        classes = {JourneyApplicationTests.class, TestKafkaConfig.class},
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ContextConfiguration
@CucumberContextConfiguration
@Slf4j
@AutoConfigureWireMock(port = 0)
@ActiveProfiles("test")
@MockBean(LoggedInUserService.class)
public abstract class SpringIntegrationTest {

    @Autowired
    protected JourneyWorld journeyWorld;

    protected SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    protected ObjectMapper objectMapper = new ObjectMapper();

    // Create a single reusable container for all tests
    private static final PostgreSQLContainer<?> postgresContainer = new PostgreSQLContainer<>(
                    DockerImageName.parse("postgres:15"))
            .withDatabaseName("testdb")
            .withUsername("testuser")
            .withPassword("testpass");

    static {
        // Start container once before any test scenario
        postgresContainer.start();
    }

    // Dynamically bind container properties to Spring Environment
    @DynamicPropertySource
    static void registerPgProperties(final DynamicPropertyRegistry registry) {
        log.info("registerPgProperties");
        log.info("registerPgProperties: {}", postgresContainer.getJdbcUrl());

        registry.add("spring.datasource.url", postgresContainer::getJdbcUrl);
        registry.add("spring.datasource.username", postgresContainer::getUsername);
        registry.add("spring.datasource.password", postgresContainer::getPassword);
    }

    protected <T> void hasExpectedFieldValues(final T expected, final T actual, final String... ignoreFields) {
        final CustomisableMatcher<T> matcher = sameBeanAs(expected);
        // ignore fields we don't specify in the feature file
        ReflectionUtils.doWithFields(expected.getClass(), field -> {
            field.setAccessible(true);
            final Object value = field.get(expected);
            if (value == null) {
                log.trace("ignoring field {}", field.getName());
                matcher.ignoring(field.getName());
            }
        });
        for (String ignoreField : ignoreFields) {
            log.trace("ignoring field {}", ignoreField);
            matcher.ignoring(ignoreField);
        }
        assertThat(actual, matcher);
    }

    protected String convertToString(final Object o) {
        final String request;
        try {
            request = objectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return request;
    }

    protected <T> T convertToObject(final String o, final Class<T> clazz) {
        final T request;
        try {
            request = objectMapper.readValue(o, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return request;
    }

    protected Date convertTo(final LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
}
