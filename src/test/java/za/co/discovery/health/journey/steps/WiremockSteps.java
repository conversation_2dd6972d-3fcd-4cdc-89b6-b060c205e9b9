package za.co.discovery.health.journey.steps;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import io.cucumber.java.BeforeStep;
import io.cucumber.java.en.Given;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import za.co.discovery.health.journey.SpringIntegrationTest;
import za.co.discovery.health.journey.util.TestDtoBuilder;

import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;

/**
 * class for writing wiremock stubs
 */
@Slf4j
public class WiremockSteps extends SpringIntegrationTest {

    @Autowired
    private WireMockServer wireMockServer;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeStep
    void cleanup() {
        wireMockServer.resetAll();
    }

    @SneakyThrows
    @Given("setup pac-man response {}")
    public void setupPacMan(final String programName) {

        wireMockServer.stubFor(get(urlMatching("/api/v1/program-enrolment-cohort-activity/dto/filter"
                        + "\\?page=0&size=**********&entityNo=1&programName="
                        + programName.replace(" ", "%20")
                        + ".*"))
                .willReturn(okJson(objectMapper.writeValueAsString(
                        TestDtoBuilder.PageProgramEnrolmentCohortActivityDTOBuilder.build()))));
    }
}
