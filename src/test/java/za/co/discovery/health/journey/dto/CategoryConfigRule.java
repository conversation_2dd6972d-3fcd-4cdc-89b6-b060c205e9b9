package za.co.discovery.health.journey.dto;

import lombok.Data;
import org.springframework.util.StringUtils;
import za.co.discovery.health.journey.model.bo.dmn.DecisionRule;

import java.util.Objects;

@Data
public class CategoryConfigRule {

    // Input conditions
    private String alliance;
    private String branch;
    private String group;

    // Outputs
    private String journeyStartTime;
    private String activityPrecondition;
    private String enrollmentPrecondition;
    private String enrollmentStartTime;
    private String enrollmentEndTime;
    private Integer maxParticipants;
    private Integer monitoringPeriodDuration;

    public static CategoryConfigRule of(final DecisionRule rule) {
        CategoryConfigRule configRule = new CategoryConfigRule();

        // Set inputs
        final String alliance1 = (String) rule.getInputs().get("alliance");
        if (alliance1 == null || !alliance1.isBlank() || !Objects.equals(alliance1, "-")) {
            configRule.setAlliance(alliance1);
        } else {
            configRule.setAlliance(null);
        }

        final String branch1 = (String) rule.getInputs().get("branch");
        if (branch1 == null || !branch1.isBlank() || !Objects.equals(branch1, "-")) {
            configRule.setBranch(branch1);
        } else {
            configRule.setBranch(null);
        }

        final String group1 = (String) rule.getInputs().get("group");
        if (group1 == null || !group1.isBlank() || !Objects.equals(group1, "-")) {
            configRule.setGroup(group1);
        } else {
            configRule.setGroup(null);
        }

        // Set outputs
        configRule.setJourneyStartTime((String) rule.getOutputs().get("journeyStartTime"));
        configRule.setActivityPrecondition((String) rule.getOutputs().get("activityPrecondition"));
        configRule.setEnrollmentPrecondition((String) rule.getOutputs().get("enrollmentPrecondition"));
        configRule.setEnrollmentStartTime((String) rule.getOutputs().get("enrollmentStartTime"));
        configRule.setEnrollmentEndTime((String) rule.getOutputs().get("enrollmentEndTime"));
        configRule.setMaxParticipants((Integer) rule.getOutputs().get("maxParticipants"));
        configRule.setMonitoringPeriodDuration((Integer) rule.getOutputs().get("monitoringPeriodDuration"));

        return configRule;
    }

    public DecisionRule toDecisionRule() {
        DecisionRule decisionRule = new DecisionRule();

        // Set inputs
        decisionRule
                .getInputs()
                .put("alliance", StringUtils.hasText(alliance) && !Objects.equals(alliance, "-") ? alliance : null);
        decisionRule.getInputs().put("group", StringUtils.hasText(group) && !Objects.equals(group, "-") ? group : null);
        decisionRule
                .getInputs()
                .put("branch", StringUtils.hasText(branch) && !Objects.equals(branch, "-") ? branch : null);

        // Set outputs
        decisionRule
                .getOutputs()
                .put("journeyStartTime", StringUtils.hasText(journeyStartTime) ? journeyStartTime : null);
        decisionRule
                .getOutputs()
                .put("activityPrecondition", StringUtils.hasText(activityPrecondition) ? activityPrecondition : null);
        decisionRule
                .getOutputs()
                .put(
                        "enrollmentPrecondition",
                        StringUtils.hasText(enrollmentPrecondition) ? enrollmentPrecondition : null);
        decisionRule
                .getOutputs()
                .put("enrollmentStartTime", StringUtils.hasText(enrollmentStartTime) ? enrollmentStartTime : null);
        decisionRule
                .getOutputs()
                .put("enrollmentEndTime", StringUtils.hasText(enrollmentEndTime) ? enrollmentEndTime : null);
        decisionRule.getOutputs().put("maxParticipants", maxParticipants != null ? maxParticipants : null);
        decisionRule
                .getOutputs()
                .put("monitoringPeriodDuration", monitoringPeriodDuration != null ? monitoringPeriodDuration : null);

        return decisionRule;
    }
}
