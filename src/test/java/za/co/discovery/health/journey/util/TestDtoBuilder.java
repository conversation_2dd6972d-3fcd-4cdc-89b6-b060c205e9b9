package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;
import za.co.discovery.health.pacman.domain.PageProgramEnrolmentCohortActivityDTO;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

import java.util.List;

@UtilityClass
public class TestDtoBuilder {

    @UtilityClass
    public static class PageProgramEnrolmentCohortActivityDTOBuilder {
        public static PageProgramEnrolmentCohortActivityDTO build() {
            final PageProgramEnrolmentCohortActivityDTO dto = new PageProgramEnrolmentCohortActivityDTO();
            dto.setTotalElements(0L);
            dto.setNumberOfElements(0);
            dto.setTotalPages(0);
            dto.setEmpty(true);
            dto.setNumber(0);
            dto.setSize(0);
            dto.setContent(List.of(ProgramEnrolmentCohortActivityDTOBuilder.build()));
            return dto;
        }
    }

    @UtilityClass
    public static class ProgramEnrolmentCohortActivityDTOBuilder {
        public static ProgramEnrolmentCohortActivityDTO build() {
            final ProgramEnrolmentCohortActivityDTO dto = new ProgramEnrolmentCohortActivityDTO();
            dto.setActivityId("10000");
            dto.setProgramId("0");
            dto.programName("Get Ready");
            return dto;
        }
    }
}
