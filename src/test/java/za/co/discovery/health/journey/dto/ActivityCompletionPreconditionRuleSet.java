package za.co.discovery.health.journey.dto;

import lombok.Data;
import za.co.discovery.health.journey.model.bo.dmn.DmnDecisionTable;

import java.util.ArrayList;
import java.util.List;

@Data
public class ActivityCompletionPreconditionRuleSet {
    private String decisionId = "decision";
    private String decisionName = "Activity Completion Precondition";
    private String hitPolicy = "COLLECT";
    //    private String aggregation = "SUM";
    private List<ActivityCompletionRule> rules;

    public ActivityCompletionPreconditionRuleSet() {
        rules = new ArrayList<>();
    }

    public DmnDecisionTable toDmnDecisionTable() {
        DmnDecisionTable decisionTable = new DmnDecisionTable();
        decisionTable.setId(decisionId);
        decisionTable.setName(decisionName);
        decisionTable.setHitPolicy(hitPolicy);
        //        decisionTable.setAggregation(aggregation);

        // Define input and output columns
        // Set inputs
        decisionTable.getInputColumns().put("milestoneValue", "number");
        decisionTable.getInputColumns().put("activityId", "string");
        decisionTable.getInputColumns().put("alliance", "string");
        decisionTable.getInputColumns().put("group", "string");
        decisionTable.getInputColumns().put("branch", "string");

        // Set outputs
        decisionTable.getOutputColumns().put("iteration", "number");
        decisionTable.getOutputColumns().put("completionPreconditionType", "string");
        decisionTable.getOutputColumns().put("completionIdentifier", "string");
        // Add rules
        rules.forEach(it -> decisionTable.getRules().add(it.toDecisionRule()));

        return decisionTable;
    }

    public static ActivityCompletionPreconditionRuleSet of(DmnDecisionTable decisionTable) {
        ActivityCompletionPreconditionRuleSet ruleSet = new ActivityCompletionPreconditionRuleSet();
        ruleSet.setDecisionId(decisionTable.getId());
        ruleSet.setDecisionName(decisionTable.getName());
        ruleSet.setHitPolicy(decisionTable.getHitPolicy());
        //        ruleSet.setAggregation(decisionTable.getAggregation());

        // Convert rules
        decisionTable.getRules().forEach(rule -> {
            ActivityCompletionRule activityRecommendationRule = ActivityCompletionRule.of(rule);
            ruleSet.getRules().add(activityRecommendationRule);
        });

        return ruleSet;
    }
}
