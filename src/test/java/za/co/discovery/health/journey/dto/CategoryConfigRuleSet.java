package za.co.discovery.health.journey.dto;

import lombok.Data;
import za.co.discovery.health.journey.model.bo.dmn.DmnDecisionTable;

import java.util.ArrayList;
import java.util.List;

@Data
public class CategoryConfigRuleSet {
    private String decisionId = "decision";
    private String decisionName = "Category Configuration";
    private String hitPolicy = "FIRST";
    private String aggregation;
    private List<CategoryConfigRule> rules = new ArrayList<>();

    public CategoryConfigRuleSet() {
        this.rules = new ArrayList<>();
    }

    public DmnDecisionTable toDmnDecisionTable() {
        DmnDecisionTable decisionTable = new DmnDecisionTable();
        decisionTable.setId(decisionId);
        decisionTable.setName(decisionName);
        decisionTable.setHitPolicy(hitPolicy);
        decisionTable.setAggregation(aggregation);

        // Define input and output columns
        decisionTable.getInputColumns().put("alliance", "string");
        decisionTable.getInputColumns().put("group", "string");
        decisionTable.getInputColumns().put("branch", "string");

        decisionTable.getOutputColumns().put("journeyStartTime", "string");
        decisionTable.getOutputColumns().put("activityPrecondition", "string");
        decisionTable.getOutputColumns().put("enrollmentPrecondition", "string");
        decisionTable.getOutputColumns().put("enrollmentStartTime", "string");
        decisionTable.getOutputColumns().put("enrollmentEndTime", "string");
        decisionTable.getOutputColumns().put("maxParticipants", "string");
        decisionTable.getOutputColumns().put("monitoringPeriodDuration", "string");

        // Add rules
        rules.forEach(it -> decisionTable.getRules().add(it.toDecisionRule()));

        return decisionTable;
    }

    public static CategoryConfigRuleSet of(DmnDecisionTable decisionTable) {
        CategoryConfigRuleSet ruleSet = new CategoryConfigRuleSet();
        ruleSet.setDecisionId(decisionTable.getId());
        ruleSet.setDecisionName(decisionTable.getName());
        ruleSet.setHitPolicy(decisionTable.getHitPolicy());
        ruleSet.setAggregation(decisionTable.getAggregation());

        // Convert rules
        decisionTable.getRules().forEach(rule -> {
            CategoryConfigRule configRule = CategoryConfigRule.of(rule);
            ruleSet.getRules().add(configRule);
        });

        return ruleSet;
    }
}
