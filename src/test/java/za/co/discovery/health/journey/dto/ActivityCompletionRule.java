package za.co.discovery.health.journey.dto;

import lombok.Data;
import org.springframework.util.StringUtils;
import za.co.discovery.health.journey.model.bo.dmn.DecisionRule;

import java.util.Map;
import java.util.Objects;

@Data
public class ActivityCompletionRule {

    // Input conditions
    private Integer milestoneValue;
    private String activityId;
    private String alliance;
    private String group;
    private String branch;

    // Outputs
    private Integer iteration;
    private String completionPreconditionType;
    private String completionIdentifier;

    public DecisionRule toDecisionRule() {
        DecisionRule decisionRule = new DecisionRule();

        // Set inputs
        decisionRule.getInputs().put("milestoneValue", milestoneValue);
        decisionRule.getInputs().put("activityId", activityId);

        decisionRule
                .getInputs()
                .put("alliance", StringUtils.hasText(alliance) && !Objects.equals(alliance, "-") ? alliance : null);
        decisionRule.getInputs().put("group", StringUtils.hasText(group) && !Objects.equals(group, "-") ? group : null);
        decisionRule
                .getInputs()
                .put("branch", StringUtils.hasText(branch) && !Objects.equals(branch, "-") ? branch : null);

        // Set outputs
        decisionRule.getOutputs().put("iteration", iteration);
        decisionRule
                .getOutputs()
                .put(
                        "completionPreconditionType",
                        StringUtils.hasText(completionPreconditionType) ? completionPreconditionType : null);
        decisionRule
                .getOutputs()
                .put("completionIdentifier", StringUtils.hasText(completionIdentifier) ? completionIdentifier : null);

        return decisionRule;
    }

    public static ActivityCompletionRule of(final DecisionRule rule) {
        ActivityCompletionRule activityRecommendationRule = new ActivityCompletionRule();

        // Set inputs
        activityRecommendationRule.setMilestoneValue((Integer) rule.getInputs().get("milestoneValue"));
        activityRecommendationRule.setActivityId((String) rule.getInputs().get("activityId"));
        String alliance1 = (String) rule.getInputs().get("alliance");
        if (!StringUtils.hasText(alliance1) || Objects.equals(alliance1, "-")) {
            alliance1 = null;
        }
        activityRecommendationRule.setAlliance(alliance1);

        String group1 = (String) rule.getInputs().get("group");
        if (!StringUtils.hasText(group1) || Objects.equals(group1, "-")) {
            group1 = null;
        }

        activityRecommendationRule.setGroup(group1);

        String branch1 = (String) rule.getInputs().get("branch");

        if (!StringUtils.hasText(branch1) || Objects.equals(branch1, "-")) {
            branch1 = null;
        }
        activityRecommendationRule.setBranch(branch1);

        // Set outputs
        final Map<String, Object> outputs = rule.getOutputs();
        activityRecommendationRule.setIteration((Integer) outputs.get("iteration"));
        activityRecommendationRule.setCompletionIdentifier((String) outputs.get("completionIdentifier"));
        activityRecommendationRule.setCompletionPreconditionType((String) outputs.get("completionPreconditionType"));

        return activityRecommendationRule;
    }
}
