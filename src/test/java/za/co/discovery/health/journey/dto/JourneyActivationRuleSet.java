package za.co.discovery.health.journey.dto;

import lombok.Data;
import za.co.discovery.health.journey.model.bo.dmn.DmnDecisionTable;

import java.util.List;

@Data
public class JourneyActivationRuleSet {

    private String decisionId = "decision";
    private String decisionName = "Journey Activation";
    private String hitPolicy = "COLLECT";
    private String aggregation;

    private List<JourneyActivationRule> rules;

    public DmnDecisionTable toDmnDecisionTable() {
        DmnDecisionTable decisionTable = new DmnDecisionTable();
        decisionTable.setId(decisionId);
        decisionTable.setName(decisionName);
        decisionTable.setHitPolicy(hitPolicy);
        decisionTable.setAggregation(aggregation);

        // Define input and output columns
        decisionTable.getInputColumns().put("alliance", "string");
        decisionTable.getInputColumns().put("group", "string");
        decisionTable.getInputColumns().put("branch", "string");

        decisionTable.getOutputColumns().put("journeyCategory", "string");

        // Add rules
        rules.forEach(it -> decisionTable.getRules().add(it.toDecisionRule()));

        return decisionTable;
    }

    public static JourneyActivationRuleSet of(DmnDecisionTable decisionTable) {
        JourneyActivationRuleSet ruleSet = new JourneyActivationRuleSet();
        ruleSet.setDecisionId(decisionTable.getId());
        ruleSet.setDecisionName(decisionTable.getName());
        ruleSet.setHitPolicy(decisionTable.getHitPolicy());
        ruleSet.setAggregation(decisionTable.getAggregation());

        // Convert rules
        decisionTable.getRules().forEach(rule -> {
            JourneyActivationRule activationRule = JourneyActivationRule.of(rule);
            ruleSet.getRules().add(activationRule);
        });

        return ruleSet;
    }
}
