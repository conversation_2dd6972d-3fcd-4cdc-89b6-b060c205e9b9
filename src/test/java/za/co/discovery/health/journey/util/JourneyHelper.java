package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;
import org.junit.Assert;
import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserMilestoneEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserProgramEnrollmentDto;

import java.util.List;
import java.util.Optional;

@UtilityClass
public class JourneyHelper {

    public static void isAllActivitiesOfStatus(
            final List<UserMilestoneEnrollmentDto.ActivityDetails> milestoneActivities, final String status) {
        milestoneActivities.forEach(activity -> {
            Assert.assertNotNull(activity.getMnemonic());
            Assert.assertEquals(status, activity.getStatus());
        });
    }

    public static Optional<UserCategoryEnrollmentDto> getEnrollmentByCategoryName(
            final List<UserCategoryEnrollmentDto> userEnrollments, final String categoryName) {
        return userEnrollments.stream()
                .filter(enrollment -> enrollment.getCategoryName().equals(categoryName))
                .findFirst();
    }

    public static Optional<UserProgramEnrollmentDto> getProgramByProgramName(
            final UserCategoryEnrollmentDto userCategoryEnrollmentDto, final String programName) {
        return userCategoryEnrollmentDto.getJourneyPrograms().stream()
                .filter(program -> program.getProgramName().equals(programName))
                .findFirst();
    }
}
