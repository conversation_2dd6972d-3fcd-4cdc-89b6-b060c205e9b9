package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;

/**
 * class for constants
 */
@UtilityClass
public class TestConstants {
    public static final Long ENTITY_NO = 1L;
    public static final String GROUP_NAME = "Weight Management";
    public static final Long POLICY_NO = 1L;

    public final LocalDateTime EFF_DATE_FROM = LocalDateTime.now();

    public final LocalDateTime EFF_DATE_TO_DATE = EFF_DATE_FROM.plusYears(1);

    public final String CURRENT_INCENTIVE_PLAN_TEST_NAME = "Current incentive plan test name";
    public final String PREVIOUS_INCENTIVE_PLAN_TEST_NAME = "Previous incentive plan test name";
}
