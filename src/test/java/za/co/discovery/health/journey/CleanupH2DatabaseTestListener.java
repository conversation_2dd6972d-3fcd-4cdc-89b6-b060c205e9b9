package za.co.discovery.health.journey;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;
import za.co.discovery.health.journey.util.CleanupDbService;
import za.co.discovery.health.journey.util.EventHelper;

@Slf4j
public class CleanupH2DatabaseTestListener implements TestExecutionListener, Ordered {
    private static final String H2_SCHEMA_NAME = "JOURNEY";
    private static final String SHEDLOCK = "SHEDLOCK";

    @Override
    public void afterTestClass(final TestContext testContext) {
        cleanupDatabase(testContext);
        cleanupEventList(testContext);
    }

    private void cleanupDatabase(final TestContext testContext) {
        final CleanupDbService cleanupDbService =
                testContext.getApplicationContext().getBean(CleanupDbService.class);
        cleanupDbService.resetDB(H2_SCHEMA_NAME, SHEDLOCK);
    }

    private void cleanupEventList(final TestContext testContext) {
        final EventHelper eventHelper = testContext.getApplicationContext().getBean(EventHelper.class);
        eventHelper.clean();
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
