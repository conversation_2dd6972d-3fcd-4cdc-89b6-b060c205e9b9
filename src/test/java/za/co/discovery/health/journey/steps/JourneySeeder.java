package za.co.discovery.health.journey.steps;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.constant.JourneyRewardTypeEnum;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategorization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategorizationId;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategoryType;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCustomerDefinition;
import za.co.discovery.health.journey.database.databaseMapping.JourneyMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramMilestoneRewardCustomization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramRewardCustomization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategorizationRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategoryRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategoryTypeRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCustomerDefinitionRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneActivityRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneAwardRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentProgramAwardRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyMilestoneRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramBehaviourRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramMilestoneRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramMilestoneRewardCustomizationRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramRewardCustomizationRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyRecommendationOrderRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyRulesRepository;
import za.co.discovery.health.journey.dto.ActivityCompletionPreconditionRuleSet;
import za.co.discovery.health.journey.dto.ActivityCompletionRule;
import za.co.discovery.health.journey.dto.ActivityRecommendationRule;
import za.co.discovery.health.journey.dto.ActivityRecommendationRuleSet;
import za.co.discovery.health.journey.dto.CategoryConfigRule;
import za.co.discovery.health.journey.dto.CategoryConfigRuleSet;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.service.bo.dmn.DmnJsonTransformer;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class JourneySeeder {

    @Autowired
    private ExtendedJourneyCategorizationRepository extendedJourneyCategorizationRepository;

    @Autowired
    private ExtendedJourneyCategoryRepository extendedJourneyCategoryRepository;

    @Autowired
    private ExtendedJourneyCategoryTypeRepository extendedJourneyCategoryTypeRepository;

    @Autowired
    private ExtendedJourneyCustomerDefinitionRepository extendedJourneyCustomerDefinitionRepository;

    @Autowired
    private ExtendedJourneyEnrollmentMilestoneActivityRepository extendedJourneyEnrollmentMilestoneActivityRepository;

    @Autowired
    private ExtendedJourneyEnrollmentMilestoneAwardRepository extendedJourneyEnrollmentMilestoneAwardRepository;

    @Autowired
    private ExtendedJourneyEnrollmentMilestoneRepository extendedJourneyEnrollmentMilestoneRepository;

    @Autowired
    private ExtendedJourneyEnrollmentProgramAwardRepository extendedJourneyEnrollmentProgramAwardRepository;

    @Autowired
    private ExtendedJourneyEnrollmentRepository enrollmentRepository;

    @Autowired
    private ExtendedJourneyMilestoneRepository extendedJourneyMilestoneRepository;

    @Autowired
    private ExtendedJourneyProgramBehaviourRepository extendedJourneyProgramBehaviourRepository;

    @Autowired
    private ExtendedJourneyProgramMilestoneRepository extendedJourneyProgramMilestoneRepository;

    @Autowired
    private ExtendedJourneyProgramMilestoneRewardCustomizationRepository
            extendedJourneyProgramMilestoneRewardCustomizationRepository;

    @Autowired
    private ExtendedJourneyProgramRepository extendedJourneyProgramRepository;

    @Autowired
    private ExtendedJourneyProgramRewardCustomizationRepository extendedJourneyProgramRewardCustomizationRepository;

    @Autowired
    private ExtendedJourneyRecommendationOrderRepository extendedJourneyRecommendationOrderRepository;

    @Autowired
    private ExtendedJourneyRulesRepository extendedJourneyRulesRepository;

    @Autowired
    private DmnJsonTransformer transformer;

    // Context variables for Cucumber scenarios
    private JourneyCategory currentCategory;
    private JourneyProgram currentProgram;
    private JourneyCategorization currentCategorization;
    private List<ActivityRecommendationRule> currentRecommendedActivities = new ArrayList<>();
    private List<ActivityCompletionRule> currentActivityCompletionPreconditions = new ArrayList<>();
    private List<JourneyProgramMilestoneRewardCustomization> currentMilestoneRewards = new ArrayList<>();
    private List<JourneyProgramRewardCustomization> currentProgramRewards = new ArrayList<>();

    // Cucumber Step: Create category with categoryCode
    public void createCategoryWithCode(String categoryCode) {
        log.info("Creating category with code: {}", categoryCode);
        this.currentCategory = createJourneyCategory(
                "DEFAULT_TYPE",
                categoryCode,
                "EXT_REF_" + categoryCode,
                "2025-01-01T00:00:00",
                "DEFAULT_ACTIVITY_PRECONDITION",
                "DEFAULT_ENROLLMENT_PRECONDITION",
                "2025-01-01T00:00:00",
                "2025-12-31T23:59:59",
                1000,
                30);
    }

    // Cucumber Step: Configure category with specific configuration
    public void configureCategoryWith(Map<String, String> configuration) {
        if (currentCategory == null) {
            throw new IllegalStateException("No current category exists. Create a category first.");
        }

        log.info("Configuring category {} with configuration: {}", currentCategory.getCategoryCode(), configuration);

        // Update category configuration
        CategoryConfigRuleSet ruleSet = getCategoryConfigRuleSet(
                configuration.getOrDefault("journeyStartTime", "2025-01-01T00:00:00"),
                configuration.getOrDefault("activityPrecondition", "DEFAULT_ACTIVITY_PRECONDITION"),
                configuration.getOrDefault("enrollmentPrecondition", "DEFAULT_ENROLLMENT_PRECONDITION"),
                configuration.getOrDefault("enrollmentStartTime", "2025-01-01T00:00:00"),
                configuration.getOrDefault("enrollmentEndTime", "2025-12-31T23:59:59"),
                Integer.parseInt(configuration.getOrDefault("maxParticipants", "1000")),
                Integer.parseInt(configuration.getOrDefault("monitoringPeriodDuration", "30")));

        String ruleName = "JOURNEY_CATEGORY_RULE_" + currentCategory.getCategoryCode();
        currentCategory.setJourneyRules(createJourneyRule(ruleName, ruleSet));
        extendedJourneyCategoryRepository.save(currentCategory);
    }

    // Cucumber Step: Create program with name
    public void createProgramWithName(String programName) {
        log.info("Creating program with name: {}", programName);
        this.currentProgram = new JourneyProgram();
        currentProgram.setName(programName);
        currentProgram.setStatus("ACTIVE");
        currentProgram.setEffFrom(LocalDateTime.of(1970, 12, 31, 23, 59));
        currentProgram.setEffTo(LocalDateTime.of(9999, 12, 31, 23, 59));
        this.currentProgram = extendedJourneyProgramRepository.save(currentProgram);
    }

    // Cucumber Step: Categorize current category and program
    public void categorizeCategoryAndProgram() {
        if (currentCategory == null || currentProgram == null) {
            throw new IllegalStateException("Both category and program must exist before categorization.");
        }

        log.info(
                "Categorizing category {} with program {}",
                currentCategory.getCategoryCode(),
                currentProgram.getName());

        this.currentCategorization = createCategorization(currentCategory, currentProgram);
    }

    // Cucumber Step: Add recommended activity to program
    public void addRecommendedActivityToProgram(Map<String, String> activityData) {
        log.info("Adding recommended activity to program: {}", activityData);

        ActivityRecommendationRule activityRule = new ActivityRecommendationRule();
        activityRule.setActivityId(activityData.get("activityId"));
        activityRule.setActivityName(activityData.get("activityName"));
        activityRule.setActivityIcon(activityData.getOrDefault("activityIcon", "default-icon"));
        activityRule.setActivityType(activityData.getOrDefault("activityType", "DEFAULT"));
        activityRule.setFrequency(Integer.parseInt(activityData.getOrDefault("frequency", "1")));
        activityRule.setCompletionType(activityData.getOrDefault("completionType", "MANUAL"));
        activityRule.setIsSkipMilestone(Boolean.parseBoolean(activityData.getOrDefault("isSkipMilestone", "false")));
        activityRule.setMilestoneValue(Integer.parseInt(activityData.getOrDefault("milestoneValue", "1")));

        currentRecommendedActivities.add(activityRule);
    }

    // Cucumber Step: Add activity completion precondition
    public void addActivityCompletionPrecondition(Map<String, String> preconditionData) {
        log.info("Adding activity completion precondition: {}", preconditionData);

        ActivityCompletionRule completionRule = new ActivityCompletionRule();
        completionRule.setCompletionPreconditionType(preconditionData.get("completionPreconditionType"));
        completionRule.setCompletionIdentifier(preconditionData.get("completionIdentifier"));
        completionRule.setActivityId(preconditionData.get("activityId"));
        completionRule.setIteration(Integer.parseInt(preconditionData.getOrDefault("iteration", "1")));
        completionRule.setMilestoneValue(Integer.parseInt(preconditionData.getOrDefault("milestoneValue", "1")));

        currentActivityCompletionPreconditions.add(completionRule);
    }

    // Cucumber Step: Add milestone reward to program
    public void addMilestoneRewardToProgram(Map<String, String> rewardData) {
        if (currentProgram == null) {
            throw new IllegalStateException("No current program exists. Create a program first.");
        }

        log.info("Adding milestone reward to program: {}", rewardData);

        JourneyProgramMilestoneRewardCustomization milestoneReward = createJourneyProgramMilestoneRewardCustomization(
                currentProgram,
                JourneyRewardTypeEnum.valueOf(rewardData.get("rewardType")),
                rewardData.get("externalReference"));

        currentMilestoneRewards.add(milestoneReward);
    }

    // Cucumber Step: Add program reward
    public void addProgramReward(Map<String, String> rewardData) {
        if (currentProgram == null) {
            throw new IllegalStateException("No current program exists. Create a program first.");
        }

        log.info("Adding program reward: {}", rewardData);

        JourneyProgramRewardCustomization programReward = createJourneyProgramRewardCustomization(
                currentProgram,
                JourneyRewardTypeEnum.valueOf(rewardData.get("rewardType")),
                rewardData.get("externalReference"));

        currentProgramRewards.add(programReward);
    }

    // Cucumber Step: Finalize program setup with all configured rules and rewards
    public void finalizeProgramSetup() {
        if (currentProgram == null) {
            throw new IllegalStateException("No current program exists. Create a program first.");
        }

        log.info("Finalizing program setup for: {}", currentProgram.getName());

        // Create rule sets from current activities and preconditions
        ActivityRecommendationRuleSet recommendationRuleSet = new ActivityRecommendationRuleSet();
        recommendationRuleSet.setRules(currentRecommendedActivities);

        ActivityCompletionPreconditionRuleSet completionRuleSet = new ActivityCompletionPreconditionRuleSet();
        completionRuleSet.setRules(currentActivityCompletionPreconditions);

        // Create program behavior with rules
        JourneyProgramBehaviour programBehaviour = createProgramBehaviour(
                30L, // default duration
                "DEFAULT_PROGRAM_TRANSITION",
                "DEFAULT_MILESTONE_TRANSITION",
                recommendationRuleSet,
                completionRuleSet,
                currentProgram);

        currentProgram.setJourneyProgramBehaviour(programBehaviour);
        extendedJourneyProgramRepository.save(currentProgram);

        log.info("Program setup completed for: {}", currentProgram.getName());
    }

    // Validation methods for Cucumber Then steps
    public JourneyCategory getCurrentCategory() {
        return currentCategory;
    }

    public JourneyProgram getCurrentProgram() {
        return currentProgram;
    }

    public JourneyCategorization getCurrentCategorization() {
        return currentCategorization;
    }

    public List<ActivityRecommendationRule> getCurrentRecommendedActivities() {
        return new ArrayList<>(currentRecommendedActivities);
    }

    public List<ActivityCompletionRule> getCurrentActivityCompletionPreconditions() {
        return new ArrayList<>(currentActivityCompletionPreconditions);
    }

    public List<JourneyProgramMilestoneRewardCustomization> getCurrentMilestoneRewards() {
        return new ArrayList<>(currentMilestoneRewards);
    }

    public List<JourneyProgramRewardCustomization> getCurrentProgramRewards() {
        return new ArrayList<>(currentProgramRewards);
    }

    // Reset context for new scenarios
    public void resetContext() {
        currentCategory = null;
        currentProgram = null;
        currentCategorization = null;
        currentRecommendedActivities.clear();
        currentActivityCompletionPreconditions.clear();
        currentMilestoneRewards.clear();
        currentProgramRewards.clear();
    }

    // Original legacy method for backward compatibility
    public void seedJourney(Map<String, String> data) {
        resetContext();

        final String categoryType = data.get("categoryType");
        final String categoryCode = data.get("categoryCode");
        final String externalReference = data.get("externalReference");
        final String journeyStartTime = data.get("journeyStartTime");
        final String activityPrecondition = data.get("activityPrecondition");
        final String enrollmentPrecondition = data.get("enrollmentPrecondition");
        final String enrollmentStartTime = data.get("enrollmentStartTime");
        final String enrollmentEndTime = data.get("enrollmentEndTime");
        final Integer maxParticipants = Integer.parseInt(data.get("maxParticipants"));
        final Integer monitoringPeriodDuration = Integer.parseInt(data.get("monitoringPeriodDuration"));

        final String programTransitionRule = data.get("programTransitionRule");
        final String milestoneTransitionRule = data.get("milestoneTransitionRule");
        final Long duration = Long.parseLong(data.get("duration"));

        final String programRewardExternalReference = data.get("programRewardExternalReference");
        final String programRewardType = data.get("programRewardType");

        final String milestoneRewardExternalReference = data.get("milestoneRewardExternalReference");
        final String milestoneRewardType = data.get("milestoneRewardType");

        final String ActivityId = data.get("ActivityId");
        final String activityName = data.get("activityName");
        final String activityIcon = data.get("activityIcon");
        final String activityType = data.get("activityType");
        final String frequency = data.get("frequency");
        final String completionType = data.get("completionType");
        final String isSkipMilestone = data.get("isSkipMilestone");
        final String milestoneValue = data.get("milestoneValue");

        final String preconditionMilestoneValue = data.get("preconditionMilestoneValue");
        final String preconditionActivityId = data.get("preconditionActivityId");
        final String preconditionIteration = data.get("preconditionIteration");
        final String preconditionCompletionPreconditionType = data.get("preconditionCompletionPreconditionType");
        final String preconditionCompletionIdentifier = data.get("preconditionCompletionIdentifier");

        ActivityRecommendationRuleSet recommendationRule = new ActivityRecommendationRuleSet();
        ActivityCompletionPreconditionRuleSet completionRule = new ActivityCompletionPreconditionRuleSet();

        final ArrayList<ActivityRecommendationRule> rules = new ArrayList<>();
        final ActivityRecommendationRule activityRecommendationRule = new ActivityRecommendationRule();
        activityRecommendationRule.setActivityId(ActivityId);
        activityRecommendationRule.setActivityName(activityName);
        activityRecommendationRule.setActivityIcon(activityIcon);
        activityRecommendationRule.setActivityType(activityType);
        activityRecommendationRule.setFrequency(Integer.valueOf(frequency));
        activityRecommendationRule.setCompletionType(completionType);
        activityRecommendationRule.setIsSkipMilestone(Boolean.valueOf(isSkipMilestone));
        activityRecommendationRule.setMilestoneValue(Integer.valueOf(milestoneValue));
        rules.add(activityRecommendationRule);
        recommendationRule.setRules(rules);

        final ArrayList<ActivityCompletionRule> completionRules = new ArrayList<>();
        final ActivityCompletionRule activityCompletionRule = new ActivityCompletionRule();
        activityCompletionRule.setCompletionPreconditionType(preconditionCompletionPreconditionType);
        activityCompletionRule.setCompletionIdentifier(preconditionCompletionIdentifier);
        activityCompletionRule.setActivityId(preconditionActivityId);
        activityCompletionRule.setIteration(Integer.valueOf(preconditionIteration));
        activityCompletionRule.setMilestoneValue(Integer.valueOf(preconditionMilestoneValue));
        completionRules.add(activityCompletionRule);
        completionRule.setRules(completionRules);

        final JourneyCategory category = createJourneyCategory(
                categoryType,
                categoryCode,
                externalReference,
                journeyStartTime,
                activityPrecondition,
                enrollmentPrecondition,
                enrollmentStartTime,
                enrollmentEndTime,
                maxParticipants,
                monitoringPeriodDuration);

        final JourneyProgram journeyProgram = createJourneyProgram(
                "PROGRAM_" + category.getCategoryCode(),
                programTransitionRule,
                milestoneTransitionRule,
                recommendationRule,
                completionRule,
                duration);

        final JourneyProgramMilestoneRewardCustomization journeyProgramMilestoneRewardCustomization =
                createJourneyProgramMilestoneRewardCustomization(
                        journeyProgram,
                        JourneyRewardTypeEnum.valueOf(milestoneRewardType),
                        milestoneRewardExternalReference);

        final JourneyProgramRewardCustomization journeyProgramRewardCustomization =
                createJourneyProgramRewardCustomization(
                        journeyProgram,
                        JourneyRewardTypeEnum.valueOf(programRewardType),
                        programRewardExternalReference);

        final JourneyCategorization categorization = createCategorization(category, journeyProgram);

        currentCategory = category;
        currentProgram = journeyProgram;
        currentCategorization = categorization;
    }

    // Original private methods remain the same
    private JourneyProgramBehaviour createProgramBehaviour(
            Long duration,
            String programTransitionRule,
            String milestoneTransitionRule,
            ActivityRecommendationRuleSet recommendationRuleSet,
            ActivityCompletionPreconditionRuleSet completionRuleSet,
            JourneyProgram journeyProgram) {
        JourneyProgramBehaviour programBehaviour = new JourneyProgramBehaviour();
        programBehaviour.setJourneyProgram(journeyProgram);
        programBehaviour.setProgramDuration(duration);
        programBehaviour.setJourneyMilestone(getJourneyMilestone());

        final JourneyProgramBehaviour savedEntity = extendedJourneyProgramBehaviourRepository.save(programBehaviour);
        createAndSetRules(
                programTransitionRule, milestoneTransitionRule, recommendationRuleSet, completionRuleSet, savedEntity);

        return savedEntity;
    }

    public JourneyCategorization createCategorization(JourneyCategory category, JourneyProgram program) {
        final JourneyCategorization journeyCategorization = new JourneyCategorization();
        journeyCategorization.setId(new JourneyCategorizationId(
                program.getJourneyProgramId(),
                category.getJourneyCategoryId(),
                LocalDateTime.of(1970, 12, 31, 23, 59)));
        journeyCategorization.setJourneyCategory(category);
        journeyCategorization.setJourneyProgram(program);
        journeyCategorization.setStatus("ACTIVE");
        journeyCategorization.setEffTo(LocalDateTime.of(9999, 12, 31, 23, 59));
        return extendedJourneyCategorizationRepository.save(journeyCategorization);
    }

    public JourneyProgramRewardCustomization createJourneyProgramRewardCustomization(
            JourneyProgram journeyProgram, JourneyRewardTypeEnum rewardTypeEnum, String externalReference) {
        final JourneyProgramRewardCustomization entity = new JourneyProgramRewardCustomization();
        entity.setJourneyCustomerDefinition(getCustomerDefinition());
        entity.setJourneyProgram(journeyProgram);
        entity.setRewardType(rewardTypeEnum.name());
        entity.setRewardValue("" + (Math.random() * 10000));
        entity.setEffFrom(LocalDateTime.of(1970, 12, 31, 23, 59));
        entity.setEffTo(LocalDateTime.of(9999, 12, 31, 23, 59));
        entity.setExtRewardRef(externalReference);

        return extendedJourneyProgramRewardCustomizationRepository.save(entity);
    }

    public JourneyProgramMilestoneRewardCustomization createJourneyProgramMilestoneRewardCustomization(
            JourneyProgram journeyProgram, JourneyRewardTypeEnum rewardTypeEnum, String externalReference) {
        final JourneyProgramMilestoneRewardCustomization entity = new JourneyProgramMilestoneRewardCustomization();
        entity.setJourneyCustomerDefinition(getCustomerDefinition());
        entity.setJourneyProgram(journeyProgram);
        entity.setRewardType(rewardTypeEnum.name());
        entity.setRewardValue("" + (Math.random() * 10000));
        entity.setEffFrom(LocalDateTime.of(1970, 12, 31, 23, 59));
        entity.setEffTo(LocalDateTime.of(9999, 12, 31, 23, 59));
        entity.setExtRewardRef(externalReference);
        entity.setJourneyProgramMilestone(getMilestone());

        return extendedJourneyProgramMilestoneRewardCustomizationRepository.save(entity);
    }

    private @NotNull JourneyProgramMilestone getMilestone() {
        final JourneyMilestone journeyMilestone = getJourneyMilestone();

        return extendedJourneyProgramMilestoneRepository
                .findByJourneyProgramVersionAndMilestoneRange(journeyMilestone, 0, 99)
                .orElseGet(() -> extendedJourneyProgramMilestoneRepository.save(
                        new JourneyProgramMilestone(journeyMilestone, 0, 99)));
    }

    private @NotNull JourneyMilestone getJourneyMilestone() {
        return extendedJourneyMilestoneRepository
                .findByName("WEEKS")
                .orElseGet(() -> extendedJourneyMilestoneRepository.save(new JourneyMilestone("WEEKS")));
    }

    private @NotNull JourneyCustomerDefinition getCustomerDefinition() {
        final Optional<JourneyCustomerDefinition> customerDefinitionByAllianceAndGroupIdAndBranch =
                extendedJourneyCustomerDefinitionRepository.findCustomerDefinitionByAllianceAndGroupIdAndBranch(
                        "*", "*", "*");
        return customerDefinitionByAllianceAndGroupIdAndBranch.orElseGet(
                () -> extendedJourneyCustomerDefinitionRepository.save(
                        new JourneyCustomerDefinition("*", "*", "*", new HashSet<>(), new HashSet<>())));
    }

    public JourneyProgram createJourneyProgram(
            String programNme,
            String programTransitionRule,
            String milestoneTransitionRule,
            ActivityRecommendationRuleSet recommendationRuleSet,
            ActivityCompletionPreconditionRuleSet completionRuleSet,
            Long duration) {
        final JourneyProgram journeyProgram = new JourneyProgram();
        journeyProgram.setName(programNme);
        journeyProgram.setStatus("ACTIVE");
        journeyProgram.setEffFrom(LocalDateTime.of(1970, 12, 31, 23, 59));
        journeyProgram.setEffTo(LocalDateTime.of(9999, 12, 31, 23, 59));
        final JourneyProgram savedEntity = extendedJourneyProgramRepository.save(journeyProgram);
        journeyProgram.setJourneyProgramBehaviour(createProgramBehaviour(
                duration,
                programTransitionRule,
                milestoneTransitionRule,
                recommendationRuleSet,
                completionRuleSet,
                journeyProgram));
        return savedEntity;
    }

    public JourneyCategory createJourneyCategory(
            String journeyTypeName,
            String journeyCategoryCode,
            String externalReference,
            String journeyStartTime,
            String activityPrecondition,
            String enrollmentPrecondition,
            String enrollmentStartTime,
            String enrollmentEndTime,
            Integer maxParticipants,
            Integer monitoringPeriodDuration) {
        final JourneyCategoryType journeyType = createJourneyType(journeyTypeName);

        final JourneyCategory journeyCategory = new JourneyCategory();
        journeyCategory.setJourneyCategoryType(journeyType);
        final CategoryConfigRuleSet ruleSet = getCategoryConfigRuleSet(
                journeyStartTime,
                activityPrecondition,
                enrollmentPrecondition,
                enrollmentStartTime,
                enrollmentEndTime,
                maxParticipants,
                monitoringPeriodDuration);

        final String name = "JOURNEY_CATEGORY_NAME" + journeyCategoryCode;
        journeyCategory.setJourneyRules(createJourneyRule(name, ruleSet));
        journeyCategory.setName(name);
        journeyCategory.setCategoryCode(journeyCategoryCode);
        journeyCategory.setExternalReference(externalReference);

        return extendedJourneyCategoryRepository.save(journeyCategory);
    }

    private static CategoryConfigRuleSet getCategoryConfigRuleSet(
            final String journeyStartTime,
            final String activityPrecondition,
            final String enrollmentPrecondition,
            final String enrollmentStartTime,
            final String enrollmentEndTime,
            final Integer maxParticipants,
            final Integer monitoringPeriodDuration) {
        final CategoryConfigRuleSet ruleSet = new CategoryConfigRuleSet();
        final ArrayList<CategoryConfigRule> rules = new ArrayList<>();
        final CategoryConfigRule categoryConfigRule = new CategoryConfigRule();
        categoryConfigRule.setJourneyStartTime(journeyStartTime);
        categoryConfigRule.setActivityPrecondition(activityPrecondition);
        categoryConfigRule.setEnrollmentPrecondition(enrollmentPrecondition);
        categoryConfigRule.setEnrollmentStartTime(enrollmentStartTime);
        categoryConfigRule.setEnrollmentEndTime(enrollmentEndTime);
        categoryConfigRule.setMaxParticipants(maxParticipants);
        categoryConfigRule.setMonitoringPeriodDuration(monitoringPeriodDuration);
        rules.add(categoryConfigRule);
        ruleSet.setRules(rules);
        return ruleSet;
    }

    public JourneyCategoryType createJourneyType(String journeyTypeName) {
        final JourneyCategoryType entity = new JourneyCategoryType();
        entity.setName(journeyTypeName);
        entity.setProgressType("DEFAULT");
        return extendedJourneyCategoryTypeRepository.save(entity);
    }

    private JourneyRules createJourneyRule(final String ruleName, final CategoryConfigRuleSet ruleSet) {
        JourneyRules rule = new JourneyRules();
        rule.setRuleSetName(ruleName);
        rule.setRuleSetType(RuleProcessorType.DMN.name());
        final byte[] dmn =
                transformer.dmnTableToXml(ruleSet.toDmnDecisionTable()).getBytes(StandardCharsets.UTF_8);
        rule.setRuleSet(dmn);

        return extendedJourneyRulesRepository.save(rule);
    }

    private void createAndSetRules(
            String programTransitionRule,
            String milestoneTransitionRule,
            ActivityRecommendationRuleSet recommendationRuleSet,
            ActivityCompletionPreconditionRuleSet completionRuleSet,
            JourneyProgramBehaviour programBehaviour) {
        // Create and set program completion rule
        JourneyRules programCompletionRule = createJourneyRule(
                programBehaviour.getJourneyProgram().getName() + " Program Completion Rule",
                programTransitionRule,
                RuleProcessorType.JEXL);

        programCompletionRule
                .getJourneyProgramBehavioursForProgramCompletionRulesId()
                .add(programBehaviour);
        programBehaviour.setJourneyRulesByProgramCompletionRulesId(programCompletionRule);
        extendedJourneyRulesRepository.save(programCompletionRule);

        // Create and set milestone transition rule
        JourneyRules milestoneRule = createJourneyRule(
                programBehaviour.getJourneyProgram().getName() + " Milestone Transition Rule",
                milestoneTransitionRule,
                RuleProcessorType.JEXL);
        milestoneRule.getJourneyProgramBehavioursForMilestoneTransitionRulesId().add(programBehaviour);
        programBehaviour.setJourneyRulesByMilestoneTransitionRulesId(milestoneRule);
        extendedJourneyRulesRepository.save(milestoneRule);

        // Create and set activity recommendation rule
        JourneyRules activityRule = createJourneyRule(
                programBehaviour.getJourneyProgram().getName() + " Activity Recommendation Rule",
                transformer.dmnTableToXml(recommendationRuleSet.toDmnDecisionTable()),
                RuleProcessorType.DMN);
        activityRule
                .getJourneyProgramBehavioursForProgramActivityRecommendationRulesId()
                .add(programBehaviour);
        programBehaviour.setJourneyRulesByProgramActivityRecommendationRulesId(activityRule);
        extendedJourneyRulesRepository.save(activityRule);

        JourneyRules completionRule = createJourneyRule(
                programBehaviour.getJourneyProgram().getName() + " Activity Completion Precondition Rule",
                transformer.dmnTableToXml(completionRuleSet.toDmnDecisionTable()),
                RuleProcessorType.DMN);
        completionRule
                .getJourneyProgramBehavioursForProgramActivityCompletionRulesId()
                .add(programBehaviour);
        programBehaviour.setJourneyRulesByProgramActivityCompletionRulesId(completionRule);
        extendedJourneyRulesRepository.save(completionRule);
    }

    private JourneyRules createJourneyRule(String ruleName, String ruleString, RuleProcessorType ruleProcessorType) {
        JourneyRules rule = new JourneyRules();
        rule.setRuleSetName(ruleName);
        rule.setRuleSetType(ruleProcessorType.name());
        rule.setRuleSet(ruleString.getBytes(StandardCharsets.UTF_8));
        return extendedJourneyRulesRepository.save(rule);
    }
}
