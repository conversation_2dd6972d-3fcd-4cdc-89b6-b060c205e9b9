package za.co.discovery.health.journey;

import io.cucumber.java.After;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import za.co.discovery.health.journey.util.CleanupDbService;
import za.co.discovery.health.journey.util.EventHelper;

@Slf4j
@RequiredArgsConstructor
public class DatabaseCleanupHook {
    private static final String H2_SCHEMA_NAME = "JOURNEY";
    private static final String SHEDLOCK = "SHEDLOCK";

    private final CleanupDbService cleanupDbService;
    private final EventHelper eventHelper;

    @After
    public void cleanupAfterScenario() {
        log.info("Cleaning up database after scenario");
        cleanupDbService.resetDB(H2_SCHEMA_NAME, SHEDLOCK);
        eventHelper.clean();
    }
}
