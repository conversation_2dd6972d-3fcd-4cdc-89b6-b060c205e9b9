package za.co.discovery.health.journey.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.service.bo.dmn.DmnJsonTransformer;

/**
 * Bidirectional transformer between JSON decision rules and DMN XML format
 */
@Component
@RequiredArgsConstructor
public class DmnJsonTransformerTest {

    // Example usage and test methods
    public static void main(String[] args) {
        DmnJsonTransformer transformer = new DmnJsonTransformer(new ObjectMapper());

        try {
            // Test with your actual DMN XML
            String dmnXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                    + "<definitions xmlns=\"https://www.omg.org/spec/DMN/20191111/MODEL/\" xmlns:modeler=\"http://camunda.org/schema/modeler/1.0\" id=\"Definitions_0lm3k5d\" name=\"DRD\" namespace=\"http://camunda.org/schema/1.0/dmn\" exporter=\"Camunda Modeler\" exporterVersion=\"5.31.0\" modeler:executionPlatform=\"Camunda Cloud\" modeler:executionPlatformVersion=\"8.6.0\">\n"
                    + "  <decision id=\"decision\" name=\"decision\">\n"
                    + "    <decisionTable id=\"DecisionTable_11p63ca\" hitPolicy=\"COLLECT\">\n"
                    + "      <input id=\"Input_1\" label=\"alliance\">\n"
                    + "        <inputExpression id=\"InputExpression_1\" typeRef=\"string\">\n"
                    + "          <text>alliance</text>\n"
                    + "        </inputExpression>\n"
                    + "      </input>\n"
                    + "      <input id=\"InputClause_0g517zt\" label=\"group\">\n"
                    + "        <inputExpression id=\"LiteralExpression_1qqmmdv\" typeRef=\"string\">\n"
                    + "          <text>group</text>\n"
                    + "        </inputExpression>\n"
                    + "      </input>\n"
                    + "      <input id=\"InputClause_07o5aau\" label=\"branch\">\n"
                    + "        <inputExpression id=\"LiteralExpression_0gkaneq\" typeRef=\"string\">\n"
                    + "          <text></text>\n"
                    + "        </inputExpression>\n"
                    + "      </input>\n"
                    + "      <output id=\"Output_1\" label=\"journeyCategory\" name=\"journeyCategory\" typeRef=\"string\" />\n"
                    + "      <output id=\"OutputClause_051r7yr\" label=\"Enrollment Start Time\" name=\"journeyEnrollmentStartTime\" typeRef=\"string\" />\n"
                    + "      <output id=\"OutputClause_16xb9ws\" label=\"Enrollment End Time\" name=\"journeyEnrollmentEndTime\" typeRef=\"string\" />\n"
                    + "      <output id=\"OutputClause_0fa2l9e\" label=\"Journey Start Time\" name=\"journeyStartTime\" typeRef=\"string\" />\n"
                    + "      <output id=\"OutputClause_0nk5wi3\" label=\"Activity Precondition\" name=\"journeyActivityPrecondition\" typeRef=\"string\" />\n"
                    + "      <output id=\"OutputClause_1ennek9\" label=\"Enrollment Precondition\" name=\"journeyEnrollmentPrecondition\" typeRef=\"string\" />\n"
                    + "      <rule id=\"DecisionRule_15c4ms4\">\n"
                    + "        <inputEntry id=\"UnaryTests_1e2p8gt\">\n"
                    + "          <text></text>\n"
                    + "        </inputEntry>\n"
                    + "        <inputEntry id=\"UnaryTests_1ce0iux\">\n"
                    + "          <text></text>\n"
                    + "        </inputEntry>\n"
                    + "        <inputEntry id=\"UnaryTests_12vu43b\">\n"
                    + "          <text></text>\n"
                    + "        </inputEntry>\n"
                    + "        <outputEntry id=\"LiteralExpression_0umd4f9\">\n"
                    + "          <text>\"Navigating stress and burnout\"</text>\n"
                    + "        </outputEntry>\n"
                    + "        <outputEntry id=\"LiteralExpression_0ucavtt\">\n"
                    + "          <text>\"2025-05-05T14:30:00\"</text>\n"
                    + "        </outputEntry>\n"
                    + "        <outputEntry id=\"LiteralExpression_0yipuqs\">\n"
                    + "          <text>\"2025-05-15T14:30:00\"</text>\n"
                    + "        </outputEntry>\n"
                    + "        <outputEntry id=\"LiteralExpression_0q5r6fx\">\n"
                    + "          <text>\"2025-05-19T00:00:01\"</text>\n"
                    + "        </outputEntry>\n"
                    + "        <outputEntry id=\"LiteralExpression_11g0o24\">\n"
                    + "          <text>\"HRAC\"</text>\n"
                    + "        </outputEntry>\n"
                    + "        <outputEntry id=\"LiteralExpression_0egb4vx\">\n"
                    + "          <text>\"\\\"Y\\\".equals(HRAC ?: \\\"N\\\")\"</text>\n"
                    + "        </outputEntry>\n"
                    + "      </rule>\n"
                    + "    </decisionTable>\n"
                    + "  </decision>\n"
                    + "</definitions>";

            System.out.println("=== Testing with your DMN XML ===");
            String jsonFromYourDmn = transformer.dmnXmlToJson(dmnXml);
            System.out.println("JSON from your DMN:");
            System.out.println(jsonFromYourDmn);

            System.out.println("\n=== Converting back to DMN XML ===");
            String dmnXmlGenerated = transformer.jsonToDmnXml(jsonFromYourDmn);
            System.out.println("Generated DMN XML:");
            System.out.println(dmnXmlGenerated);

            // Example JSON input
            String jsonInput = "    {"
                    + "        \"decisionId\": \"decision_1\",\n"
                    + "        \"decisionName\": \"Journey Activation Rule\",\n"
                    + "        \"hitPolicy\": \"FIRST\",\n"
                    + "        \"aggregation\": null,\n"
                    + "        \"rules\": [\n"
                    + "            {\n"
                    + "             \"inputs\": {\n"
                    + "                 \"alliance\": null,\n"
                    + "                 \"branch\": null,\n"
                    + "                 \"group\": null\n"
                    + "             },\n"
                    + "             \"outputs\": {\n"
                    + "                 \"enrollmentStartTime\": null,\n"
                    + "                 \"enrollmentEndTime\": \"2025-05-05T14:30:00\",\n"
                    + "                 \"journeyStartTime\": \"2025-05-05T14:30:00\",\n"
                    + "                 \"enrollmentPrecondition\": \"HRAC\",\n"
                    + "                 \"activityPrecondition\": \"'Y'.equals(HRAC ?: 'N')\",\n"
                    + "                 \"maxParticipants\": 5\n"
                    + "             }\n"
                    + "            }\n"
                    + "        ]\n"
                    + "    }\n";

            System.out.println("\n=== JSON to DMN XML ===");
            String dmnXmlFromJson = transformer.jsonToDmnXml(jsonInput);
            System.out.println(dmnXmlFromJson);

            System.out.println("\n=== DMN XML back to JSON ===");
            String jsonOutput = transformer.dmnXmlToJson(dmnXmlFromJson);
            System.out.println(jsonOutput);

        } catch (Exception e) {
            System.err.println("Error during transformation: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
