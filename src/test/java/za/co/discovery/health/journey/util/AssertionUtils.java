// package za.co.discovery.health.journey.util;
//
// import lombok.extern.slf4j.Slf4j;
// import org.junit.jupiter.api.Assertions;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Component;
// import org.springframework.transaction.annotation.Transactional;
// import za.co.discovery.health.pathfinder.constant.ProgramStatus;
// import za.co.discovery.health.pathfinder.database.databaseMapping.ActivityTransaction;
// import za.co.discovery.health.pathfinder.database.databaseMapping.PacProgram;
// import za.co.discovery.health.pathfinder.database.databaseMapping.PacProgramEnrolment;
// import za.co.discovery.health.pathfinder.database.databaseMapping.PathwayPersonalProgram;
// import za.co.discovery.health.pathfinder.database.databaseMapping.PathwayPersonalProgramDetail;
// import za.co.discovery.health.pathfinder.database.databaseMapping.PathwayTransitionRule;
// import za.co.discovery.health.pathfinder.database.databaseMapping.PersonalProgramGroup;
// import
// za.co.discovery.health.pathfinder.database.repository.ExtendedPathwayPersonalProgramRepository;
// import
// za.co.discovery.health.pathfinder.database.repository.ExtendedPathwayTransitionRulesRepository;
// import
// za.co.discovery.health.pathfinder.database.repository.ExtendedPersonalProgramGroupRepository;
// import za.co.discovery.health.pathfinder.util.model.Audience;
//
// import java.time.LocalDateTime;
// import java.util.List;
// import java.util.Objects;
// import java.util.Optional;
// import java.util.Set;
// import java.util.stream.Collectors;
//
/// **
// * class for writing assertions
// */
// @Slf4j
// @Component
// public class AssertionUtils {
//
//    @Autowired
//    private ExtendedPathwayPersonalProgramRepository pathwayPersonalProgramRepository;
//
//    @Autowired
//    private ExtendedPersonalProgramGroupRepository personalProgramGroupRepository;
//
//    @Autowired
//    private ExtendedPathwayTransitionRulesRepository rulesRepository;
//
//    @Autowired
//    private EventHelper eventHelper;
//
//    @Transactional(readOnly = true)
//    public void assertEnrollment(final PacProgramEnrolment pacProgramEnrolment) {
//        final Optional<PersonalProgramGroup> personalProgramGroup =
// personalProgramGroupRepository.findActiveBy(
//                pacProgramEnrolment.getPacProgram().getPacProgramGroup(), TestConstants.ENTITY_NO,
// LocalDateTime.now());
//
//        Assertions.assertTrue(personalProgramGroup.isPresent());
//
//        Assertions.assertTrue(pathwayPersonalProgramRepository
//                .findAlreadyExistingActiveProgram(pacProgramEnrolment, personalProgramGroup.get())
//                .isPresent());
//
//        long sizeOfActivePrograms =
// personalProgramGroup.get().getPathwayPersonalPrograms().stream()
//                .filter(it -> it.getProgramStatus().equals(ProgramStatus.ACTIVE.getStatus()))
//                .count();
//
//        Assertions.assertEquals(1, sizeOfActivePrograms);
//        log.info("assertEnrollment was successful");
//    }
//
//    @Transactional(readOnly = true)
//    public void assertActivityCompletion(
//            final PacProgramEnrolment pacProgramEnrolment, final List<ActivityTransaction>
// activityTransaction) {
//        PersonalProgramGroup personalProgramGroup = personalProgramGroupRepository
//                .findActiveBy(
//                        pacProgramEnrolment.getPacProgram().getPacProgramGroup(),
//                        TestConstants.ENTITY_NO,
//                        LocalDateTime.now())
//                .orElseThrow(() -> new RuntimeException(
//                        "No active group found by
// assertActivityCompletion.personalProgramGroupRepository\n"
//                                + "                .findActiveBy "));
//
//        PathwayPersonalProgram alreadyExistingActiveProgram = pathwayPersonalProgramRepository
//                .findAlreadyExistingActiveProgram(pacProgramEnrolment, personalProgramGroup)
//                .orElseThrow(() -> new RuntimeException(
//                        "No active program found by
// assertActivityCompletion.pathwayPersonalProgramRepository\n"
//                                + "                .findAlreadyExistingActiveProgram"));
//
//        Set<PathwayPersonalProgramDetail> pathwayPersonalProgramDetails =
//                alreadyExistingActiveProgram.getPathwayPersonalProgramDetails();
//
//        Assertions.assertEquals(activityTransaction.size(), pathwayPersonalProgramDetails.size());
//
//        List<Long> activityTransactionIds =
//
// activityTransaction.stream().map(ActivityTransaction::getId).collect(Collectors.toList());
//
//        Assertions.assertTrue(pathwayPersonalProgramDetails.stream()
//                .allMatch(it -> activityTransactionIds.contains(
//                        it.getActivityTransaction().getId())));
//
//        log.info("assertActivityCompletion was successful");
//    }
//
//    public void assertRulesTable(
//            List<String> activityNames, PacProgramEnrolment pacProgramEnrolment, Audience
// audience) {
//        PacProgram pacProgram = pacProgramEnrolment.getPacProgram();
//        final List<PathwayTransitionRule> rules =
//
// rulesRepository.findByPacProgramGroupAndPacProgram(pacProgram.getPacProgramGroup(), pacProgram);
//        //        Assertions.assertTrue(rule.isPresent());
//        activityNames.forEach(it -> Assertions.assertTrue(
//                rules.stream().anyMatch(rule -> rule.getVariableFunction().contains(it))));
//        //        Assertions.assertTrue(rules.getPathwayTransitionRuleAudiences().stream()
//        //                .anyMatch(it -> it.getAllianceId() == audience.getAllianceNo()
//        //                        && Objects.equals(it.getBranchId(), audience.getBranchNo())
//        //                        && Objects.equals(it.getGroupId(), audience.getGroupNo())));
//    }
//
//    public void assertProgramCompletionEventSent(PacProgramEnrolment pacProgramEnrolment) {
//        final PacProgram pacProgram = pacProgramEnrolment.getPacProgram();
//        AwaitilityHelper.minWait(() ->
// Assertions.assertTrue(eventHelper.PERSONAL_PROGRAM_COMPLETE_EVENT_LIST.stream()
//                .anyMatch(event -> Objects.equals(pacProgram.getId(), event.getPacProgramId())
//                        && Objects.equals(pacProgram.getName(), event.getPacProgramName())
//                        && Objects.equals(pacProgram.getPacProgramGroup().getDescr(),
// event.getPacProgramGroupName())
//                        && Objects.equals(pacProgramEnrolment.getId(),
// event.getPacProgramEnrollmentId())
//                        && Objects.equals(pacProgramEnrolment.getEntityId(),
// event.getEntityId()))));
//    }
//
//    public void assertProgramCompletionEventSent(final Long count) {
//        AwaitilityHelper.minWait(
//                () -> Assertions.assertEquals(count,
// eventHelper.PERSONAL_PROGRAM_COMPLETE_EVENT_LIST.size()));
//    }
// }
