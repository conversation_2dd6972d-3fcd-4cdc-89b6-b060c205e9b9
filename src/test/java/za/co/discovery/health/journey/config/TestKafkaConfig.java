package za.co.discovery.health.journey.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.utils.KafkaTestUtils;
import za.co.discovery.health.journey.util.ProducerCreator;

import java.util.Map;

@Configuration
public class TestKafkaConfig {

    @Autowired
    private EmbeddedKafkaBroker embeddedKafkaBroker;

    @Bean
    public <T> DefaultKafkaProducerFactory<String, T> producerFactory() {
        final Map<String, Object> producerProps = KafkaTestUtils.producerProps(embeddedKafkaBroker);
        producerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        return new DefaultKafkaProducerFactory<>(producerProps);
    }

    @Bean
    public ProducerCreator<String, String> producerFactoryMethod() {
        return () -> {
            final DefaultKafkaProducerFactory<String, String> factory = producerFactory();
            return factory.createProducer();
        };
    }
}
