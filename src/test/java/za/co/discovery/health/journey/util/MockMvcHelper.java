package za.co.discovery.health.journey.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.multipart;

@Component
@Slf4j
public class MockMvcHelper {

    @Autowired
    private ObjectMapper objectMapper;

    @SneakyThrows
    public MvcResult post(final MockMvc mockMvc, final String url, final Object obj) {
        return mockMvc.perform(MockMvcRequestBuilders.post(url)
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(objectMapper.writeValueAsString(obj)))
                .andReturn();
    }

    @SneakyThrows
    public MvcResult post(
            final MockMvc mockMvc,
            final String url,
            final Object obj,
            final MultiValueMap<String, String> queryParams) {

        return mockMvc.perform(MockMvcRequestBuilders.post(url)
                        .queryParams(queryParams)
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(objectMapper.writeValueAsString(obj)))
                .andReturn();
    }

    @SneakyThrows
    public ResultActions postMultipart(
            final MockMvc mockMvc, final String url, final MockMultipartFile mockMultipartFile) {
        return mockMvc.perform(multipart(url).file(mockMultipartFile));
    }

    @SneakyThrows
    public MvcResult post(final MockMvc mockMvc, final String url, final byte[] fileAsBytes) {
        return mockMvc.perform(MockMvcRequestBuilders.post(url)
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(fileAsBytes))
                .andReturn();
    }

    @SneakyThrows
    public MvcResult put(final MockMvc mockMvc, final String url, final Object obj) {
        return mockMvc.perform(MockMvcRequestBuilders.put(url)
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(objectMapper.writeValueAsString(obj)))
                .andReturn();
    }

    @SneakyThrows
    public MvcResult get(final MockMvc mockMvc, final String url) {
        return get(mockMvc, url, new LinkedMultiValueMap<>());
    }

    @SneakyThrows
    public MvcResult get(final MockMvc mockMvc, final String url, final MultiValueMap<String, String> queryParams) {
        log.info("get request started");
        return mockMvc.perform(MockMvcRequestBuilders.get(url)
                        .queryParams(queryParams)
                        .contentType(MediaType.APPLICATION_JSON_VALUE))
                .andReturn();
    }

    @SneakyThrows
    public MvcResult delete(final MockMvc mockMvc, final String url) {
        return mockMvc.perform(MockMvcRequestBuilders.delete(url).contentType(MediaType.APPLICATION_JSON_VALUE))
                .andReturn();
    }

    @SneakyThrows
    public <T> T getResult(final MvcResult mvcResult, final Class<T> clazz) {
        final String contentAsString = mvcResult.getResponse().getContentAsString();
        return objectMapper.readValue(contentAsString, clazz);
    }

    @SneakyThrows
    public <T> T getResult(final MvcResult mvcResult, final TypeReference<T> clazz) {
        final String contentAsString = mvcResult.getResponse().getContentAsString();
        return objectMapper.readValue(contentAsString, clazz);
    }

    @SneakyThrows
    public <T> List<T> getResultFromPage(final MvcResult mvcResult, final Class<T> clazz) {
        final Map<String, Object> responseMap =
                objectMapper.readValue(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {});
        final String responseContentStringValue = objectMapper.writeValueAsString(responseMap.get("content"));
        final CollectionType typedReference = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        return objectMapper.readValue(responseContentStringValue, typedReference);
    }

    public Map<String, String> getMapFromFields(final Object object) {
        if (object == null) {
            return null;
        }

        return Arrays.stream(object.getClass().getDeclaredFields())
                .filter(field -> {
                    field.setAccessible(true);
                    try {
                        return field.get(object) != null;
                    } catch (IllegalAccessException e) {
                        return false;
                    }
                })
                .collect(Collectors.toMap(Field::getName, a -> {
                    try {
                        a.setAccessible(true);
                        final Object o = a.get(object);
                        return String.valueOf(o);
                    } catch (final IllegalAccessException e) {
                        throw new RuntimeException(String.format(
                                "Error occurred while extracting %s class into MultiValueMap",
                                object.getClass().getName()));
                    }
                }));
    }
}
