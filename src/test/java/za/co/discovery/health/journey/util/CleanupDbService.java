package za.co.discovery.health.journey.util;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import javax.transaction.Transactional;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Arrays;

@Component
@Slf4j
@RequiredArgsConstructor
public class CleanupDbService {
    public static final String H2_DB_PRODUCT_NAME = "H2";
    public static final String POSTGRES_DB_PRODUCT_NAME = "PostgreSQL";
    private final DataSource dataSource;

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @SneakyThrows
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public void resetDB(String... schemaName) {
        Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .locations("classpath:/database/db/migration")
                .load();
        flyway.clean();
        Arrays.stream(schemaName).forEach(this::dropSchema);
        flyway.migrate();
    }

    public void clearHibernateCaches() {
        entityManagerFactory.getCache().unwrap(org.hibernate.Cache.class).evictAllRegions();
    }

    @SneakyThrows
    private void dropSchema(final String schemaName) {
        try (Connection connection = dataSource.getConnection();
                Statement statement = connection.createStatement()) {
            if (isSupportedDatabase(connection)) {
                log.info(
                        "Dropping schema {} on database {}",
                        schemaName,
                        connection.getMetaData().getDatabaseProductName());
                executeStatement(statement, "DROP SCHEMA IF EXISTS " + schemaName + " CASCADE");
                clearHibernateCaches();
            } else {
                log.warn("Skipping dropping database schema, because it's not a supported database type");
            }
        }
    }

    @SneakyThrows
    private boolean isSupportedDatabase(final Connection connection) {
        final String databaseProductName = connection.getMetaData().getDatabaseProductName();
        log.info("Checking database type: {}", databaseProductName);
        return H2_DB_PRODUCT_NAME.equals(databaseProductName) || POSTGRES_DB_PRODUCT_NAME.equals(databaseProductName);
    }

    @SneakyThrows
    private void executeStatement(final Statement statement, final String sql) {
        statement.executeUpdate(sql);
        log.debug("Executed SQL: {}", sql);
    }
}
