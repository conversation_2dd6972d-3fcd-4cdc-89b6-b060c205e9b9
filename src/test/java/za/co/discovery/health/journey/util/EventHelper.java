package za.co.discovery.health.journey.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * class for writing test consumers (@KafkaListener), where we will add events to list and then clean
 */
@Slf4j
@Component
public class EventHelper {

    public final List<String> PERSONAL_PROGRAM_COMPLETE_EVENT_LIST = new ArrayList<>();

    //
    //    @Autowired
    //    private ObjectMapper objectMapper;
    //
    //    @KafkaListener(
    //            topics = KafkaTopics.PERSONAL_PROGRAM_COMPLETE_EVENT,
    //            groupId = "${spring.kafka.consumer.group-id}",
    //            containerFactory = "defaultContainerFactory")
    //    public void handle(final String message) throws JsonProcessingException {
    //        log.info("Received PersonalProgramCompleteEvent {}", message);
    //        final PersonalProgramCompleteEvent event = objectMapper.readValue(message,
    // PersonalProgramCompleteEvent.class);
    //        PERSONAL_PROGRAM_COMPLETE_EVENT_LIST.add(event);
    //    }

    public void clean() {
        PERSONAL_PROGRAM_COMPLETE_EVENT_LIST.clear();
    }
}
