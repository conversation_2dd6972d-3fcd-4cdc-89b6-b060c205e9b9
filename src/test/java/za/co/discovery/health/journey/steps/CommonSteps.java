package za.co.discovery.health.journey.steps;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.cucumber.java.DefaultDataTableCellTransformer;
import io.cucumber.java.DefaultDataTableEntryTransformer;
import io.cucumber.java.DefaultParameterTransformer;
import io.cucumber.java.ParameterType;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import za.co.discovery.health.hs.resourceserver.dto.LoggedInUser;
import za.co.discovery.health.hs.resourceserver.service.LoggedInUserService;
import za.co.discovery.health.journey.SpringIntegrationTest;
import za.co.discovery.health.journey.util.EventHelper;
import za.co.discovery.health.journey.util.TestConstants;
import za.co.discovery.health.journey.world.JourneyWorld;

import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Slf4j
public class CommonSteps extends SpringIntegrationTest {

    @Autowired
    EventHelper eventHelper;

    @Autowired
    WebApplicationContext webApplicationContext;

    @Autowired
    private JourneyWorld world;

    @Autowired
    private LoggedInUserService loggedInUserService;

    private final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    //    @Autowired
    //    private RuleCacheService ruleCacheService;

    //    @Given("dummy file is imported")
    //    public void runDummyImport() {
    //        try {
    //            // Load the spreadsheet from the resources directory
    //            final ClassPathResource resource = new ClassPathResource("test_import.xlsx");
    //            final File excelFile = resource.getFile();
    //
    //            // Import the Excel file
    //            excelImportService.importExcel(excelFile);
    //            ruleCacheService.init();
    //        } catch (Exception e) {
    //            // Log an error if the file is missing or something goes wrong
    //            log.error("Failed to import Excel file: {}", e.getMessage(), e);
    //        }
    //    }

    // support generic cucumber table mappings
    @DefaultParameterTransformer
    @DefaultDataTableEntryTransformer
    @DefaultDataTableCellTransformer
    public Object defaultTransformer(final Object fromValue, final Type toValueType) {
        final JavaType javaType = objectMapper.constructType(toValueType);
        return objectMapper.convertValue(fromValue, javaType);
    }

    @ParameterType(name = "boolean", value = "true|True|TRUE|false|False|FALSE")
    public Boolean booleanValue(String value) {
        return Boolean.valueOf(value);
    }

    @ParameterType(value = "has|does NOT have")
    public boolean has(String value) {
        return value.equals("has");
    }

    @ParameterType("\\d{4}\\-\\d{2}\\-\\d{2}")
    public LocalDate localDate(final String dateString) {
        return LocalDate.parse(dateString, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Given("the rest application has started up")
    public void setUp() {
        log.info("the rest application has started up");
        world.setMockMvc(
                MockMvcBuilders.webAppContextSetup(webApplicationContext).build());
    }

    @And("clean up events")
    public void cleanUpEvents() {
        log.info("clean up events");
        eventHelper.clean();
    }

    @And("set up authorized entity")
    public void setUpEntity() {
        final LoggedInUser loggedInUser = new LoggedInUser();
        loggedInUser.setUid(TestConstants.ENTITY_NO.toString());
        Mockito.when(loggedInUserService.assertLoggedInUser()).thenReturn(loggedInUser);
    }
}
