package za.co.discovery.health.journey.util;

import org.awaitility.core.ThrowingRunnable;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static org.awaitility.Awaitility.await;

/**
 * class for async awaits
 */
public class AwaitilityHelper {

    public static void wait(final Callable<Boolean> condition) {
        await().atMost(5, TimeUnit.SECONDS)
                .with()
                .pollDelay(0, TimeUnit.MILLISECONDS)
                .pollInterval(500, TimeUnit.MILLISECONDS)
                .until(condition);
    }

    public static void minWait(final ThrowingRunnable condition) {
        await().atMost(5, TimeUnit.SECONDS)
                .with()
                .pollDelay(1, TimeUnit.SECONDS)
                .pollInterval(500, TimeUnit.MILLISECONDS)
                .untilAsserted(condition);
    }

    public static void wait(final ThrowingRunnable condition) {
        await().atMost(5, TimeUnit.SECONDS)
                .with()
                .pollDelay(0, TimeUnit.MILLISECONDS)
                .pollInterval(500, TimeUnit.MILLISECONDS)
                .untilAsserted(condition);
    }
}
