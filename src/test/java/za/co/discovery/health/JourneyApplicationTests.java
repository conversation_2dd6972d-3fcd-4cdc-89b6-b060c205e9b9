package za.co.discovery.health;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.springframework.web.client.RestTemplate;
import za.co.discovery.health.journey.CleanupH2DatabaseTestListener;

@SpringBootApplication(scanBasePackages = {"za.co.discovery.health"})
@TestExecutionListeners(
        listeners = {DependencyInjectionTestExecutionListener.class, CleanupH2DatabaseTestListener.class})
@EnableCaching
@ConfigurationPropertiesScan
public class JourneyApplicationTests extends SpringBootServletInitializer {

    public static void main(final String[] args) {
        SpringApplication.run(JourneyApplicationTests.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(final SpringApplicationBuilder application) {
        return application.sources(JourneyApplicationTests.class);
    }

    @Bean("b2b-resttemplate")
    public RestTemplate testB2bRestTemplate() {
        return new RestTemplate();
    }
}
