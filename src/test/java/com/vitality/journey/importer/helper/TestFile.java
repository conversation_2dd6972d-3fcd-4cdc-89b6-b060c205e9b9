package com.vitality.journey.importer.helper;

import java.nio.file.Files;
import java.nio.file.Path;

public record TestFile(Path path) implements AutoCloseable {

    public static TestFile create(Path path) {
        return new TestFile(path);
    }

    @Override
    public void close() throws Exception {
        Files.deleteIfExists(path);
    }

    public String getName() {
        return path.getFileName().toString();
    }
}
