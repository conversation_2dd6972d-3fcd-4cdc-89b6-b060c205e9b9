package com.vitality.journey.importer.helper;

import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.service.csv.CsvWriter;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

@Component
@RequiredArgsConstructor
public class TestCsvHelper {

    private final CsvWriter csvWriter;
    private final DppDataGenerator dataGenerator;

    public InputStream getDppCsvInputStream(int numberOfUsers, int numberOfWeeks) {
        List<DppMemberRecord> memberRecords = dataGenerator.generate(numberOfUsers, numberOfWeeks);
        String csv = csvWriter.toCsv(memberRecords);
        return new ByteArrayInputStream(csv.getBytes());
    }

    public Path getDppCsvFile(int numberOfUsers, int numberOfWeeks) throws IOException {
        String csv = generateDppCsv(numberOfUsers, numberOfWeeks);
        return createCsvFile(csv);
    }

    public static Path createCsvFile(String csv) throws IOException {
        Path tempCsv = Files.createTempFile("dpp-import-", ".csv");
        Files.writeString(tempCsv, csv);
        return tempCsv;
    }

    public String generateDppCsv(int numberOfUsers, int numberOfWeeks) {
        List<DppMemberRecord> memberRecords = dataGenerator.generate(numberOfUsers, numberOfWeeks);
        return csvWriter.toCsv(memberRecords);
    }
}