package com.vitality.journey.importer.service.imprt;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.model.MemberMilestoneData;
import com.vitality.journey.importer.model.MemberRecord;
import com.vitality.journey.importer.model.ProgramMemberData;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.model.csv.DppMemberWeekData;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.csv.CsvReader;
import com.vitality.journey.importer.service.csv.CsvWriter;
import com.vitality.journey.importer.service.csv.ParsedCsvRow;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordReader;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;

@Slf4j
class StagingRecordReaderTest {

    private final DppDataGenerator dataGenerator = new DppDataGenerator();
    private final CsvWriter csvWriter = new CsvWriter();
    private final CsvReader csvReader = new CsvReader();

    @Test
    void parseRecord() throws IOException {
        List<DppMemberRecord> memberRecords = dataGenerator.generate(1, 52);
        String csv = csvWriter.toCsv(memberRecords);
        ParsedCsvRow parsedCsvRow = csvReader.toJsonStream(new ByteArrayInputStream(csv.getBytes())).findFirst().orElseThrow();


        DppMemberRecord dppMemberRecord = memberRecords.getFirst();
        String json = parsedCsvRow.json().toString();

        log.info("JSON: {}", json);

        StagingRecord stagingRecord = new StagingRecord();
        stagingRecord.setType(MediaType.APPLICATION_JSON.getSubtype());
        stagingRecord.setPayload(JdbcLobUtils.toClob(json));
        stagingRecord.setStatus(RecordStatus.LOADED.name());

        StagingRecordReader parser = StagingRecordReader.of(stagingRecord, 1L);

        MemberRecord memberRecord = parser.readMember();

        Assertions.assertNotNull(memberRecord);
        Assertions.assertEquals(dppMemberRecord.getUniqueId(), memberRecord.getUniqueId());
        Assertions.assertEquals(dppMemberRecord.getFirstName(), memberRecord.getFirstName());
        Assertions.assertEquals(dppMemberRecord.getLastName(), memberRecord.getLastName());
        Assertions.assertEquals(StringUtils.hasText(dppMemberRecord.getCdcIdentifier()) ? dppMemberRecord.getCdcIdentifier() : null, memberRecord.getCdcId());

        ProgramMemberData memberProgramData = parser.readMemberProgramData();
        Assertions.assertNotNull(memberProgramData);
        Assertions.assertEquals(memberRecord, memberProgramData.member());
        Assertions.assertEquals(dppMemberRecord.getStartingWeight(), memberProgramData.startingWeight());
        Assertions.assertEquals(dppMemberRecord.getTargetWeight(), memberProgramData.targetWeight());
        Assertions.assertEquals(dppMemberRecord.getClassDate(), memberProgramData.programStartDate());
        Assertions.assertEquals(dppMemberRecord.getProgramStartDate(), memberProgramData.memberStartDate());

        Assertions.assertNotNull(memberProgramData.iterations());
        List<DppMemberWeekData> weeks = dppMemberRecord.getWeeks();
        Assertions.assertEquals(weeks.size(), memberProgramData.iterations().size());

        DppMemberWeekData firstWeek = weeks.getFirst();
        MemberMilestoneData firstIterationData = memberProgramData.iterations().getFirst();
        Assertions.assertEquals(1, firstIterationData.getIteration());


        Assertions.assertEquals(firstWeek.getAttendanceMode(), firstIterationData.getAttendanceMode());
        Assertions.assertEquals(firstWeek.getSessionDate(), firstIterationData.getSessionDate());

        if (firstWeek.getWeight() != null) {
            Assertions.assertEquals(Double.valueOf(firstWeek.getWeight()), firstIterationData.getWeight());
        }
        Assertions.assertEquals(firstWeek.getActivityMinutes(), firstIterationData.getActivityMinutes());
    }
}