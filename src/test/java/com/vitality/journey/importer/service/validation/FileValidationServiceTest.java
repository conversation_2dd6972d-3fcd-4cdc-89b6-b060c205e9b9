package com.vitality.journey.importer.service.validation;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;

import static org.assertj.core.api.Assertions.assertThat;

class FileValidationServiceTest {

    private final FileValidationService fileValidationService = new FileValidationService();

    @Test
    void shouldRejectEmptyFile() {
        var result = fileValidationService.validateFile(null);
        
        assertThat(result.valid()).isFalse();
        assertThat(result.errors()).contains("File is required and cannot be empty");
    }

    @Test
    void shouldRejectInvalidFileExtension() {
        var file = new MockMultipartFile("file", "test.txt", "text/plain", "content".getBytes());
        
        var result = fileValidationService.validateFile(file);
        
        assertThat(result.valid()).isFalse();
        assertThat(result.errors()).contains("File must have .csv extension");
    }

    @Test
    void shouldAcceptValidCsvFile() {
        var file = new MockMultipartFile("file", "test.csv", "text/csv", "content".getBytes());
        
        var result = fileValidationService.validateFile(file);
        
        assertThat(result.valid()).isTrue();
        assertThat(result.errors()).isEmpty();
    }
}