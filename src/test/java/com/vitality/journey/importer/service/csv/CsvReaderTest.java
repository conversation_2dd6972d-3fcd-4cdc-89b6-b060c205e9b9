package com.vitality.journey.importer.service.csv;

import com.fasterxml.jackson.databind.JsonNode;
import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class CsvReaderTest {

    private final CsvWriter csvWriter = new CsvWriter();
    private final CsvReader csvReader = new CsvReader();
    private final DppDataGenerator dataGenerator = new DppDataGenerator();

    @Test
    void testAllValidRows() throws Exception {
        List<DppMemberRecord> memberRecords = dataGenerator.generate(1000, 26);
        String csv = csvWriter.toCsv(memberRecords);

        Stream<ParsedCsvRow> jsonStream = csvReader.toJsonStream(new ByteArrayInputStream(csv.getBytes()));

        List<ParsedCsvRow> csvRows = jsonStream.toList();

        assertNotNull(csvRows);
        assertFalse(csvRows.isEmpty());

        for (ParsedCsvRow csvRow : csvRows) {
            assertTrue(csvRow.isSuccess());

            JsonNode json = csvRow.json();
            assertNotNull(json);

            assertNotNull(json.get(DppCsvHeader.UNIQUE_ID));
            assertNotNull(json.get(DppCsvHeader.FIRST_NAME));
            assertNotNull(json.get(DppCsvHeader.LAST_NAME));
            assertNotNull(json.get(DppCsvHeader.CLASS));
            assertNotNull(json.get(DppCsvHeader.CLASS_DATE));
            assertNotNull(json.get(DppCsvHeader.STARTING_WEIGHT));
            assertNotNull(json.get(DppCsvHeader.TARGET_WEIGHT));
        }
    }

    @Test
    void testInvalidRow() throws IOException {
        int numberOfWeeks = 26;

        String className = "Test Class " + System.currentTimeMillis();
        List<DppMemberRecord> memberRecords = dataGenerator.generate(2, numberOfWeeks, className);
        String validRow1 = csvWriter.toCsvLine(memberRecords.getFirst());
        String validRow2 = csvWriter.toCsvLine(memberRecords.getLast());
        String invalidRow1 = String.format("MK382527,\"\"\"''',Robin,Ebert,%s,20241026,20250117,20250412", className);

        String csv = DppCsvHeader.buildHeader(numberOfWeeks) + "\n" +
                validRow1 + "\n" +
                invalidRow1 + "\n" +
                validRow2 + "\n";

        log.info("Invalid CSV: {}", csv);

        Stream<ParsedCsvRow> jsonStream = csvReader.toJsonStream(new ByteArrayInputStream(csv.getBytes()));

        List<ParsedCsvRow> csvRows = jsonStream.toList();

        assertNotNull(csvRows);
        assertFalse(csvRows.isEmpty());
        assertEquals(3, csvRows.size());

        for (ParsedCsvRow csvRow : csvRows) {
            if (csvRow.isSuccess()) {
                JsonNode json = csvRow.json();
                assertNotNull(json);

                assertNotNull(json.get(DppCsvHeader.UNIQUE_ID));
                assertNotNull(json.get(DppCsvHeader.FIRST_NAME));
                assertNotNull(json.get(DppCsvHeader.LAST_NAME));
                assertNotNull(json.get(DppCsvHeader.CLASS));
                assertNotNull(json.get(DppCsvHeader.CLASS_DATE));
                assertNotNull(json.get(DppCsvHeader.STARTING_WEIGHT));
                assertNotNull(json.get(DppCsvHeader.TARGET_WEIGHT));
            } else {
                assertNull(csvRow.json());
                assertNotNull(csvRow.error());
                assertEquals(invalidRow1, csvRow.rawLine());
            }
        }
    }

    @Test
    void testCorruptedFileContent() throws IOException {
        // Create binary/corrupted content (not valid CSV)
        byte[] corruptedContent = {(byte) 0xFF, (byte) 0xFE, (byte) 0x00, (byte) 0x01, 
                                   (byte) 0x89, (byte) 0x50, (byte) 0x4E, (byte) 0x47};

        Stream<ParsedCsvRow> jsonStream = csvReader.toJsonStream(new ByteArrayInputStream(corruptedContent));

        List<ParsedCsvRow> csvRows = jsonStream.toList();

        assertNotNull(csvRows);
        // Parser cannot parse any lines from corrupted content, so returns empty list
        assertTrue(csvRows.isEmpty());
    }
}