package com.vitality.journey.importer.service.generator;

import com.vitality.journey.importer.model.csv.DppMemberRecord;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class DppDataGeneratorTest {

    @Test
    void generatesRecordsWithCorrectProperties() {
        DppDataGenerator gen = new DppDataGenerator();
        int users = 3;
        int weeks = 4;
        List<DppMemberRecord> records = gen.generate(users, weeks, null, null);
        assertNotNull(records);
        assertEquals(users, records.size());

        for (DppMemberRecord memberRecord : records) {
            assertNotNull(memberRecord.getUniqueId());
            assertTrue(memberRecord.getUniqueId().matches("MK\\d{6}"));
            assertNotNull(memberRecord.getFirstName());
            assertNotNull(memberRecord.getLastName());
            assertNotNull(memberRecord.getClassName());
            assertNotNull(memberRecord.getClassDate());
            assertNotNull(memberRecord.getProgramStartDate());
            assertTrue(memberRecord.getClassDate().isBefore(LocalDate.now()) || memberRecord.getClassDate().isEqual(LocalDate.now()));
            assertFalse(memberRecord.getProgramStartDate().isBefore(memberRecord.getClassDate()), "Program Start must be >= Class Date");
            assertTrue(memberRecord.getStartingWeight() >= 151 && memberRecord.getStartingWeight() <= 309);
            assertTrue(memberRecord.getTargetWeight() < memberRecord.getStartingWeight());
            assertEquals(weeks, memberRecord.getWeeks().size());
        }
    }

    @Test
    void uniqueIdsAreAllUniqueWithinGeneratedRecords() {
        DppDataGenerator gen = new DppDataGenerator();
        int users = 500;
        int weeks = 10;
        List<DppMemberRecord> records = gen.generate(users, weeks, null, null);
        assertEquals(users, records.size());
        Set<String> ids = new HashSet<>();
        for (DppMemberRecord memberRecord : records) {
            assertTrue(memberRecord.getUniqueId().matches("MK\\d{6}"));
            boolean added = ids.add(memberRecord.getUniqueId());
            assertTrue(added, "Duplicate Unique ID found: " + memberRecord.getUniqueId());
        }
        assertEquals(users, ids.size());
    }
}