package com.vitality.journey.importer.service.imprt;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.vitality.journey.importer.BaseSpringBootTest;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.model.imprt.ExecutionResult;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.TestConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.BatchStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.wiremock.spring.EnableWireMock;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@EnableWireMock
class ImportOrchestratorIntegrationTest extends BaseSpringBootTest {

    public static final String TEST_CSV_FILE = "test.csv";
    @Autowired
    private ImportOrchestrator importOrchestrator;

    @Test
    void testCompleteImportProcessSuccessful() {
        int numberOfUsers = 5;
        int numberOfWeeks = 26;

        // Generate test CSV content
        List<DppMemberRecord> memberRecords = dataGenerator.generate(numberOfUsers, numberOfWeeks);
        String csv = csvWriter.toCsv(memberRecords);
        InputStream inputStream = new ByteArrayInputStream(csv.getBytes());

        memberRecords.forEach(ImportOrchestratorIntegrationTest::stubEntityResponse);

        // Execute import using ImportOrchestrator
        ExecutionResult executionResult = importOrchestrator.importDppCsv(TEST_CSV_FILE, inputStream, TestConstants.EMPLOYER_ID);

        // Verify Spring Batch job execution
        assertThat(executionResult.status()).isEqualTo(BatchStatus.COMPLETED.name());
        String status = getBatchJobStatus(executionResult.executionId());
        assertThat(status).isEqualTo(BatchStatus.COMPLETED.name());

        // Verify staging records were processed
        List<StagingRecord> stagingRecords = entityManager.createQuery(
                "SELECT s FROM StagingRecord s " +
                    "JOIN StagingToEntityCrossmap c ON s.id = c.id.stagingRecordId " +
                    "LEFT JOIN FETCH s.stagingRecordErrors se " +
                    "WHERE c.id.importJobId = :jobId", StagingRecord.class)
            .setParameter("jobId", executionResult.executionId())
            .getResultList();

        assertThat(stagingRecords).hasSize(numberOfUsers);
        stagingRecords.forEach(stagingRecord -> {
            assertThat(stagingRecord.getStatus()).isEqualTo(RecordStatus.ENRICHED.name());
            assertThat(stagingRecord.getStagingRecordErrors()).isEmpty();
        });

        // Verify members were created and enriched
        List<Member> members = entityManager.createQuery(
                "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                    "ON m.id = c.id.entityRefId " +
                    "WHERE c.id.importJobId = :jobId AND c.id.entityRefType = 'MEMBER'", Member.class)
            .setParameter("jobId", executionResult.executionId())
            .getResultList();

        assertThat(members).hasSize(numberOfUsers);
        members.forEach(member -> {
            assertThat(member.getEntityNo()).isNotNull();
            assertThat(member.getUniqueId()).isNotEmpty();
            assertThat(member.getFirstName()).isNotEmpty();
            assertThat(member.getLastName()).isNotEmpty();
            assertThat(member.getEmployerId()).isEqualTo(TestConstants.EMPLOYER_ID);
        });

        log.info("Complete import process test completed successfully: {} users processed", numberOfUsers);
    }

    @Test
    void testImportProcessWithErrors() {
        int numberOfValidUsers = 3;
        int numberOfWeeks = 26;

        String className = "Test Class " + System.currentTimeMillis();
        var memberRecords = dataGenerator.generate(numberOfValidUsers, numberOfWeeks, className);

        List<String> validCsvRows = new ArrayList<>();

        memberRecords.forEach(memberRecord -> {
            validCsvRows.add(csvWriter.toCsvLine(memberRecord));
            stubEntityResponse(memberRecord);
        });

        String invalidRow = String.format("MK999999,\"\"\"''',Invalid,User,%s,20241026,20250117,20250412", className);

        String csvContent = DppCsvHeader.buildHeader(numberOfWeeks) + "\n" +
            validCsvRows.getFirst() + "\n" +
            invalidRow + "\n" +
            validCsvRows.get(1) + "\n" +
            validCsvRows.get(2) + "\n";

        ByteArrayInputStream inputStream = new ByteArrayInputStream(csvContent.getBytes());

        // Execute import using ImportOrchestrator
        ExecutionResult executionResult = importOrchestrator.importDppCsv(TEST_CSV_FILE, inputStream, TestConstants.EMPLOYER_ID);

        // Verify Spring Batch job execution
        assertThat(executionResult.status()).isEqualTo(BatchStatus.COMPLETED.name());
        String status = getBatchJobStatus(executionResult.executionId());
        assertThat(status).isEqualTo(BatchStatus.COMPLETED.name());

        // Verify error records were handled
        List<StagingRecord> errorRecords = entityManager.createQuery(
                "SELECT s FROM StagingRecord s " +
                    "JOIN StagingToEntityCrossmap c ON s.id = c.id.stagingRecordId " +
                    "LEFT JOIN FETCH s.stagingRecordErrors se " +
                    "WHERE c.id.importJobId = :jobId AND s.status = :status", StagingRecord.class)
            .setParameter("jobId", executionResult.executionId())
            .setParameter("status", RecordStatus.ERROR.name())
            .getResultList();

        assertThat(errorRecords).hasSize(1);

        // Verify valid records were processed
        List<Member> members = entityManager.createQuery(
                "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                    "ON m.id = c.id.entityRefId " +
                    "WHERE c.id.importJobId = :jobId AND c.id.entityRefType = 'MEMBER'", Member.class)
            .setParameter("jobId", executionResult.executionId())
            .getResultList();

        assertThat(members).hasSize(numberOfValidUsers);

        log.info("Error scenario test completed: {} valid users processed, 1 error record handled", numberOfValidUsers);
    }

    @Test
    void testEntityServiceFailure() {
        // Override stub to return empty response (no entity found)
        WireMock.stubFor(get(urlPathEqualTo("/entity/api/findByEmployeeNoAndEmpEntNo"))
            .willReturn(aResponse()
                .withStatus(HttpStatus.OK.value())
                .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .withBody("[]")));

        int numberOfUsers = 2;

        InputStream inputStream = csvHelper.getDppCsvInputStream(numberOfUsers, 26);

        // Execute import using ImportOrchestrator
        ExecutionResult executionResult = importOrchestrator.importDppCsv(TEST_CSV_FILE, inputStream, TestConstants.EMPLOYER_ID);

        // Verify Spring Batch job execution
        assertThat(executionResult.status()).isEqualTo(BatchStatus.FAILED.name());
        String status = getBatchJobStatus(executionResult.executionId());
        assertThat(status).isEqualTo(BatchStatus.FAILED.name());

        // Verify members were created but not enriched (entityNo should be null)
        List<Member> members = entityManager.createQuery(
                "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                    "ON m.id = c.id.entityRefId " +
                    "WHERE c.id.importJobId = :jobId AND c.id.entityRefType = 'MEMBER'", Member.class)
            .setParameter("jobId", executionResult.executionId())
            .getResultList();

        assertThat(members).hasSize(numberOfUsers);
        members.forEach(member -> assertThat(member.getEntityNo()).isNull());

        log.info("Entity service failure test completed: {} members processed without enrichment", numberOfUsers);
    }

    private String getBatchJobStatus(long jobExecutionId) {
        return (String) entityManager.createNativeQuery(
                "SELECT STATUS FROM JOURNEY_IMPORT.BATCH_JOB_EXECUTION WHERE JOB_EXECUTION_ID = ?")
            .setParameter(1, jobExecutionId)
            .getSingleResult();
    }

    private static void stubEntityResponse(DppMemberRecord memberRecord) {
        WireMock.stubFor(get(urlPathEqualTo("/v3/entity/api/findByEmployeeNoAndEmpEntNo"))
            .withQueryParam("employeeNo", equalTo(memberRecord.getUniqueId()))
            .withQueryParam("employerEntityNo", equalTo(String.valueOf(TestConstants.EMPLOYER_ID)))
            .willReturn(aResponse()
                .withStatus(HttpStatus.OK.value())
                .withHeader("Content-Type", "application/json")
                .withBody("""
                    [{
                        "memberEnityNo": {{randomInt lower=1000000000 upper=9999999999}},
                        "employeeNo": "%s",
                        "empEntNo": %s,
                        "firstName": "%s",
                        "lastName": "%s",
                        "particStat": "P"
                    }]
                    """.formatted(memberRecord.getUniqueId(), TestConstants.EMPLOYER_ID,
                    memberRecord.getFirstName(), memberRecord.getLastName()))
                .withTransformers("response-template")));
    }
}
