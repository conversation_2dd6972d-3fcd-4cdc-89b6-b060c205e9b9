package com.vitality.journey.importer.service.csv;

import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class CsvWriterTest {

    @Test
    void toCsv() {
        int numberOfUsers = 10;
        int numberOfWeeks = 26;

        CsvWriter csvWriter = new CsvWriter();
        DppDataGenerator dataGenerator = new DppDataGenerator();
        List<DppMemberRecord> records = dataGenerator.generate(numberOfUsers, numberOfWeeks);

        String csv = csvWriter.toCsv(records);

        assertNotNull(csv);
        String[] lines = csv.split("\r?\n");
        assertEquals(numberOfUsers + 1, lines.length);

        assertEquals(DppCsvHeader.buildHeader(numberOfWeeks), lines[0]);
    }
}
