package com.vitality.journey.importer;

import com.vitality.journey.importer.helper.TestCsvHelper;
import com.vitality.journey.importer.service.csv.CsvWriter;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;

@SpringBootTest
@ActiveProfiles({"h2-db"})
@Sql(scripts = {"/db/clear-db.sql", "/db/init-data.sql"},
        executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD,
        config = @SqlConfig(transactionMode = SqlConfig.TransactionMode.ISOLATED))
public abstract class BaseSpringBootTest {
    @Autowired
    protected CsvWriter csvWriter;

    @Autowired
    protected DppDataGenerator dataGenerator;

    @PersistenceContext
    protected EntityManager entityManager;

    @Autowired
    protected TestCsvHelper csvHelper;
}
