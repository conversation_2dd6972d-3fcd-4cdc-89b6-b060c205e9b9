package com.vitality.journey.importer.batch.enrich;

import com.vitality.journey.importer.batch.BaseBatchTest;
import com.vitality.journey.importer.batch.step.ImportJobStep;
import com.vitality.journey.importer.batch.job.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import com.vitality.journey.importer.helper.TestFile;
import com.vitality.journey.importer.mapper.MemberMapper;
import com.vitality.journey.importer.model.MemberRecord;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.member.EntityNoResolver;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.OptionalLong;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

@Slf4j
@ContextConfiguration(classes = EnrichmentStepTest.TestConfig.class)
class EnrichmentStepTest extends BaseBatchTest {

    @Autowired
    @Qualifier("stagingStep")
    private Step stagingStep;

    @Autowired
    @Qualifier("normalizeStep")
    private Step normalizeStep;

    @Autowired
    @Qualifier("enrichStep")
    private Step enrichStep;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private EntityNoResolver entityNoResolver;
    @Autowired
    private MemberMapper memberMapper;

    @TestConfiguration
    static class TestConfig {
        @Bean
        @Primary
        public EntityNoResolver entityNoResolver() {
            return mock(EntityNoResolver.class);
        }
    }

    @BeforeEach
    void setUp() {
        jobRepositoryTestUtils.removeJobExecutions();
    }

    @Test
    void testEnrichmentStepWithValidMembers() throws Exception {
        // Given - Mock successful entity resolution
        reset(entityNoResolver);
        when(entityNoResolver.resolveEntityNo(any(MemberRecord.class)))
            .thenReturn(OptionalLong.of(12345L));

        int numberOfUsers = 3;
        int numberOfWeeks = 26;
        Long employerId = 100L;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, numberOfWeeks))) {
            // Create custom job with staging, normalization, and enrichment steps
            Job testJob = new JobBuilder("testEnrichmentJob", jobRepository)
                .start(stagingStep)
                .next(normalizeStep)
                .next(enrichStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters jobParams = JobParametersFactory.getImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Verify job completed successfully
            assertThat(jobExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);
            assertThat(jobExecution.getStepExecutions()).hasSize(numberOfUsers); // staging + normalize + enrich

            // Verify enrichment step executed successfully
            validateSuccessfulStepExecution(jobExecution, ImportJobStep.ENRICH, numberOfUsers);

            // Verify members were enriched with entityNo
            List<Member> enrichedMembers = entityManager.createQuery(
                    "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                        "ON m.id = c.id.entityRefId " +
                        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER'", Member.class)
                .setParameter("importJobId", jobExecution.getJobId())
                .getResultList();

            assertThat(enrichedMembers).hasSize(numberOfUsers);
            enrichedMembers.forEach(member -> {
                assertThat(member.getEntityNo()).isEqualTo(12345L);
                assertThat(member.getUniqueId()).isNotEmpty();
                assertThat(member.getEmployerId()).isEqualTo(employerId);
            });
        }
    }

    @Test
    void testEnrichmentStepWithEntityResolutionFailure() throws Exception {
        // Given - Mock failed entity resolution
        reset(entityNoResolver);
        when(entityNoResolver.resolveEntityNo(any(MemberRecord.class)))
            .thenReturn(OptionalLong.empty());

        int numberOfUsers = 2;
        Long employerId = 100L;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, 26))) {
            // Create custom job with staging, normalization, and enrichment steps
            Job testJob = new JobBuilder("testEnrichmentFailure", jobRepository)
                .start(stagingStep)
                .next(normalizeStep)
                .next(enrichStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters jobParams = JobParametersFactory.getImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Job should complete with skipped records
            assertThat(jobExecution.getExitStatus().getExitCode()).isEqualTo(ExitStatus.FAILED.getExitCode());
            assertThat(jobExecution.getExitStatus().getExitDescription()).contains("All " + numberOfUsers + " records failed; step enrichStep is exitCode=FAILED");

            // Verify enrichment step statistics
            jobExecution.getStepExecutions().stream()
                .filter(step -> step.getStepName().equals(ImportJobStep.ENRICH.getStepName()))
                .findFirst()
                .ifPresent(stepExecution -> {
                    assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                    assertThat(stepExecution.getWriteCount()).isZero(); // No records written due to failures
                    assertThat(stepExecution.getSkipCount()).isEqualTo(numberOfUsers); // All records skipped
                });

            // Verify members still have null entityNo
            List<Member> members = entityManager.createQuery(
                    "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                        "ON m.id = c.id.entityRefId " +
                        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER'", Member.class)
                .setParameter("importJobId", jobExecution.getJobId())
                .getResultList();

            assertThat(members).hasSize(numberOfUsers);
            members.forEach(member -> assertThat(member.getEntityNo()).isNull());

            // Verify staging records were marked as ERROR with StagingRecordError
            List<StagingRecord> errorRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.ERROR);
            assertThat(errorRecords).hasSize(numberOfUsers);

            errorRecords.forEach(errorRecord -> {
                assertThat(errorRecord.getStagingRecordErrors()).hasSize(1);
                StagingRecordError stagingRecordError = CollectionUtils.firstElement(errorRecord.getStagingRecordErrors());
                assertThat(stagingRecordError).isNotNull();
                assertThat(stagingRecordError.getErrorMessage()).isNotNull();
                String errorMessage = JdbcLobUtils.readClobAsString(stagingRecordError.getErrorMessage());
                assertThat(errorMessage).contains("Failed to resolve entityNo");
            });
        }
    }

    @Test
    void testEnrichmentStepWithUnexpectedException() throws Exception {
        // Given - Mock unexpected exception during entity resolution
        when(entityNoResolver.resolveEntityNo(any(MemberRecord.class)))
            .thenThrow(new RuntimeException("External service unavailable"));

        int numberOfUsers = 2;
        Long employerId = 100L;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, 26))) {
            Job testJob = new JobBuilder("testEnrichmentException", jobRepository)
                .start(stagingStep)
                .next(normalizeStep)
                .next(enrichStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters jobParams = JobParametersFactory.getImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Job should complete with all records skipped due to exceptions
            assertThat(jobExecution.getExitStatus().getExitCode()).isEqualTo(ExitStatus.FAILED.getExitCode());
            assertThat(jobExecution.getExitStatus().getExitDescription()).contains("All " + numberOfUsers + " records failed; step enrichStep is exitCode=FAILED");

            // Verify enrichment step statistics
            jobExecution.getStepExecutions().stream()
                .filter(step -> step.getStepName().equals(ImportJobStep.ENRICH.getStepName()))
                .findFirst()
                .ifPresent(stepExecution -> {
                    assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                    assertThat(stepExecution.getWriteCount()).isZero(); // No records written due to exceptions
                    assertThat(stepExecution.getSkipCount()).isEqualTo(numberOfUsers); // All records skipped
                });

            // Verify staging records were marked as ERROR with StagingRecordError
            List<StagingRecord> errorRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.ERROR);
            assertThat(errorRecords).hasSize(numberOfUsers);

            errorRecords.forEach(errorRecord -> {
                assertThat(errorRecord.getStagingRecordErrors()).hasSize(1);
                StagingRecordError stagingRecordError = CollectionUtils.firstElement(errorRecord.getStagingRecordErrors());
                assertThat(stagingRecordError).isNotNull();
                assertThat(stagingRecordError.getErrorMessage()).isNotNull();
                String errorMessage = JdbcLobUtils.readClobAsString(stagingRecordError.getErrorMessage());
                assertThat(errorMessage).contains("External service unavailable");
            });
        }
    }

    @Test
    void testEnrichmentStepWithMixedSuccessAndFailure() throws Exception {
        // Given - Mock mixed results: some succeed, some fail
        int numberOfUsers = 4;
        Long employerId = 100L;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, 26))) {
            // Create tasklet to set up specific mock behavior after normalization
            Tasklet mockSetupTasklet = (contribution, chunkContext) -> {
                Long jobId = chunkContext.getStepContext().getStepExecution().getJobExecution().getJobId();

                // Get the created members to set up specific mock behavior
                List<Member> members = entityManager.createQuery(
                        "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                            "ON m.id = c.id.entityRefId " +
                            "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER' " +
                            "ORDER BY m.id", Member.class)
                    .setParameter("importJobId", jobId)
                    .getResultList();

                // Mock different responses for different members
                when(entityNoResolver.resolveEntityNo(eq(memberMapper.toRecord(members.get(0)))))
                    .thenReturn(OptionalLong.of(11111L)); // Success
                when(entityNoResolver.resolveEntityNo(eq(memberMapper.toRecord(members.get(1)))))
                    .thenReturn(OptionalLong.empty()); // Failure - no entity found
                when(entityNoResolver.resolveEntityNo(eq(memberMapper.toRecord(members.get(2)))))
                    .thenReturn(OptionalLong.of(22222L)); // Success
                when(entityNoResolver.resolveEntityNo(eq(memberMapper.toRecord(members.get(3)))))
                    .thenThrow(new RuntimeException("Service timeout")); // Exception

                return RepeatStatus.FINISHED;
            };

            Step mockSetupStep = new StepBuilder("mockSetupStep", jobRepository)
                .tasklet(mockSetupTasklet, transactionManager)
                .build();

            // Create single job with all steps
            Job testJob = new JobBuilder("testEnrichmentMixed", jobRepository)
                .start(stagingStep)
                .next(normalizeStep)
                .next(mockSetupStep)
                .next(enrichStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters jobParams = JobParametersFactory.getImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Job should complete with mixed results
            assertThat(jobExecution.getExitStatus())
                .isEqualTo(ExitStatus.COMPLETED.addExitDescription("Failed to process 2 records"));
            assertThat(jobExecution.getStepExecutions()).hasSize(4); // staging + normalize + mockSetup + enrich

            // Verify enrichment step statistics
            jobExecution.getStepExecutions().stream()
                .filter(step -> step.getStepName().equals(ImportJobStep.ENRICH.getStepName()))
                .findFirst()
                .ifPresent(stepExecution -> {
                    assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                    assertThat(stepExecution.getWriteCount()).isEqualTo(2); // 2 successful enrichments
                    assertThat(stepExecution.getSkipCount()).isEqualTo(2); // 2 failed enrichments
                });

            // Verify mixed results
            List<Member> members = entityManager.createQuery(
                    "SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
                        "ON m.id = c.id.entityRefId " +
                        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER' " +
                        "ORDER BY m.id", Member.class)
                .setParameter("importJobId", jobExecution.getJobId())
                .getResultList();

            // Verify successful enrichments
            assertThat(members.get(0).getEntityNo()).isEqualTo(11111L);
            assertThat(members.get(2).getEntityNo()).isEqualTo(22222L);

            // Verify failed enrichments still have null entityNo
            assertThat(members.get(1).getEntityNo()).isNull();
            assertThat(members.get(3).getEntityNo()).isNull();

            // Verify error records were created for failed enrichments
            List<StagingRecord> errorRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.ERROR);
            assertThat(errorRecords).hasSize(2); // 2 failed enrichments

            errorRecords.forEach(errorRecord -> {
                assertThat(errorRecord.getStagingRecordErrors()).hasSize(1);
                StagingRecordError stagingRecordError = CollectionUtils.firstElement(errorRecord.getStagingRecordErrors());
                assertThat(stagingRecordError).isNotNull();
                assertThat(stagingRecordError.getErrorMessage()).isNotNull();
            });
        }
    }

    private static void validateSuccessfulStepExecution(JobExecution jobExecution, ImportJobStep step, int expectedCount) {
        jobExecution.getStepExecutions().stream()
            .filter(stepExec -> stepExec.getStepName().equals(step.getStepName()))
            .forEach(stepExecution -> {
                assertThat(stepExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);
                assertThat(stepExecution.getReadCount()).isEqualTo(expectedCount);
                assertThat(stepExecution.getWriteCount()).isEqualTo(expectedCount);
                assertThat(stepExecution.getSkipCount()).isZero();
            });
    }
}
