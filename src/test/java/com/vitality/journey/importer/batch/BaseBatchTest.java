package com.vitality.journey.importer.batch;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.repository.StagingRecordRepository;
import com.vitality.journey.importer.helper.TestCsvHelper;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.csv.CsvWriter;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import com.vitality.journey.importer.service.imprt.EntityCrossmapService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.batch.test.JobLauncherTestUtils;
import org.springframework.batch.test.JobRepositoryTestUtils;
import org.springframework.batch.test.context.SpringBatchTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;

import java.util.List;

@SpringBatchTest
@SpringBootTest
@ActiveProfiles({"h2-db"})
@Sql(scripts = {"/db/clear-db.sql", "/db/init-data.sql"},
        executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD,
        config = @SqlConfig(transactionMode = SqlConfig.TransactionMode.ISOLATED))
public abstract class BaseBatchTest {

    @Autowired
    protected JobLauncherTestUtils jobLauncherTestUtils;

    @Autowired
    protected JobRepositoryTestUtils jobRepositoryTestUtils;

    @Autowired
    protected StagingRecordRepository stagingRecordRepository;

    @Autowired
    protected TestCsvHelper csvHelper;

    @Autowired
    protected DppDataGenerator dataGenerator;

    @Autowired
    protected CsvWriter csvWriter;

    @PersistenceContext
    protected EntityManager entityManager;

    protected List<StagingRecord> fetchStagingRecordsForJob(long jobId) {
        return entityManager.createQuery("SELECT s FROM StagingRecord s " +
                                "JOIN StagingToEntityCrossmap c " +
                                "     ON s.id = c.id.stagingRecordId " +
                                "     AND c.id.entityRefType = '" + EntityCrossmapService.ENTITY_TYPE_BATCH_JOB_EXECUTION + "' " +
                                "     AND c.id.entityRefId = :jobId " +
                                "LEFT JOIN FETCH s.stagingRecordErrors se " +
                                "WHERE c.id.importJobId = :jobId",
                        StagingRecord.class)
                .setParameter("jobId", jobId)
                .getResultList();
    }

    protected List<StagingRecord> fetchStagingRecordsForJob(long jobId, RecordStatus status) {
        return entityManager.createQuery("SELECT s FROM StagingRecord s " +
                                "JOIN StagingToEntityCrossmap c " +
                                "     ON s.id = c.id.stagingRecordId " +
                                "     AND c.id.entityRefType = '" + EntityCrossmapService.ENTITY_TYPE_BATCH_JOB_EXECUTION + "' " +
                                "     AND c.id.entityRefId = :jobId " +
                                "LEFT JOIN FETCH s.stagingRecordErrors se " +
                                "WHERE c.id.importJobId = :jobId " +
                                "AND s.status = :status",
                        StagingRecord.class)
                .setParameter("jobId", jobId)
                .setParameter("status", status.name())
                .getResultList();
    }
}
