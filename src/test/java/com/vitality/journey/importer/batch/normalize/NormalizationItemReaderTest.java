package com.vitality.journey.importer.batch.normalize;

import com.vitality.journey.importer.batch.step.normalize.NormalizationItemReader;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class NormalizationItemReaderTest {

    private NormalizationItemReader reader;
    private StagingRecordRepositoryExtension repository;
    private StepExecution stepExecution;

    @BeforeEach
    void setUp() {
        repository = mock(StagingRecordRepositoryExtension.class);
        reader = new NormalizationItemReader(repository);
        stepExecution = mock(StepExecution.class);
    }

    @Test
    void shouldReadStagingRecords() throws Exception {
        // Given
        Long jobExecutionId = 123L;
        when(stepExecution.getJobExecutionId()).thenReturn(jobExecutionId);
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters());

        StepSynchronizationManager.register(stepExecution);

        StagingRecord record1 = new StagingRecord();
        record1.setId(1L);
        StagingRecord record2 = new StagingRecord();
        record2.setId(2L);

        // First page with data, second page empty
        Page<StagingRecord> firstPage = new PageImpl<>(List.of(record1, record2));
        Page<StagingRecord> emptyPage = new PageImpl<>(List.of());

        when(repository.findByJobIdAndStatus(eq(jobExecutionId), eq(RecordStatus.LOADED.name()), any(Pageable.class)))
                .thenReturn(firstPage)
                .thenReturn(emptyPage);

        // When
        reader.beforeStep(stepExecution);
        StagingRecord result1 = reader.read();
        StagingRecord result2 = reader.read();
        StagingRecord result3 = reader.read();

        // Then
        assertNotNull(result1);
        assertEquals(1L, result1.getId());
        assertNotNull(result2);
        assertEquals(2L, result2.getId());
        assertNull(result3); // End of data
    }

    @Test
    void shouldReturnNullWhenNoRecords() throws Exception {
        // Given
        Long jobExecutionId = 123L;
        when(stepExecution.getJobExecutionId()).thenReturn(jobExecutionId);
        when(stepExecution.getJobParameters()).thenReturn(new JobParameters());
        StepSynchronizationManager.register(stepExecution);

        Page<StagingRecord> emptyPage = new PageImpl<>(List.of());
        when(repository.findByJobIdAndStatus(eq(jobExecutionId), eq(RecordStatus.LOADED.name()), any(Pageable.class)))
                .thenReturn(emptyPage);

        // When
        reader.beforeStep(stepExecution);
        StagingRecord result = reader.read();

        // Then
        assertNull(result);
    }
}
