Feature: Journey Seeder Test Suite
  As a test engineer
  I want to create and configure journey categories and programs
  So that I can validate the journey seeding functionality

  Background:
    Given the rest application has started up
    And the journey seeder is initialized
    And the database is clean

  Scenario: Create a basic journey category with default configuration
    Given I have a category with categoryCode "FITNESS_BASIC"
    When this category configuration is:
      | journeyStartTime         | 2025-01-01T00:00:00 |
      | activityPrecondition     | FITNESS_LEVEL_1     |
      | enrollmentPrecondition   | MEMBER_ACTIVE       |
      | enrollmentStartTime      | 2025-01-01T00:00:00 |
      | enrollmentEndTime        | 2025-12-31T23:59:59 |
      | maxParticipants          | 500                 |
      | monitoringPeriodDuration | 30                  |
    Then the category "FITNESS_BASIC" should be created successfully
    And the category should have the correct configuration

  Scenario: Create a program and associate it with a category
    Given I have a category with categoryCode "WELLNESS_PROGRAM"
    And this category configuration is:
      | journeyStartTime         | 2025-02-01T00:00:00 |
      | activityPrecondition     | WELLNESS_ELIGIBLE   |
      | enrollmentPrecondition   | MEMBER_PREMIUM      |
      | enrollmentStartTime      | 2025-02-01T00:00:00 |
      | enrollmentEndTime        | 2025-11-30T23:59:59 |
      | maxParticipants          | 1000                |
      | monitoringPeriodDuration | 45                  |
    When I have a program "WELLNESS_PROGRAM_2025"
    And this category and program is categorized
    Then the categorization should be created successfully
    And the program should be associated with the category

  Scenario: Configure program with recommended activities
    Given I have a category with categoryCode "CARDIO_CHALLENGE"
    And this category configuration is:
      | journeyStartTime         | 2025-03-01T00:00:00 |
      | enrollmentStartTime      | 2025-03-01T00:00:00 |
      | enrollmentEndTime        | 2025-06-30T23:59:59 |
      | maxParticipants          | 300                 |
      | monitoringPeriodDuration | 60                  |
    And I have a program "CARDIO_CHALLENGE_SPRING"
    And this category and program is categorized
    When this program has recommended activities:
      | activityId      | RUNNING_001   |
      | activityName    | Daily Running |
      | activityIcon    | running-icon  |
      | activityType    | CARDIO        |
      | frequency       | 5             |
      | completionType  | AUTO          |
      | isSkipMilestone | false         |
      | milestoneValue  | 1             |
    And this program has recommended activities:
      | activityId      | CYCLING_001    |
      | activityName    | Weekly Cycling |
      | activityIcon    | cycling-icon   |
      | activityType    | CARDIO         |
      | frequency       | 2              |
      | completionType  | MANUAL         |
      | isSkipMilestone | false          |
      | milestoneValue  | 2              |
    And the program setup is finalized
    Then the program should have 2 recommended activities
    And the activity "RUNNING_001" should have frequency 5
    And the activity "CYCLING_001" should have frequency 2

  Scenario: Configure program with activity completion preconditions
    Given I have a category with categoryCode "STRENGTH_TRAINING"
    And I have a program "STRENGTH_PROGRAM_ADVANCED"
    And this category and program is categorized
    When this program has activity completion preconditions:
      | completionPreconditionType | PREVIOUS_ACTIVITY   |
      | completionIdentifier       | STRENGTH_BASIC_DONE |
      | activityId                 | STRENGTH_BASIC_001  |
      | iteration                  | 5                   |
      | milestoneValue             | 3                   |
    And this program has activity completion preconditions:
      | completionPreconditionType | TIME_BASED      |
      | completionIdentifier       | WEEK_2_COMPLETE |
      | activityId                 | WEEK_MILESTONE  |
      | iteration                  | 1               |
      | milestoneValue             | 2               |
    And the program setup is finalized
    Then the program should have 2 activity completion preconditions
    And the precondition "STRENGTH_BASIC_DONE" should require 5 iterations

  Scenario: Configure program with milestone rewards
    Given I have a category with categoryCode "NUTRITION_JOURNEY"
    And I have a program "HEALTHY_EATING_PROGRAM"
    And this category and program is categorized
    When this program has milestone rewards:
      | rewardType        | ACTIVITY_BASED_POINTS |
      | externalReference | NUTRITION_PT_01       |
    And this program has milestone rewards:
      | rewardType        | BADGE           |
      | externalReference | NUTRITION_BG_01 |
    And the program setup is finalized
    Then the program should have 2 milestone rewards
    And the milestone reward with reference "NUTRITION_PT_01" should be of type "ACTIVITY_BASED_POINTS"
    And the milestone reward with reference "NUTRITION_BG_01" should be of type "BADGE"

  Scenario: Configure program with program rewards
    Given I have a category with categoryCode "MINDFULNESS_PRACTICE"
    And I have a program "MEDITATION_MASTERY"
    And this category and program is categorized
    When this program has program rewards:
      | rewardType        | CARD          |
      | externalReference | MEDITATION_DIS_01 |
    And this program has program rewards:
      | rewardType        | SPIN           |
      | externalReference | MEDITATION_VOU_01 |
    And the program setup is finalized
    Then the program should have 2 program rewards
    And the program reward with reference "MEDITATION_DIS_01" should be of type "CARD"
    And the program reward with reference "MEDITATION_VOU_01" should be of type "SPIN"

  Scenario: Complete journey setup with all components
    Given I have a category with categoryCode "COMPLETE_WELLNESS"
    And this category configuration is:
      | journeyStartTime         | 2025-01-15T00:00:00 |
      | activityPrecondition     | COMPLETE_ELIGIBLE   |
      | enrollmentPrecondition   | MEMBER_PLATINUM     |
      | enrollmentStartTime      | 2025-01-15T00:00:00 |
      | enrollmentEndTime        | 2025-12-15T23:59:59 |
      | maxParticipants          | 150                 |
      | monitoringPeriodDuration | 90                  |
    And I have a program "COMPLETE_WELLNESS_2025"
    And this category and program is categorized
    When this program has recommended activities:
      | activityId      | WORKOUT_001   |
      | activityName    | Daily Workout |
      | activityIcon    | workout-icon  |
      | activityType    | FITNESS       |
      | frequency       | 7             |
      | completionType  | AUTO          |
      | isSkipMilestone | false         |
      | milestoneValue  | 1             |
    And this program has activity completion preconditions:
      | completionPreconditionType | PREREQUISITE      |
      | completionIdentifier       | HEALTH_CHECK_DONE |
      | activityId                 | HEALTH_CHECK_001  |
      | iteration                  | 1                 |
      | milestoneValue             | 1                 |
    And this program has milestone rewards:
      | rewardType        | ACTIVITY_BASED_POINTS         |
      | externalReference | WELLNESS_PT_01 |
    And this program has program rewards:
      | rewardType        | CERTIFICATE      |
      | externalReference | WELLNESS_CERT_01 |
    And the program setup is finalized
    Then the category "COMPLETE_WELLNESS" should be created successfully
    And the program "COMPLETE_WELLNESS_2025" should be created successfully
    And the categorization should be created successfully
    And the program should have 1 recommended activities
    And the program should have 1 activity completion preconditions
    And the program should have 1 milestone rewards
    And the program should have 1 program rewards

  Scenario Outline: Create multiple journey categories with different configurations
    Given I have a category with categoryCode "<categoryCode>"
    When this category configuration is:
      | journeyStartTime         | <startTime>        |
      | maxParticipants          | <maxParticipants>  |
      | monitoringPeriodDuration | <monitoringPeriod> |
    Then the category "<categoryCode>" should be created successfully
    And the category should have max participants <maxParticipants>
    And the category should have monitoring period <monitoringPeriod> days

    Examples:
      | categoryCode         | startTime           | maxParticipants | monitoringPeriod |
      | BEGINNER_FITNESS     | 2025-01-01T00:00:00 | 1000            | 30               |
      | INTERMEDIATE_FITNESS | 2025-02-01T00:00:00 | 500             | 45               |
      | ADVANCED_FITNESS     | 2025-03-01T00:00:00 | 200             | 60               |
      | EXPERT_FITNESS       | 2025-04-01T00:00:00 | 50              | 90               |

  Scenario: Error handling - Create program without category
    When I attempt to create a program "ORPHAN_PROGRAM" without a category
    Then an error should be thrown indicating "No current category exists"

  Scenario: Error handling - Categorize without both category and program
    Given I have a category with categoryCode "LONELY_CATEGORY"
    When I attempt to categorize without a program
    Then an error should be thrown indicating "Both category and program must exist"

  Scenario: Validate context reset between scenarios
    Given I have a category with categoryCode "TEMP_CATEGORY"
    And I have a program "TEMP_PROGRAM"
    When the context is reset
    Then no current category should exist
    And no current program should exist
    And no categorization should exist

  Scenario: Legacy seedJourney method compatibility
    When I use the legacy seedJourney method with data:
      | categoryType                           | FITNESS                |
      | categoryCode                           | LEGACY_FITNESS         |
      | externalReference                      | EXT_REF_LEGACY         |
      | journeyStartTime                       | 2025-01-01T00:00:00    |
      | activityPrecondition                   | FITNESS_LEVEL_1        |
      | enrollmentPrecondition                 | MEMBER_ACTIVE          |
      | enrollmentStartTime                    | 2025-01-01T00:00:00    |
      | enrollmentEndTime                      | 2025-12-31T23:59:59    |
      | maxParticipants                        | 500                    |
      | monitoringPeriodDuration               | 30                     |
      | programTransitionRule                  | DEFAULT_PROGRAM_RULE   |
      | milestoneTransitionRule                | DEFAULT_MILESTONE_RULE |
      | duration                               | 60                     |
      | programRewardExternalReference         | PROG_REWARD_REF        |
      | programRewardType                      | ACTIVITY_BASED_POINTS  |
      | milestoneRewardExternalReference       | MILE_REWARD_REF        |
      | milestoneRewardType                    | BADGE                  |
      | ActivityId                             | LEGACY_ACTIVITY_001    |
      | activityName                           | Legacy Activity        |
      | activityIcon                           | legacy-icon            |
      | activityType                           | GENERAL                |
      | frequency                              | 3                      |
      | completionType                         | MANUAL                 |
      | isSkipMilestone                        | false                  |
      | milestoneValue                         | 1                      |
      | preconditionMilestoneValue             | 1                      |
      | preconditionActivityId                 | LEGACY_PRECOND_001     |
      | preconditionIteration                  | 2                      |
      | preconditionCompletionPreconditionType | LEGACY_TYPE            |
      | preconditionCompletionIdentifier       | LEGACY_IDENTIFIER      |
    Then the legacy journey should be created successfully
    And the category "LEGACY_FITNESS" should exist
    And the program should have been created and categorized
