spring:
  application:
    name: hs-journey-importer
  profiles.include: h2-db, b2b
  cloud:
    config:
      import-check:
        enabled: false
  jpa:
    properties:
      generate_statistics: true
      hibernate:
        format_sql: false
        id:
          new_generator_mappings: true
        current_session_context_class: thread
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: none
  batch:
    job:
      enabled: false
    jdbc:
      table-prefix: JOURNEY_IMPORT.BATCH_
      initialize-schema: never
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

server:
  port: 35350
  servlet:
    context-path: /v3/journey-importer

entity-service:
  url: http://localhost:${wiremock.server.port:0}/v3/entity

personal-journey-service:
  url: http://localhost:${wiremock.server.port:0}/v3/journey

logging:
  pattern:
    level: "%5p [${spring.application.name:},%X{trace_id:-},%X{span_id:-}]"
  level:
    root: INFO
#    org.springframework.transaction.interceptor: DEBUG
#    org.hibernate.SQL: DEBUG
    #    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
#    org.hibernate.orm.jdbc.bind: TRACE

management:
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"

security:
  oauth2:
    client:
      client-id: hs-platform
      client-secret: iCqganMJ2jZLcrcBtt2pbkQO7RuFh3G6
      scope: openid,profile,email,roles
      oauth-base-url: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/auth
    resource:
      id: hs-platform
      token-info-uri: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/token
      user-info-uri: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/userinfo
      jwk:
        key-set-uri: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/certs

hs:
  oauth2:
    url: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: client
      secret: secret

platform:
  logging:
    console-level: "info"
    json-console-level: "off"
    flat-file-level: "off"
    json-file-level: "off"
  oauth2:
    resourceserver:
      cors:
        allowed-origins: '*'
        allowed-methods: GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS,TRACE
        allowed-headers: X-XSRF-TOKEN,XSRF-TOKEN,CONTENT-TYPE
        allow-credentials: true
        max-age: 10
      developer-endpoints:
        - paths: /actuator/**,/**/actuator/**
          role: DEVELOPER
      public-endpoints:
        - paths: /**
---
spring:
  config:
    activate:
      on-profile: h2-db
  h2:
    console:
      enabled: false
  flyway:
    enabled: true
    locations: classpath:/database/db/migration
  datasource:
    type: org.apache.tomcat.jdbc.pool.DataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:JOURNEY_IMPORT
    username: sa
    password:
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
