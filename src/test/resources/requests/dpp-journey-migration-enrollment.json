{"journeyTemplate": {"journeyType": "DPP", "journeyMilestoneType": "WEEK", "journeyName": "<PERSON> Past DPP - Living Well with Chronic Conditions (Do not touch)", "attributes": {"WELLSPARK_DPP_MIGRATION": "true"}, "member": {"id": 1, "entityNo": 1424540217}, "program": {"programStartDate": "2025-02-03T00:00:00", "programEndDate": "2026-02-08T23:59:59", "memberStartDate": "2025-03-30", "memberLastAttendedDate": "2025-07-28", "startingWeight": 169, "targetWeight": 149, "milestones": [{"iteration": 1, "startDate": "2025-02-03T00:00:00", "endDate": "2025-02-09T23:59:59", "weight": 181, "activityMinutes": 215, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 2, "startDate": "2025-02-10T00:00:00", "endDate": "2025-02-16T23:59:59", "weight": 250, "activityMinutes": 150, "attendanceMode": "RECORDING", "sessionDate": "2025-02-10"}, {"iteration": 3, "startDate": "2025-02-17T00:00:00", "endDate": "2025-02-23T23:59:59", "weight": 314, "activityMinutes": 135, "attendanceMode": "LIVE", "sessionDate": "2025-02-17"}, {"iteration": 4, "startDate": "2025-02-24T00:00:00", "endDate": "2025-03-02T23:59:59", "activityMinutes": 235, "attendanceMode": "RECORDING", "sessionDate": "2025-02-24"}, {"iteration": 5, "startDate": "2025-03-03T00:00:00", "endDate": "2025-03-09T23:59:59", "weight": 286, "activityMinutes": 165, "attendanceMode": "RECORDING", "sessionDate": "2025-03-03"}, {"iteration": 6, "startDate": "2025-03-10T00:00:00", "endDate": "2025-03-16T23:59:59", "attendanceMode": "RECORDING", "sessionDate": "2025-03-10"}, {"iteration": 7, "startDate": "2025-03-17T00:00:00", "endDate": "2025-03-23T23:59:59", "attendanceMode": "LIVE", "sessionDate": "2025-03-17"}, {"iteration": 8, "startDate": "2025-03-24T00:00:00", "endDate": "2025-03-30T23:59:59", "weight": 147, "activityMinutes": 200, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 9, "startDate": "2025-03-31T00:00:00", "endDate": "2025-04-06T23:59:59", "activityMinutes": 185, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 10, "startDate": "2025-04-07T00:00:00", "endDate": "2025-04-13T23:59:59", "activityMinutes": 230, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 11, "startDate": "2025-04-14T00:00:00", "endDate": "2025-04-20T23:59:59", "weight": 191, "activityMinutes": 80, "attendanceMode": "RECORDING", "sessionDate": "2025-04-14"}, {"iteration": 12, "startDate": "2025-04-21T00:00:00", "endDate": "2025-04-27T23:59:59", "activityMinutes": 140, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 13, "startDate": "2025-04-28T00:00:00", "endDate": "2025-05-04T23:59:59", "weight": 140, "activityMinutes": 180, "attendanceMode": "LIVE", "sessionDate": "2025-04-28"}, {"iteration": 14, "startDate": "2025-05-05T00:00:00", "endDate": "2025-05-11T23:59:59", "weight": 145, "activityMinutes": 120, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 15, "startDate": "2025-05-12T00:00:00", "endDate": "2025-05-18T23:59:59", "weight": 226, "activityMinutes": 210, "attendanceMode": "LIVE", "sessionDate": "2025-05-12"}, {"iteration": 16, "startDate": "2025-05-19T00:00:00", "endDate": "2025-05-25T23:59:59", "attendanceMode": "LIVE", "sessionDate": "2025-05-19"}, {"iteration": 17, "startDate": "2025-05-26T00:00:00", "endDate": "2025-06-01T23:59:59", "weight": 318, "activityMinutes": 220, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 18, "startDate": "2025-06-02T00:00:00", "endDate": "2025-06-08T23:59:59", "activityMinutes": 215, "attendanceMode": "LIVE", "sessionDate": "2025-06-02"}, {"iteration": 19, "startDate": "2025-06-09T00:00:00", "endDate": "2025-06-15T23:59:59", "weight": 145, "activityMinutes": 175, "attendanceMode": "LIVE", "sessionDate": "2025-06-09"}, {"iteration": 20, "startDate": "2025-06-16T00:00:00", "endDate": "2025-06-22T23:59:59", "weight": 146, "activityMinutes": 300, "attendanceMode": "LIVE", "sessionDate": "2025-06-16"}, {"iteration": 21, "startDate": "2025-06-23T00:00:00", "endDate": "2025-06-29T23:59:59", "weight": 215, "activityMinutes": 155, "attendanceMode": "RECORDING", "sessionDate": "2025-06-23"}, {"iteration": 22, "startDate": "2025-06-30T00:00:00", "endDate": "2025-07-06T23:59:59", "activityMinutes": 120, "attendanceMode": "RECORDING", "sessionDate": "2025-06-30"}, {"iteration": 23, "startDate": "2025-07-07T00:00:00", "endDate": "2025-07-13T23:59:59", "attendanceMode": "RECORDING", "sessionDate": "2025-07-07"}, {"iteration": 24, "startDate": "2025-07-14T00:00:00", "endDate": "2025-07-20T23:59:59", "attendanceMode": "RECORDING", "sessionDate": "2025-07-14"}, {"iteration": 25, "startDate": "2025-07-21T00:00:00", "endDate": "2025-07-27T23:59:59", "weight": 226, "attendanceMode": "NOT_ATTENDED"}, {"iteration": 26, "startDate": "2025-07-28T00:00:00", "endDate": "2025-08-03T23:59:59", "weight": 287, "activityMinutes": 235, "attendanceMode": "LIVE", "sessionDate": "2025-07-28"}]}}}