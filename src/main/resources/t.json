{"journeyActivationXml": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<definitions xmlns=\"https://www.omg.org/spec/DMN/20191111/MODEL/\" id=\"definitions\" name=\"definitions\" namespace=\"http://camunda.org/schema/1.0/dmn\">\n  <decision id=\"decision\" name=\"Journey Recommendation\">\n    <decisionTable hitPolicy=\"COLLECT\">\n      <input id=\"input1\" label=\"Alliance\">\n        <inputExpression id=\"inputExpression1\" typeRef=\"string\">\n          <text>alliance</text>\n        </inputExpression>\n      </input>\n      <input id=\"input2\" label=\"Customer\">\n        <inputExpression id=\"inputExpression2\" typeRef=\"string\">\n          <text>customer</text>\n        </inputExpression>\n      </input>\n      <input id=\"input3\" label=\"Branch\">\n        <inputExpression id=\"inputExpression3\" typeRef=\"string\">\n          <text>branch</text>\n        </inputExpression>\n      </input>\n      <output id=\"output1\" label=\"JourneyCategory\" name=\"journeyCategory\" typeRef=\"string\" />\n      <rule id=\"DecisionRule_1wcp84p\">\n        <inputEntry id=\"UnaryTests_0asw82z\">\n          <text></text>\n        </inputEntry>\n        <inputEntry id=\"UnaryTests_04w2jej\">\n          <text></text>\n        </inputEntry>\n        <inputEntry id=\"UnaryTests_02i6iy0\">\n          <text></text>\n        </inputEntry>\n        <outputEntry id=\"LiteralExpression_0hj61gg\">\n          <text>\"Physical\"</text>\n        </outputEntry>\n      </rule>\n      <rule id=\"DecisionRule_1v3g5j7\">\n        <inputEntry id=\"UnaryTests_162xi43\">\n          <text></text>\n        </inputEntry>\n        <inputEntry id=\"UnaryTests_130cmx7\">\n          <text></text>\n        </inputEntry>\n        <inputEntry id=\"UnaryTests_0bzpa6l\">\n          <text>\"1\"</text>\n        </inputEntry>\n        <outputEntry id=\"LiteralExpression_14h9xqd\">\n          <text>\"Blue\"</text>\n        </outputEntry>\n      </rule>\n    </decisionTable>\n  </decision>\n</definitions>\n"}