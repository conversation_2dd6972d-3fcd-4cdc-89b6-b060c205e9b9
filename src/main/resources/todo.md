1. add rule in journey activation logic `start enrollment time and end enrollment time`
   `(both optional and inclusive dates)`
1. add rule in journey activation logic
   `start on next monday (start right away after enrollment) or start on next monday after enrollment end date (after everybody enrolled)`
   `(both optional)`
1. admins should be able to indicate when journeys will be visible in the app as `UPCOMING JOURNEY`
1. add preconditions rule to journey activation logic (assessment/DAS/control-center/feature-flag and so on)
1. return precondition to journey activation logic to frontend so they know which assessment/step/flag/mnemonic is
   required
1. display details for the journeys full journey duration
1. make activity/appointment as hyperlink if it is `<= current week`
1. support coaching appointment ids in activity recommendation dmn
1. support coaching appointment attandace events listen to it and based on that complete the activity
1. support activity name override config in activity recommendation dmn
1. history of old journey
1. user should be able to continue progress even if they cant finish the journey successfully but should not receive
   reward
1. configure static content for journey
1. bff service to get all appropriate details (static-content/appointment details/activity details) for the journey
1. Leave the journey support
1. actually reward the points and so on (need <PERSON>'s solution on this, we dont know yet)