<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:modeler="http://camunda.org/schema/modeler/1.0"
             id="Definitions_0lm3k5d" name="DRD" namespace="http://camunda.org/schema/1.0/dmn"
             exporter="Camunda Modeler" exporterVersion="5.31.0" modeler:executionPlatform="Camunda Cloud"
             modeler:executionPlatformVersion="8.6.0">
    <decision id="decision" name="decision">
        <decisionTable id="DecisionTable_11p63ca" hitPolicy="COLLECT">
            <input id="Input_1" label="alliance">
                <inputExpression id="InputExpression_1" typeRef="string">
                    <text>alliance</text>
                </inputExpression>
            </input>
            <input id="InputClause_0g517zt" label="group">
                <inputExpression id="LiteralExpression_1qqmmdv" typeRef="string">
                    <text>group</text>
                </inputExpression>
            </input>
            <input id="InputClause_07o5aau" label="branch">
                <inputExpression id="LiteralExpression_0gkaneq" typeRef="string">
                    <text></text>
                </inputExpression>
            </input>
            <output id="Output_1" label="journeyCategory" name="journeyCategory" typeRef="string"/>
            <rule id="DecisionRule_15c4ms4">
                <inputEntry id="UnaryTests_1e2p8gt">
                    <text></text>
                </inputEntry>
                <inputEntry id="UnaryTests_1ce0iux">
                    <text></text>
                </inputEntry>
                <inputEntry id="UnaryTests_12vu43b">
                    <text></text>
                </inputEntry>
                <outputEntry id="LiteralExpression_0umd4f9">
                    <text>"Navigating stress and burnout"</text>
                </outputEntry>
            </rule>
        </decisionTable>
    </decision>
</definitions>
