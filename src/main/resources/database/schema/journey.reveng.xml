<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE hibernate-reverse-engineering
        SYSTEM "https://hibernate.org/dtd/hibernate-reverse-engineering-3.0.dtd" >

<hibernate-reverse-engineering>

    <schema-selection match-schema="JOURNEY_IMPORT"/>

    <type-mapping>
        <sql-type jdbc-type="NUMERIC" precision="1" scale="0" not-null="true" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="1" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="2" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="3" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="4" scale="0" not-null="true" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="4" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="12" scale="2" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" precision="12" scale="2" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="2" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="3" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="4" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="5" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" precision="8" scale="0" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" precision="8" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" precision="10" scale="0" not-null="true" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" precision="10" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" not-null="true" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="INTEGER" not-null="true" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="DATE" hibernate-type="java.time.LocalDate"/>
        <sql-type jdbc-type="TIMESTAMP" hibernate-type="java.time.LocalDateTime"/>
        <sql-type jdbc-type="BLOB" hibernate-type="byte[]"/>
    </type-mapping>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="STAGING_RECORD">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.STAGING_RECORD_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="STAGING_RECORD_ERROR">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.STAGING_RECORD_ERROR_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="STAGING_TO_ENTITY_CROSSMAP">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.STAGING_TO_ENTITY_CROSSMAP_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="MEMBER">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.MEMBER_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="JOURNEY_TEMPLATE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.JOURNEY_TEMPLATE_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="JOURNEY_PROGRAM_TEMPLATE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.JOURNEY_PROGRAM_TEMPLATE_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="JOURNEY_PROGRAM_MEMBER_DATA">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.JOURNEY_PROGRAM_MEMBER_DATA_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="JOURNEY_MILESTONE_TEMPLATE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.JOURNEY_MILESTONE_TEMPLATE_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <table schema="JOURNEY_IMPORT" catalog="ENTITY" name="JOURNEY_MILESTONE_MEMBER_DATA">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="sequence_name">JOURNEY_IMPORT.JOURNEY_MILESTONE_MEMBER_DATA_SEQ</param>
                <param name="optimizer">none</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

    <!-- Exclude Spring Batch tables from entity generation -->
    <table-filter match-name="BATCH_STEP_EXECUTION" exclude="true"/>
    <table-filter match-name="BATCH_JOB_EXECUTION" exclude="true"/>
    <table-filter match-name="BATCH_JOB_INSTANCE" exclude="true"/>
    <table-filter match-name="BATCH_JOB_EXECUTION_PARAMS" exclude="true"/>
    <table-filter match-name="BATCH_JOB_EXECUTION_CONTEXT" exclude="true"/>
    <table-filter match-name="BATCH_STEP_EXECUTION_CONTEXT" exclude="true"/>

</hibernate-reverse-engineering>
