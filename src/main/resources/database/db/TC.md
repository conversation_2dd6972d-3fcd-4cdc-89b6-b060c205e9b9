# Alliance-Specific Journey Test Cases

## Background
- Today's date is "2025-06-20"
- **Alliance A**: Represented by user Alice
- **Alliance B**: Represented by user Bob
- Testing system's ability to handle alliance-specific journey configurations
- Each milestone represents exactly 1 week
- Programs are fixed - alliances can only have different completion rules

---

## Original Cross-Alliance Journeys

### JOURNEY_1: "Navigating Through the System"
- **Available to**: Both Alliance A and Alliance B
- **Journey Length**: 3 weeks
- **Enrollment Start**: "2025-06-18"
- **Enrollment End**: "2025-06-22"
- **Journey Start Time**: "2025-10-23 00:00:00"
- **Journey Precondition**: "The user must have completed NSBO activity"
- **Program_1**: "Navigating Through the System"
    - **Success Milestone End**: "Complete all mandatory activities and appointments"
    - **Milestone_1**: "Week 1"
        - **Activity_1**: "Activity 1" → "Complete the first activity"
        - **Appointment_1**: "Appointment 1" → "Attend the first appointment"
    - **Milestone_2**: "Week 2"
        - **Appointment_2**: "Appointment 2" → "Attend the second appointment"
    - **Milestone_3**: "Week 3"
        - **Activity_3**: "Activity 3" → "Complete the third activity"
        - **Appointment_3**: "Appointment 3" → "Attend the third appointment"
    - **Successful End**: "Completed At Least 2 Appointments and 1 Activity"
    - **End**: "End of Journey 1"

### JOURNEY_2: "Get Accustomed"
- **Available to**: Both Alliance A and Alliance B
- **Journey Length**: 5 weeks
- **Enrollment Start**: "2025-06-27"
- **Enrollment End**: "2025-06-29"
- **Journey Start Time**: NOT SPECIFIED so system should use next closest monday or current date if it is monday
- **Journey Precondition**: "The user must have completed NHS activity"
- **Program_2**: "Get Started"
    - **Successful Milestone End**: "Complete all mandatory activities"
    - **Milestone_1**: "Week 1"
        - **Activity_1**: "Activity 1" → "Complete the first activity"
    - **Milestone_2**: "Week 2"
        - **Activity_2**: "Activity 2" → "Complete the second activity"
    - **Milestone_3**: "Week 3"
        - **Activity_3**: "Activity 3" → "Complete the third activity"
    - **Successful End**: "Completed At Least 2 Activities"
    - **End**: "End of PROGRAM 2 - Get Started" AND Transition to PROGRAM 3 On Success
- **Program_3**: "Get Accustomed"
    - **Successful Milestone End**: "Complete all mandatory activities"
    - **Milestone_1**: "Week 1"
        - **Activity_1**: "Activity 1" → "Complete the first activity"
    - **Milestone_2**: "Week 2"
        - **Activity_2**: "Activity 2" → "Complete the second activity"
    - **Successful End**: "Completed At Least 2 Activities"
    - **End**: "End of PROGRAM 3 - Get Accustomed"
- **End of Journey 2**

### JOURNEY_3: "Ready to Use"
- **Available to**: Both Alliance A and Alliance B
- **Journey Length**: 4 weeks
- **Enrollment Start**: NOT SPECIFIED so user should be able to enroll at any time
- **Enrollment End**: NOT SPECIFIED so user should be able to enroll at any time
- **Journey Start Time**: NOT SPECIFIED so system should use next closest monday or current date if it is monday
- **Journey Precondition**: "The user must have completed NKL activity"
- **Program_4**: "Ready to Use"
    - **Successful Milestone End**: "Complete all mandatory activities"
    - **Milestone_1**: "Week 1"
        - **Activity_1**: "Activity 1" → "Complete the first activity"
    - **Milestone_2**: "Week 2"
        - **Activity_2**: "Activity 2" → "Complete the second activity"
    - **Milestone_3**: "Week 3"
        - **Activity_3**: "Activity 3" → "Complete the third activity"
    - **Milestone_4**: "Week 4"
        - **Activity_4**: "Activity 4" → "Complete the fourth activity"
    - **Successful End**: "Completed At Least 4 Activities"
    - **End of Journey 3**

### JOURNEY_4: "EXPIRED JOURNEY"
*This journey is expired and should not be available for enrollment even if the user meets the precondition and the start time is in the future*
- **Journey Length**: 4 weeks
- **Enrollment Start**: "2025-06-05"
- **Enrollment End**: "2025-06-07"
- **Journey Start Time**: "2025-10-23 00:00:00"
- **Journey Precondition**: "The user must have completed NKLS activity"

### JOURNEY_5: "COMPLETED JOURNEY"
*This journey is completed and should not be available for enrollment but the user can still see the journey details if they have completed it*
- **Journey Length**: 4 weeks
- **Enrollment Start**: "2023-05-05"
- **Enrollment End**: "2025-05-07"
- **Journey Start Time**: "2025-05-23 00:00:00"
- **Journey Precondition**: "The user must have completed LSQ activity"

---

## NEW: Alliance-Specific Test Cases

### JOURNEY_6: "Alliance A Exclusive Training"
**Available to**: Alliance A ONLY

- **Journey Length**: 6 weeks
- **Enrollment Start**: "2025-06-25"
- **Enrollment End**: "2025-07-05"
- **Journey Start Time**: "2025-08-01 00:00:00"
- **Journey Precondition**: "The user must have completed AAT activity"
- **Program_5**: "Advanced Alliance Training"
    - **Milestone_1**: "Week 1"
        - **Activity_1**: "Alliance A Orientation" → "Complete specialized orientation"
        - **Activity_2**: "A-Specific Protocol Training" → "Learn Alliance A protocols"
    - **Milestone_2**: "Week 2"
        - **Appointment_1**: "Alliance A Mentor Meeting" → "Meet with Alliance A mentor"
        - **Activity_3**: "A-Methodology Workshop" → "Complete methodology training"
    - **Milestone_3**: "Week 3"
        - **Activity_4**: "Alliance A Certification Prep" → "Prepare for certification"
    - **Milestone_4**: "Week 4"
        - **Appointment_2**: "Alliance A Assessment" → "Complete assessment"
    - **Milestone_5**: "Week 5"
        - **Activity_5**: "Alliance A Final Project" → "Complete final project"
    - **Milestone_6**: "Week 6"
        - **Appointment_3**: "Alliance A Graduation" → "Attend graduation ceremony"
- **Successful End**: "Complete at least 3 activities and 2 appointments"

### JOURNEY_7: "Alliance B Professional Development"
**Available to**: Alliance B ONLY

- **Journey Length**: 4 weeks
- **Enrollment Start**: "2025-07-10" (Different enrollment start from Journey 6)
- **Enrollment End**: "2025-07-20" (Different enrollment end from Journey 6)
- **Journey Start Time**: Next closest Monday (system calculated)
- **Journey Precondition**: "The user must have completed BPD activity"
- **Program_6**: "B-Alliance Specialization"
    - **Milestone_1**: "Week 1"
        - **Activity_1**: "Alliance B Foundation" → "Learn B-Alliance fundamentals"
    - **Milestone_2**: "Week 2"
        - **Activity_2**: "B-Specific Tools Training" → "Master Alliance B tools"
        - **Appointment_1**: "B-Alliance Supervisor Check-in" → "Meet with supervisor"
    - **Milestone_3**: "Week 3"
        - **Activity_3**: "B-Process Implementation" → "Implement B-Alliance processes"
    - **Milestone_4**: "Week 4"
        - **Activity_4**: "B-Alliance Project Completion" → "Complete final project"
        - **Appointment_2**: "B-Alliance Final Review" → "Final review session"
- **Successful End**: "Complete all 4 activities and 2 appointments"

### JOURNEY_8: "Cross-Alliance Collaboration with Skip Week"
**Available to**: Both Alliance A and Alliance B (DIFFERENT ENROLLMENT DATES & COMPLETION RULES)

#### Alliance A Configuration:
- **Journey Length**: 6 weeks
- **Enrollment Start**: "2025-06-30"
- **Enrollment End**: "2025-07-10"
- **Journey Start Time**: "2025-08-15 00:00:00"
- **Journey Precondition**: "The user must have completed CAC-A activity"

#### Alliance B Configuration:
- **Journey Length**: 6 weeks (Same program structure)
- **Enrollment Start**: "2025-07-05" (Different enrollment dates)
- **Enrollment End**: "2025-07-15" (Different enrollment dates)
- **Journey Start Time**: "2025-08-15 00:00:00"
- **Journey Precondition**: "The user must have completed CAC-B activity"

**Program_7**: "Collaboration Framework" (Same for both alliances)
- **Milestone_1**: "Week 1"
    - **Activity_A**: "Team Leadership Setup" → "Establish leadership roles"
    - **Activity_B**: "Communication Protocols" → "Set communication standards"
    - **Activity_C**: "Planning Framework" → "Create planning structure"
- **Milestone_2**: "Week 2"
    - **Appointment_1**: "Cross-Alliance Planning Session" → "Plan with stakeholders"
    - **Activity_D**: "Resource Allocation" → "Allocate project resources"
- **Milestone_3**: "Week 3"
    - **Activity_E**: "Integration Methods" → "Integrate alliance methods"
    - **Appointment_2**: "Mid-Point Review" → "Review progress"
- **Milestone_4**: "Week 4" - **SKIP WEEK**
    - *No activities or appointments scheduled - participants can rest/prepare*
- **Milestone_5**: "Week 5"
    - **Activity_F**: "Feedback Collection" → "Collect stakeholder feedback"
    - **Appointment_3**: "Stakeholder Meeting" → "Meet with key stakeholders"
- **Milestone_6**: "Week 6"
    - **Activity_G**: "Final Documentation" → "Document outcomes"
    - **Appointment_4**: "Project Closure" → "Close project formally"

**Alliance A Milestone Transition Rule**: `hasCompletedAllMandatoryActivitiesAtLeastOnce`
- For Alliance A: Only Activities are marked as MANDATORY, Appointments are OPTIONAL
- Milestone completes when all mandatory activities completed at least once
- Appointments do not block milestone progression

**Alliance B Milestone Transition Rule**: `hasMetAllConditions`
- For Alliance B: Both Activities and Appointments are marked as MANDATORY with specific required counts
- Milestone completes only when all mandatory items meet their exact required completion count
- Both activities and appointments must be completed as specified

**Activity/Appointment Flexibility Configuration**:

*Alliance A*:
- Activity_A: MANDATORY (requiredCount: 1)
- Activity_B: MANDATORY (requiredCount: 1)
- Activity_C: MANDATORY (requiredCount: 1)
- Activity_D: OPTIONAL
- Activity_E: MANDATORY (requiredCount: 1)
- Activity_F: MANDATORY (requiredCount: 1)
- Activity_G: OPTIONAL
- All Appointments: OPTIONAL

*Alliance B*:
- Activity_A: OPTIONAL
- Activity_B: OPTIONAL
- Activity_C: MANDATORY (requiredCount: 1)
- Activity_D: MANDATORY (requiredCount: 1)
- Activity_E: MANDATORY (requiredCount: 1)
- Activity_F: OPTIONAL
- Activity_G: MANDATORY (requiredCount: 1)
- Appointment_1: MANDATORY (requiredCount: 1)
- Appointment_2: MANDATORY (requiredCount: 1)
- Appointment_3: MANDATORY (requiredCount: 1)
- Appointment_4: OPTIONAL

**Alliance A Final Journey Completion Rules**: "Complete activities A, B, C, E, F (all mandatory activities)"
**Alliance B Final Journey Completion Rules**: "Complete activities C, D, E, G and attend appointments 1, 2, 3 (all mandatory items)"

### JOURNEY_9: "Multi-Stage Development"
**Available to**: Both Alliance A and Alliance B (DIFFERENT COMPLETION RULES)

- **Journey Length**: 8 weeks
- **Enrollment Start Alliance A**: "2025-07-01"
- **Enrollment End Alliance A**: "2025-07-15"
- **Enrollment Start Alliance B**: "2025-07-08"
- **Enrollment End Alliance B**: "2025-07-22"
- **Journey Start Time**: "2025-09-01 00:00:00"
- **Journey Precondition Alliance A**: "The user must have completed MSD-A activity"
- **Journey Precondition Alliance B**: "The user must have completed MSD-B activity"

**Program_8**: "Foundation Phase"
- **Milestone_1**: "Week 1"
    - **Activity_1**: "Basic Concepts" → "Learn fundamental concepts"
    - **Activity_2**: "Tool Introduction" → "Introduction to tools"
- **Milestone_2**: "Week 2"
    - **Activity_3**: "Hands-on Practice" → "Practice with tools"
    - **Appointment_1**: "Progress Check" → "Check learning progress"
- **Milestone_3**: "Week 3"
    - **Activity_4**: "Case Study Analysis" → "Analyze real cases"
- **Successful End**: Based on alliance-specific rules below
- **Transition to Program_9 on Success**

**Program_9**: "Advanced Phase"
- **Milestone_1**: "Week 1"
    - **Activity_5**: "Advanced Techniques" → "Learn advanced methods"
    - **Activity_6**: "Complex Problem Solving" → "Solve complex problems"
- **Milestone_2**: "Week 2"
    - **Appointment_2**: "Mentorship Session" → "Work with mentor"
- **Milestone_3**: "Week 3" - **SKIP WEEK**
    - *Break week for reflection and preparation*
- **Milestone_4**: "Week 4"
    - **Activity_7**: "Capstone Project" → "Complete final project"
    - **Appointment_3**: "Final Presentation" → "Present final work"
- **Milestone_5**: "Week 5"
    - **Activity_8**: "Peer Review" → "Review peer work"
    - **Appointment_4**: "Graduation Ceremony" → "Attend graduation"

**Program_8 - Alliance A Milestone Transition Rule**: `hasCompletedAllMandatoryActivitiesAtLeastOnce`
**Program_8 - Alliance B Milestone Transition Rule**: `hasMetAllConditions`

**Program_8 Activity/Appointment Flexibility Configuration**:

*Alliance A*:
- Activity_1: MANDATORY (requiredCount: 1)
- Activity_2: MANDATORY (requiredCount: 1)
- Activity_3: OPTIONAL
- Activity_4: MANDATORY (requiredCount: 1)
- Appointment_1: OPTIONAL

*Alliance B*:
- Activity_1: OPTIONAL
- Activity_2: MANDATORY (requiredCount: 1)
- Activity_3: MANDATORY (requiredCount: 1)
- Activity_4: MANDATORY (requiredCount: 1)
- Appointment_1: MANDATORY (requiredCount: 1)

**Program_9 - Alliance A Milestone Transition Rule**: `hasCompletedAllMandatoryActivitiesAtLeastOnce`
**Program_9 - Alliance B Milestone Transition Rule**: `hasMetAllConditions`

**Program_9 Activity/Appointment Flexibility Configuration**:

*Alliance A*:
- Activity_5: MANDATORY (requiredCount: 1)
- Activity_6: OPTIONAL
- Activity_7: MANDATORY (requiredCount: 1)
- Activity_8: MANDATORY (requiredCount: 1)
- Appointment_2: MANDATORY (requiredCount: 1)
- Appointment_3: MANDATORY (requiredCount: 1)
- Appointment_4: OPTIONAL

*Alliance B*:
- Activity_5: OPTIONAL
- Activity_6: MANDATORY (requiredCount: 1)
- Activity_7: MANDATORY (requiredCount: 1)
- Activity_8: MANDATORY (requiredCount: 1)
- Appointment_2: MANDATORY (requiredCount: 1)
- Appointment_3: MANDATORY (requiredCount: 1)
- Appointment_4: MANDATORY (requiredCount: 1)

**Alliance A Program_8 Final Completion Rules**: "Complete activities 1, 2, 4 (all mandatory activities)"
**Alliance B Program_8 Final Completion Rules**: "Complete activities 2, 3, 4 and attend appointment 1 (all mandatory items)"

**Alliance A Program_9 Final Completion Rules**: "Complete activities 5, 7, 8 and attend appointments 2, 3 (all mandatory items)"
**Alliance B Program_9 Final Completion Rules**: "Complete activities 6, 7, 8 and attend appointments 2, 3, 4 (all mandatory items)"

---

## Test Scenarios to Validate

### Alliance Exclusivity Tests:
1. **Alice (Alliance A)** should see JOURNEY_6 but NOT JOURNEY_7
2. **Bob (Alliance B)** should see JOURNEY_7 but NOT JOURNEY_6
3. Verify different enrollment windows for alliance-exclusive journeys

### Alliance-Specific Enrollment Tests:
1. **JOURNEY_8**: Verify Alliance A can enroll June 30-July 10, Alliance B can enroll July 5-15
2. **JOURNEY_9**: Verify different enrollment periods and preconditions
3. Test enrollment overlap periods and system handling

### Alliance-Specific Completion Rules Tests:
1. **JOURNEY_8**: Verify Alliance A must complete A,B,C + appointments 1,2,3
2. **JOURNEY_8**: Verify Alliance B must complete D,C,E + appointments 1,2,3
3. **JOURNEY_9**: Verify different program transition requirements
4. **JOURNEY_9**: Verify different final completion requirements

### Skip Week Functionality Tests:
1. **JOURNEY_8**: Verify Week 4 has no mandatory activities/appointments
2. **JOURNEY_9**: Verify Week 3 of Advanced Phase is properly handled as skip week
3. Verify system correctly advances to next milestone after skip week

### Milestone Transition Rule Tests:
1. **Alliance A (`hasCompletedAllMandatoryActivitiesAtLeastOnce`)**:
    - Verify milestones complete when all mandatory activities completed at least once
    - Verify optional appointments don't block milestone progression
    - Verify user can progress even if they complete activities multiple times
2. **Alliance B (`hasMetAllConditions`)**:
    - Verify milestones complete only when mandatory items meet exact required counts
    - Verify both mandatory activities and appointments must be completed
    - Verify milestone blocks if any mandatory item doesn't meet required count
3. **Mixed Scenarios**:
    - Test milestone progression with different completion patterns
    - Verify system correctly applies alliance-specific transition rules

### Cross-Alliance Validation Tests:
1. Verify shared journeys work identically for both alliances
2. Verify alliance-specific preconditions are enforced
3. Verify system applies correct completion rules per alliance during execution
4. 