CREATE SEQUENCE JOURNEY_IMPORT.BATCH_JOB_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE JOURNEY_IMPORT.BATCH_JOB_EXECUTION_SEQ START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE JOURNEY_IMPORT.BATCH_STEP_EXECUTION_SEQ START WITH 1 INCREMENT BY 1;

CREATE TABLE JOURNEY_IMPORT.BATCH_JOB_INSTANCE
(
    JOB_INSTANCE_ID NUMERIC(10)  NOT NULL,
    VERSION         NUMERIC(10),
    JOB_NAME        VARCHAR(100) NOT NULL,
    JOB_KEY         VARCHAR(32)  NOT NULL,
    CONSTRAINT PK_BATCH_JOB_INSTANCE PRIMARY KEY (JOB_INSTANCE_ID),
    CONSTRAINT JOB_INST_UN UNIQUE (<PERSON><PERSON><PERSON><PERSON><PERSON>AME, JO<PERSON>_KEY)
);

CREATE TABLE JOURNEY_IMPORT.BATCH_JOB_EXECUTION
(
    JOB_EXECUTION_ID NUMERIC(10) NOT NULL,
    VERSION          NUMERIC(10),
    JOB_INSTANCE_ID  NUMERIC(10) NOT NULL,
    CREATE_TIME      TIMESTAMP   NOT NULL,
    START_TIME       TIMESTAMP,
    END_TIME         TIMESTAMP,
    STATUS           VARCHAR(10),
    EXIT_CODE        VARCHAR(2500),
    EXIT_MESSAGE     VARCHAR(2500),
    LAST_UPDATED     TIMESTAMP,
    CONSTRAINT PK_BATCH_JOB_EXECUTION PRIMARY KEY (JOB_EXECUTION_ID),
    CONSTRAINT JOB_INST_EXEC_FK FOREIGN KEY (JOB_INSTANCE_ID) REFERENCES JOURNEY_IMPORT.BATCH_JOB_INSTANCE (JOB_INSTANCE_ID)
);

CREATE TABLE JOURNEY_IMPORT.BATCH_JOB_EXECUTION_PARAMS
(
    JOB_EXECUTION_ID NUMERIC(10)  NOT NULL,
    PARAMETER_NAME   VARCHAR(100) NOT NULL,
    PARAMETER_TYPE   VARCHAR(100) NOT NULL,
    PARAMETER_VALUE  VARCHAR(2500),
    IDENTIFYING      CHAR(1)      NOT NULL,
    CONSTRAINT JOB_EXEC_PARAMS_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES JOURNEY_IMPORT.BATCH_JOB_EXECUTION (JOB_EXECUTION_ID)
);

CREATE TABLE JOURNEY_IMPORT.BATCH_STEP_EXECUTION
(
    STEP_EXECUTION_ID  NUMERIC(10)  NOT NULL,
    VERSION            NUMERIC(10)  NOT NULL,
    STEP_NAME          VARCHAR(100) NOT NULL,
    JOB_EXECUTION_ID   NUMERIC(10)  NOT NULL,
    CREATE_TIME        TIMESTAMP    NOT NULL,
    START_TIME         TIMESTAMP,
    END_TIME           TIMESTAMP,
    STATUS             VARCHAR(10),
    COMMIT_COUNT       NUMERIC(10),
    READ_COUNT         NUMERIC(10),
    FILTER_COUNT       NUMERIC(10),
    WRITE_COUNT        NUMERIC(10),
    READ_SKIP_COUNT    NUMERIC(10),
    WRITE_SKIP_COUNT   NUMERIC(10),
    PROCESS_SKIP_COUNT NUMERIC(10),
    ROLLBACK_COUNT     NUMERIC(10),
    EXIT_CODE          VARCHAR(2500),
    EXIT_MESSAGE       VARCHAR(2500),
    LAST_UPDATED       TIMESTAMP,
    CONSTRAINT PK_BATCH_STEP_EXECUTION PRIMARY KEY (STEP_EXECUTION_ID),
    CONSTRAINT JOB_EXEC_STEP_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES JOURNEY_IMPORT.BATCH_JOB_EXECUTION (JOB_EXECUTION_ID)
);

CREATE TABLE JOURNEY_IMPORT.BATCH_JOB_EXECUTION_CONTEXT
(
    JOB_EXECUTION_ID   NUMERIC(10)   NOT NULL,
    SHORT_CONTEXT      VARCHAR(2500) NOT NULL,
    SERIALIZED_CONTEXT CLOB,
    CONSTRAINT JOB_EXEC_CTX_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES JOURNEY_IMPORT.BATCH_JOB_EXECUTION (JOB_EXECUTION_ID)
);

CREATE TABLE JOURNEY_IMPORT.BATCH_STEP_EXECUTION_CONTEXT
(
    STEP_EXECUTION_ID  NUMERIC(10)   NOT NULL,
    SHORT_CONTEXT      VARCHAR(2500) NOT NULL,
    SERIALIZED_CONTEXT CLOB,
    CONSTRAINT STEP_EXEC_CTX_FK FOREIGN KEY (STEP_EXECUTION_ID) REFERENCES JOURNEY_IMPORT.BATCH_STEP_EXECUTION (STEP_EXECUTION_ID)
);

-- Indexes for performance
CREATE INDEX JOB_EXEC_INST_FK_IDX ON JOURNEY_IMPORT.BATCH_JOB_EXECUTION (JOB_INSTANCE_ID);
CREATE INDEX JOB_EXEC_PARAMS_FK_IDX ON JOURNEY_IMPORT.BATCH_JOB_EXECUTION_PARAMS (JOB_EXECUTION_ID);
CREATE INDEX STEP_EXEC_JOB_FK_IDX ON JOURNEY_IMPORT.BATCH_STEP_EXECUTION (JOB_EXECUTION_ID);