CREATE SCHEMA JOURNEY;

CREATE SEQUENCE JOURNEY.JOURNEY_CATEGORY_TYPE_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_CUSTOMER_DEFINITION_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_CATEGORY_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_RULES_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_MILESTONE_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_MILESTONE_SEQ START WITH 10000;

CREATE TABLE JOURNEY.JOURNEY_CUSTOMER_DEFINITION
(
    CUSTOMER_DEFINITION_ID NUMERIC(15) PRIMARY KEY,
    ALLIANCE               VARCHAR(255),
    GROUP_ID               VARCHAR(255),
    BRANCH                 VARCHAR(255)
);

-- Used to manage the different types of programs that can be created or categories/dimensions we want to assign to a category
CREATE TABLE JOURNEY.JOURNEY_CATEGORY_TYPE
(
    JOURNEY_CATEGORY_TYPE_ID NUMERIC(15) PRIMARY KEY, -- 1
    NAME                     VARCHAR(255) NOT NULL    -- GROUP
);

-- Used to define the different categories that can be assigned to a program
CREATE TABLE JOURNEY.JOURNEY_CATEGORY
(
    JOURNEY_CATEGORY_ID      NUMERIC(15) PRIMARY KEY, --1
    NAME                     VARCHAR(255) NOT NULL,   -- Weight Management CATEGORY
    RESOURCE_CODE            VARCHAR(255),            -- 1
    JOURNEY_CATEGORY_TYPE_ID NUMERIC(15) NOT NULL,    -- 1

    CONSTRAINT JOURNEY_CATEGORY_FK
        FOREIGN KEY (JOURNEY_CATEGORY_TYPE_ID)
            REFERENCES JOURNEY.JOURNEY_CATEGORY_TYPE (JOURNEY_CATEGORY_TYPE_ID)
);

-- Used to define the different rules that can be used to determine if a program can be recommended to a customer, the activities, the transitions and the next program
CREATE TABLE JOURNEY.JOURNEY_RULES
(
    RULE_ID NUMERIC(15) PRIMARY KEY,       --1
    RULE_SET_NAME VARCHAR(255) NOT NULL,   -- ACTIVITIES TO RECOMMEND
    RULE_SET_TYPE VARCHAR(255) NOT NULL,   -- DMN, SQL, JEXL
    RULE_SET      bytea

);

--  Used to define the different programs that can be created for instance weight management
CREATE TABLE JOURNEY.JOURNEY_PROGRAM
(
    JOURNEY_PROGRAM_ID NUMERIC(15) PRIMARY KEY, --1
    NAME               VARCHAR(255) NOT NULL,   -- Weight Management
    DESCRIPTION        VARCHAR(255),            -- Weight Management Program To Manage Weight,
    RESOURCE_CODE      VARCHAR(255),            -- 1
    STATUS             VARCHAR(255) NOT NULL,   -- ACTIVE, INACTIVE
    EFF_FROM           TIMESTAMP,               -- 2021-08-01 00:00:00
    EFF_TO             TIMESTAMP                -- 2021-08-01 00:00:00
);

CREATE TABLE JOURNEY.JOURNEY_MILESTONE
(
    JOURNEY_MILESTONE_ID NUMERIC(15) PRIMARY KEY, --1
    DESCRIPTION          VARCHAR(255) NOT NULL    -- WEEKS
);

CREATE TABLE JOURNEY.JOURNEY_PROGRAM_BEHAVIOUR
(
    JOURNEY_PROGRAM_ID                       NUMERIC(15) PRIMARY KEY, --1
    PROGRAM_DURATION                         NUMERIC(15) NOT NULL,    -- HOW LONG THE PROGRAM RUNS -- WEEKS
    JOURNEY_MILESTONE_ID                     NUMERIC(255) NOT NULL,   -- WEEKLY, MONTHLY
    PROGRAM_COMPLETION_RULES_ID              NUMERIC(15),             -- HOW DO WE KNOW THIS PROGRAM IS COMPLETED
    MILESTONE_TRANSITION_RULES_ID            NUMERIC(15),             -- HOW DO I TRANSITION TO THE NEXT MILESTONE -- COULD THIS BE A SETTINGS TABLE?
    PROGRAM_ACTIVITY_RECOMMENDATION_RULES_ID NUMERIC(15),             -- HOW DO I RECOMMEND ACTIVITIES (INTERNAL RULES EVENT QUANTIUM AS A RULESET)

    CONSTRAINT JOURNEY_PROGRAM_BEHAVIOUR_FK5
        FOREIGN KEY (JOURNEY_MILESTONE_ID)
            REFERENCES JOURNEY.JOURNEY_MILESTONE (JOURNEY_MILESTONE_ID),

    CONSTRAINT JOURNEY_PROGRAM_BEHAVIOUR_FK
        FOREIGN KEY (JOURNEY_PROGRAM_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),

    CONSTRAINT JOURNEY_PROGRAM_BEHAVIOUR_FK2
        FOREIGN KEY (MILESTONE_TRANSITION_RULES_ID)
            REFERENCES JOURNEY.JOURNEY_RULES (RULE_ID),

    CONSTRAINT JOURNEY_PROGRAM_BEHAVIOUR_FK3
        FOREIGN KEY (PROGRAM_ACTIVITY_RECOMMENDATION_RULES_ID)
            REFERENCES JOURNEY.JOURNEY_RULES (RULE_ID),

    CONSTRAINT JOURNEY_PROGRAM_BEHAVIOUR_FK4
        FOREIGN KEY (PROGRAM_COMPLETION_RULES_ID)
            REFERENCES JOURNEY.JOURNEY_RULES (RULE_ID)

);


-- Used to associate the program to a specific category for a period of time
CREATE TABLE JOURNEY.JOURNEY_CATEGORIZATION
(
    JOURNEY_PROGRAM_ID  NUMERIC(15) NOT NULL,  -- 1
    JOURNEY_CATEGORY_ID NUMERIC(15) NOT NULL,  -- 1
    EFF_FROM            TIMESTAMP,             -- 2021-08-01 00:00:00
    EFF_TO              TIMESTAMP,             -- 2021-08-01 00:00:00
    STATUS              VARCHAR(255) NOT NULL, -- ACTIVE, INACTIVE

    CONSTRAINT JOURNEY_CATEGORIZATION_PK PRIMARY KEY (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM),
    CONSTRAINT JOURNEY_CATEGORIZATION_FK
        FOREIGN KEY (JOURNEY_PROGRAM_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),

    CONSTRAINT JOURNEY_CATEGORIZATION_FK2
        FOREIGN KEY (JOURNEY_CATEGORY_ID)
            REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)
);

-- HOW THE PROGRAM BEHAVES, WHAT ARE THE TRANSITIONS, THE RULES, THE MILESTONES
CREATE TABLE JOURNEY.JOURNEY_RECOMMENDATION_ORDER
(
    JOURNEY_CATEGORY_ID NUMERIC(15) NOT NULL,
    JOURNEY_PROGRAM_ID  NUMERIC(15) NOT NULL,
    PROGRAM_ORDER       NUMERIC(15) NOT NULL,
    EFF_FROM            TIMESTAMP,
    EFF_TO              TIMESTAMP,
    STATUS              VARCHAR(255) NOT NULL,

    CONSTRAINT JOURNEY_RECOMMENDATION_ORDER_PK
        PRIMARY KEY (JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID),

    CONSTRAINT JOURNEY_RECOMMENDATION_ORDER_FK4
        FOREIGN KEY (JOURNEY_PROGRAM_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),

    CONSTRAINT JOURNEY_RECOMMENDATION_ORDER_FK5
        FOREIGN KEY (JOURNEY_CATEGORY_ID)
            REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)
);


-- Defines the configuration for how milestones will be handled, for this a milestone could be 1 to 22 iterations of a Week, ie 22 WEEK PROGRAM
CREATE TABLE JOURNEY.JOURNEY_PROGRAM_MILESTONE
(
    JOURNEY_PROGRAM_MILESTONE_ID NUMERIC(15) PRIMARY KEY, --1
    MILESTONE_RANGE_FROM         NUMERIC(15) NOT NULL,    -- 1
    MILESTONE_RANGE_TO           NUMERIC(15) NOT NULL,    -- 22
    JOURNEY_MILESTONE_ID         NUMERIC(15) NOT NULL,    -- 1

    CONSTRAINT JOURNEY_PROGRAM_MILESTONE_FK
        FOREIGN KEY (JOURNEY_MILESTONE_ID)
            REFERENCES JOURNEY.JOURNEY_MILESTONE (JOURNEY_MILESTONE_ID)

);

-- REWARD SECTION
-- CREATE CUSTOMER VALUES FOR EACH REWARD LEVEL FOR EACH CUSTOMER UPON COMPLETION for each milestone
CREATE TABLE JOURNEY.JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION
(                                                            --FIXED REWARD
    JOURNEY_REWARD_CUSTOMIZATION_ID NUMERIC(15) PRIMARY KEY, --1
    CUSTOMER_ID                     NUMERIC(15) NOT NULL,    -- 1
    JOURNEY_PROGRAM_ID              NUMERIC(15) NOT NULL,    -- 1
    JOURNEY_CATEGORY_ID             NUMERIC(15) NOT NULL,    -- 1
    JOURNEY_PROGRAM_MILESTONE_ID    NUMERIC(15) NOT NULL,    -- 1 EVERY WEEK GET A REWARD, BUT WHAT ABOUT MILESTONE NUMBERS UPON EACH MILESTONE TYPE COMPLETION AWARD THIS
    REWARD_TYPE                     VARCHAR(255) NOT NULL,   -- POINTS, BADGE, CERTIFICATE
    REWARD_VALUE                    VARCHAR(50)  NOT NULL,   -- 100
    EFF_FROM                        TIMESTAMP,               -- 2021-08-01 00:00:00
    EFF_TO                          TIMESTAMP,               -- 2021-08-01 00:00:00

    CONSTRAINT JOURNEY_REWARD_CUSTOMIZATION_FK
        FOREIGN KEY (JOURNEY_PROGRAM_MILESTONE_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM_MILESTONE (JOURNEY_PROGRAM_MILESTONE_ID),
    CONSTRAINT JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_FK2
        FOREIGN KEY (CUSTOMER_ID)
            REFERENCES JOURNEY.JOURNEY_CUSTOMER_DEFINITION (CUSTOMER_DEFINITION_ID),
    CONSTRAINT JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_FK3
        FOREIGN KEY (JOURNEY_PROGRAM_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),
    CONSTRAINT JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_FK4
        FOREIGN KEY (JOURNEY_CATEGORY_ID)
            REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)
);
---
-- Assign a reward to the completion of a program for a customer
CREATE TABLE JOURNEY.JOURNEY_PROGRAM_REWARD_CUSTOMIZATION
(                                                            --FIXED REWARD
    JOURNEY_REWARD_CUSTOMIZATION_ID NUMERIC(15) PRIMARY KEY, -- 1
    CUSTOMER_ID                     NUMERIC(15) NOT NULL,    -- 1
    JOURNEY_PROGRAM_ID              NUMERIC(15) NOT NULL,    -- 1 EVERY WEEK GET A REWARD, BUT WHAT ABOUT MILESTONE NUMBERS
    JOURNEY_CATEGORY_ID             NUMERIC(15) NOT NULL,    -- 1
    REWARD_TYPE                     VARCHAR(255) NOT NULL,   -- POINTS, BADGE, CERTIFICATE
    REWARD_VALUE                    VARCHAR(50)  NOT NULL,   -- 100
    EFF_FROM                        TIMESTAMP,               -- 2021-08-01 00:00:00
    EFF_TO                          TIMESTAMP,               -- 2021-08-01 00:00:00
    CONSTRAINT JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_FK2
        FOREIGN KEY (CUSTOMER_ID)
            REFERENCES JOURNEY.JOURNEY_CUSTOMER_DEFINITION (CUSTOMER_DEFINITION_ID),

    CONSTRAINT JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_FK
        FOREIGN KEY (JOURNEY_PROGRAM_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),

    CONSTRAINT JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_FK3
        FOREIGN KEY (JOURNEY_CATEGORY_ID)
            REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)
);
