CREATE SEQUENCE JOURNEY.JOURNEY_ENROLLMENT_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_AWARD_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_ENROLLMENT_PROGRAM_AWARD_SEQ START WITH 10000;
CREATE SEQUENCE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_SEQ START WITH 10000;


-- Which program each customer is enrolled in
CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT
(
    JOURNEY_ENROLLMENT_ID       NUMERIC(15) PRIMARY KEY, --1
    ENTITY_ID                   NUMERIC(15)  NOT NULL,   -- 1234566879 NATHAN
    JOURNEY_CATEGORY_ID         NUMERIC(15)  NOT NULL,   -- 1
    JOURNEY_PROGRAM_ID          NUMERIC(15)  NOT NULL,   -- 1
    ENROLLMENT_DATE             TIMESTAMP,
    TERMINATION_DATE            TIMESTAMP,
    STATUS                      VARCHAR(255) NOT NULL,   -- ACTIVE, INACTIVE
    TRANSITION_TO_ENROLLMENT_ID NUMERIC(15)  NULL,       -- 2


    CONSTRAINT JOURNEY_ENROLLMENT_FK
        FOREIGN KEY (JOURNEY_PROGRAM_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),

    CONSTRAINT JOURNEY_ENROLLMENT_FK2
        FOREIGN KEY (TRANSITION_TO_ENROLLMENT_ID)
            REFERENCES JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID),

    CONSTRAINT JOURNEY_ENROLLMENT_FK3
        FOREIGN KEY (JOURNEY_CATEGORY_ID)
            REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)

);

-- Keep track of the reward given to the customer for that program associated
CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT_PROGRAM_AWARD
(
    JOURNEY_ENROLLMENT_PROGRAM_AWARD_ID NUMERIC(15) PRIMARY KEY, --1
    JOURNEY_ENROLLMENT_ID               NUMERIC(15)  NOT NULL,   --1
    AWARD_DATE                          TIMESTAMP,               -- 2021-08-01 00:00:00
    REWARD_TYPE                         VARCHAR(255) NOT NULL,   -- POINTS, BADGE, CERTIFICATE
    REWARD_VALUE                        VARCHAR(50)  NOT NULL,   -- 100
    EXT_REWARD_REF                      VARCHAR(255),            -- 100
    CONSTRAINT JOURNEY_ENROLLMENT_PROGRAM_AWARD_FK
        FOREIGN KEY (JOURNEY_ENROLLMENT_ID)
            REFERENCES JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID)
);

-- A header table to keep track of the different milestones that the person has to complete, and what is their status, and if completed
CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE
(
    JOURNEY_ENROLLMENT_MILESTONE_ID NUMERIC(15) PRIMARY KEY, --1 SEQUENCE
    JOURNEY_ENROLLMENT_ID           NUMERIC(15)  NOT NULL,   -- 1
    MILESTONE_ITERATION             NUMERIC(15)  NOT NULL,   -- 1 (Keeps track of ordering)
    MILESTONE_FROM                  TIMESTAMP,               -- 2021-08-01 00:00:00
    MILESTONE_TO                    TIMESTAMP,               -- 2021-08-01 00:00:00
    MILESTONE_STATUS                VARCHAR(255) NOT NULL,   -- MEETS, IN-PROGRESS, DID NOT MEET
    MILESTONE_COMPLETED_DATE        TIMESTAMP,               -- 2021-08-01 00:00:00
    CONSTRAINT JOURNEY_ENROLLMENT_MILESTONE_FK
        FOREIGN KEY (JOURNEY_ENROLLMENT_ID)
            REFERENCES JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID)

);


-- Keep track of the reward given to the customer for that milestone associated
CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_AWARD
(
    JOURNEY_ENROLLMENT_MILESTONE_AWARD_ID NUMERIC(15) PRIMARY KEY, --1
    JOURNEY_ENROLLMENT_MILESTONE_ID       NUMERIC(15)  NOT NULL,   -- 1
    AWARD_DATE                            TIMESTAMP,               -- 2021-08-01 00:00:00
    REWARD_TYPE                           VARCHAR(255) NOT NULL,   -- POINTS, BADGE, CERTIFICATE
    REWARD_VALUE                          VARCHAR(50)  NOT NULL,   -- 100
    EXT_REWARD_REF                        VARCHAR(255),            -- 100
    CONSTRAINT JOURNEY_ENROLLMENT_MILESTONE_AWARD_FK
        FOREIGN KEY (JOURNEY_ENROLLMENT_MILESTONE_ID) REFERENCES
            JOURNEY.JOURNEY_ENROLLMENT_MILESTONE (JOURNEY_ENROLLMENT_MILESTONE_ID)
);

CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY
(
    JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID NUMERIC(15) PRIMARY KEY, --1
    JOURNEY_ENROLLMENT_MILESTONE_ID          NUMERIC(15)  NOT NULL,   -- 1
    ACTIVITY_ALLOCATED_BY                    NUMERIC(15)  NOT NULL,   -- SYSTEM, COACH
    ACTIVITY_MNEMONIC_ID                     VARCHAR(255) NOT NULL,   -- 1
    ACTIVITY_STATUS                          VARCHAR(255) NOT NULL,   -- COMPLETED, INCOMPLETE
    ACTIVITY_AMOUNT                          NUMERIC(15)  NOT NULL,   -- 1
    ACTIVITY_COMPLETION_COUNT                NUMERIC(15)  NOT NULL,   -- 1
    ACTIVITY_FLEXIBILITY                     VARCHAR(5)   NOT NULL,   -- M, O    -- COULD ADD IN A REWARD FOR THE ITEM
    CONSTRAINT JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_FK
        FOREIGN KEY (JOURNEY_ENROLLMENT_MILESTONE_ID) REFERENCES
            JOURNEY.JOURNEY_ENROLLMENT_MILESTONE (JOURNEY_ENROLLMENT_MILESTONE_ID)
);

CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN
(
    JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN_ID NUMERIC(15) PRIMARY KEY, --1
    JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID     NUMERIC(15),             --1
    ACTIVITY_COMPLETED_DATE                      TIMESTAMP,               -- 2021-08-01 00:00:00

    CONSTRAINT JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN_FK
        FOREIGN KEY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID)
            REFERENCES JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID)
);
