create sequence JOURNEY.JOURNEY_ELIGIBILITY_SNAPSHOT_SEQ start with 10000;

CREATE TABLE JOURNEY.JOURNEY_ELIGIBILITY_SNAPSHOT
(
    JOURNEY_ELIGIBILITY_SNAPSHOT_ID NUMERIC(15) PRIMARY KEY,
    <PERSON><PERSON>URNEY_NAME VARCHAR(255) NOT NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON> VARCHAR(100),
    CUSTOMER VARCHAR(100),
    BRANCH VARCHAR(100),
    SNAPSHOT_TIMESTAMP TIMESTAMP NOT NULL,

    CONSTRAINT UK_SNAPSHOT_RULE UNIQUE (JO<PERSON>NEY_NAME, ALLIANC<PERSON>, CUSTOMER, <PERSON><PERSON><PERSON>)
);


create sequence JOURNEY.USER_JOURNEY_NOTIFICATION_SEQ start with 10000;

CREATE TABLE JOURNEY.USER_JOURNEY_NOTIFICATION
(
    USER_JOURNEY_NOTIFICATION_ID NUMERIC(15) PRIMARY KEY,
    J<PERSON><PERSON>NEY_CATEGORY_ID NUMERIC(15) NOT NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON> VARCHAR(100),
    <PERSON><PERSON><PERSON><PERSON><PERSON> VARCHAR(100),
    <PERSON><PERSON><PERSON> VARCHAR(100),
    E<PERSON>OLLMENT_START_TIME TIMESTAMP NOT NULL,
    ENROLLMENT_END_TIME TIMESTAMP NOT NULL,
    NOTIFICATION_STATUS VARCHAR(50) NOT NULL,
    NOTIFIED_AT TIMESTAMP,

--    CONSTRAINT UK_USER_JOURNEY UNIQUE (ALLIANCE, CUSTOMER, BRANCH, JOURNEY_CATEGORY_ID, ENROLLMENT_START_TIME),
    CONSTRAINT JOURNEY_CATEGORY_NOTIFICATION_FK
            FOREIGN KEY (JOURNEY_CATEGORY_ID)
                REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)
);
