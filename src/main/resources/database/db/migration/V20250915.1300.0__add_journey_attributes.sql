CREATE SEQUENCE JOURNEY.JOURNEY_ENROLLMENT_ATTRIBUTE_SEQ START WITH 10000;

CREATE TABLE JOURNEY.JOURNEY_ENROLLMENT_ATTRIBUTE
(
    ID                    NUMERIC(10)  NOT NULL,
    JOURNEY_ENROLLMENT_ID NUMERIC(10)  NOT NULL,
    ATTR_NAME             VARCHAR(50)  NOT NULL,
    ATTR_VALUE            VARCHAR(255) NOT NULL,
    CONSTRAINT JOURNEY_ENROLLMENT_ATTRIBUTE_PK
        PRIMARY KEY (ID),
    CONSTRAINT JOURNEY_ENROLLMENT_ATTRIBUTE_FK
        FOREIGN KEY (JOURNEY_ENROLLMENT_ID)
            REFERENCES JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID)
);
