CREATE TABLE JOURNEY_IMPORT.STAGING_TO_ENTITY_CROSSMAP
(
    IMPORT_JOB_ID     NUMERIC(10),
    STAGING_RECORD_ID NUMERIC(10) NOT NULL,
    ENTITY_REF_ID     NUMERIC(10) NOT NULL,
    ENTITY_REF_TYPE   VARCHAR(50) NOT NULL,
    CREATED_AT        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_STAGING_TO_ENTITY_CROSSMAP PRIMARY KEY (IMPORT_JOB_ID, STAGING_RECORD_ID, ENTITY_REF_ID, ENTITY_REF_TYPE)
);
