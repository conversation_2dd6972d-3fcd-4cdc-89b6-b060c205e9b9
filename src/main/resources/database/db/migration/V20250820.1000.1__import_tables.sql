CREATE TABLE JOURNEY_IMPORT.IMPORT_JOB
(
    ID            NUMERIC(10)  NOT NULL,
    RESOURCE_NAME VARCHAR(100) NOT NULL,
    RESOURCE_TYPE VARCHAR(4)   NOT NULL,
    <PERSON><PERSON><PERSON><PERSON>YER_ID   NUMERIC(10)  NOT NULL,
    DATA_TYPE     VARCHAR(10)  NOT NULL,
    STATUS        VARCHAR(10)  NOT NULL,
    STARTED_AT    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FINISHED_AT   TIMESTAMP,
    CONSTRAINT PK_IMPORT_JOB PRIMARY KEY (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.IMPORT_JOB_SEQ START WITH 1 INCREMENT BY 1;

CREATE TABLE JOURNEY_IMPORT.IMPORT_JOB_STEP
(
    ID              NUMERIC(10) NOT NULL,
    IMPORT_JOB_ID   NUMERIC(10) NOT NULL,
    STEP_CODE       VARCHAR(20) NOT NULL,
    STATUS          VARCHAR(10) NOT NULL,
    STARTED_AT      TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,
    FINISHED_AT     TIMESTAMP,
    CONSTRAINT PK_IMPORT_JOB_STEP PRIMARY KEY (ID),
    CONSTRAINT FK_IMPORT_JOB FOREIGN KEY (IMPORT_JOB_ID)
        REFERENCES JOURNEY_IMPORT.IMPORT_JOB (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.IMPORT_JOB_STEP_SEQ START WITH 1 INCREMENT BY 1;