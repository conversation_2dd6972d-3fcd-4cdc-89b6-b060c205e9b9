CREATE TABLE JOURNEY_IMPORT.MEMBER
(
    ID             NUMERIC(10)  NOT NULL,
    UNIQUE_ID      VARCHAR(20)  NOT NULL,
    EMPLOYER_ID    NUMERIC(10)  NOT NULL,
    CDC_IDENTIFIER VARCHAR(255),
    FIRST_NAME     VARCHAR(255) NOT NULL,
    LAST_NAME      VARCHAR(255) NOT NULL,
    ENTITY_NO      NUMERIC(10),
    CREATED_AT     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT     TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_MEMBER PRIMARY KEY (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.MEMBER_SEQ START WITH 1 INCREMENT BY 1;

CREATE INDEX IDX_UNQ_MEMBER_EMPLOYER ON JOURNEY_IMPORT.MEMBER (UNIQUE_ID, EMPLOYER_ID);
CREATE INDEX IDX_MEMBER_ENTITY_NO ON JOURNEY_IMPORT.MEMBER (ENTITY_NO);

CREATE TABLE JOURNEY_IMPORT.JOURNEY_TEMPLATE
(
    ID             NUMERIC(10) NOT NULL,
    JOURNEY_TYPE   VARCHAR(10) NOT NULL,
    MILESTONE_TYPE VARCHAR(10) NOT NULL,
    CREATED_AT     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT     TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_JOURNEY_TEMPLATE PRIMARY KEY (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.JOURNEY_TEMPLATE_SEQ START WITH 1 INCREMENT BY 1;

CREATE UNIQUE INDEX IDX_JT_UNQ_TYPE ON JOURNEY_IMPORT.JOURNEY_TEMPLATE (JOURNEY_TYPE, MILESTONE_TYPE);

CREATE TABLE JOURNEY_IMPORT.JOURNEY_PROGRAM_TEMPLATE
(
    ID                  NUMERIC(10)  NOT NULL,
    JOURNEY_TEMPLATE_ID NUMERIC(10),
    NAME                VARCHAR(255) NOT NULL,
    START_DATE          TIMESTAMP    NOT NULL,
    END_DATE            TIMESTAMP    NOT NULL,
    CREATED_AT          TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT          TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_JOURNEY_PROGRAM_TEMPLATE PRIMARY KEY (ID),
    CONSTRAINT FK_JPT_JT
        FOREIGN KEY (JOURNEY_TEMPLATE_ID)
            REFERENCES JOURNEY_IMPORT.JOURNEY_TEMPLATE (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.JOURNEY_PROGRAM_TEMPLATE_SEQ START WITH 1 INCREMENT BY 1;

CREATE UNIQUE INDEX IDX_JPT_UNQ ON JOURNEY_IMPORT.JOURNEY_PROGRAM_TEMPLATE (JOURNEY_TEMPLATE_ID, NAME, START_DATE, END_DATE);

CREATE TABLE JOURNEY_IMPORT.JOURNEY_MILESTONE_TEMPLATE
(
    ID                          NUMERIC(10) NOT NULL,
    JOURNEY_PROGRAM_TEMPLATE_ID NUMERIC(10),
    ITERATION                   NUMERIC(3),
    START_DATE                  TIMESTAMP   NOT NULL,
    END_DATE                    TIMESTAMP   NOT NULL,
    CREATED_AT                  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT                  TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_JOURNEY_MILESTONE_TEMPLATE PRIMARY KEY (ID),
    CONSTRAINT FK_JMT_JPT
        FOREIGN KEY (JOURNEY_PROGRAM_TEMPLATE_ID)
            REFERENCES JOURNEY_IMPORT.JOURNEY_PROGRAM_TEMPLATE (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.JOURNEY_MILESTONE_TEMPLATE_SEQ START WITH 1 INCREMENT BY 1;

CREATE UNIQUE INDEX IDX_JMT_PT_ID_ITER ON JOURNEY_IMPORT.JOURNEY_MILESTONE_TEMPLATE (JOURNEY_PROGRAM_TEMPLATE_ID, ITERATION);

CREATE TABLE JOURNEY_IMPORT.JOURNEY_PROGRAM_MEMBER_DATA
(
    ID                          NUMERIC(10)   NOT NULL,
    MEMBER_ID                   NUMERIC(10)   NOT NULL,
    JOURNEY_PROGRAM_TEMPLATE_ID NUMERIC(10),
    MEMBER_START_DATE           DATE,
    MEMBER_LAST_ATTENDED_DATE   DATE,
    STARTING_WEIGHT             NUMERIC(5, 2) NOT NULL,
    TARGET_WEIGHT               NUMERIC(5, 2) NOT NULL,
    WEIGHT_UNIT                 VARCHAR(3)    NOT NULL,
    CREATED_AT                  TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT                  TIMESTAMP              DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_JPMD PRIMARY KEY (ID),
    CONSTRAINT FK_JPMD_MEMBER
        FOREIGN KEY (MEMBER_ID)
            REFERENCES JOURNEY_IMPORT.MEMBER (ID),
    CONSTRAINT FK_JPMD_PROGRAM_TEMPLATE
        FOREIGN KEY (JOURNEY_PROGRAM_TEMPLATE_ID)
            REFERENCES JOURNEY_IMPORT.JOURNEY_PROGRAM_TEMPLATE (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.JOURNEY_PROGRAM_MEMBER_DATA_SEQ START WITH 1 INCREMENT BY 1;

CREATE UNIQUE INDEX IDX_JPMD_MEMBER_PROGRAM ON JOURNEY_IMPORT.JOURNEY_PROGRAM_MEMBER_DATA (MEMBER_ID, JOURNEY_PROGRAM_TEMPLATE_ID);

CREATE TABLE JOURNEY_IMPORT.JOURNEY_MILESTONE_MEMBER_DATA
(
    ID                             NUMERIC(10) NOT NULL,
    MEMBER_ID                      NUMERIC(10) NOT NULL,
    JOURNEY_PROGRAM_MEMBER_DATA_ID NUMERIC(10) NOT NULL,
    JOURNEY_MILESTONE_TEMPLATE_ID  NUMERIC(10),
    WEIGHT                         NUMERIC(5, 2),
    WEIGHT_UNIT                    VARCHAR(3),
    PHYSICAL_ACTIVITY_MIN          NUMERIC(5),
    SESSION_ATTENDED_DATE          DATE,
    SESSION_ATTENDANCE_MODE        VARCHAR(50) NOT NULL,
    CREATED_AT                     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT                     TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT PK_JMMD PRIMARY KEY (ID),
    CONSTRAINT FK_JMMD_MEMBER
        FOREIGN KEY (MEMBER_ID)
            REFERENCES JOURNEY_IMPORT.MEMBER (ID),
    CONSTRAINT FK_JMMD_MILESTONE
        FOREIGN KEY (JOURNEY_MILESTONE_TEMPLATE_ID)
            REFERENCES JOURNEY_IMPORT.JOURNEY_MILESTONE_TEMPLATE (ID),
    CONSTRAINT FK_JMMD_PROGRAM_MEMBER_DATA
        FOREIGN KEY (JOURNEY_PROGRAM_MEMBER_DATA_ID)
            REFERENCES JOURNEY_IMPORT.JOURNEY_PROGRAM_MEMBER_DATA (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.JOURNEY_MILESTONE_MEMBER_DATA_SEQ START WITH 1 INCREMENT BY 1;

CREATE UNIQUE INDEX IDX_JMMD_MEMBER_MILESTONE ON JOURNEY_IMPORT.JOURNEY_MILESTONE_MEMBER_DATA (MEMBER_ID,
                                                                                               JOURNEY_PROGRAM_MEMBER_DATA_ID,
                                                                                               JOURNEY_MILESTONE_TEMPLATE_ID);
