CREATE TABLE JOURNEY_IMPORT.STAGING_RECORD
(
    ID            NUMERIC(10) NOT NULL,
    IMPORT_JOB_ID NUMERIC(10),
    TYPE          VARCHAR(4)  NOT NULL,
    STATUS        VARCHAR(50) NOT NULL,
    CREATED_AT    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PAYLOAD       CLOB        NOT NULL,

    CONSTRAINT PK_STAGING_RECORD PRIMARY KEY (ID),
    CONSTRAINT FK_STAGING_IMPORT_JOB FOREIGN KEY (IMPORT_JOB_ID)
        REFERENCES JOURNEY_IMPORT.IMPORT_JOB (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.STAGING_RECORD_SEQ START WITH 1 INCREMENT BY 1;

CREATE INDEX IDX_STAGING_RECORD_IMPORT_JOB_ID ON JOURNEY_IMPORT.STAGING_RECORD (IMPORT_JOB_ID);

CREATE TABLE JOURNEY_IMPORT.STAGING_RECORD_ERROR
(
    ID                NUMERIC(10) NOT NULL,
    STEP_ID           NUMERIC(10),
    STAGING_RECORD_ID NUMERIC(10) NOT NULL,
    ERROR_MESSAGE     CLOB,
    CREATED_AT        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT PK_STAGING_RECORD_STATUS PRIMARY KEY (ID),
    CONSTRAINT FK_STAGING_RECORD FOREIGN KEY (STAGING_RECORD_ID)
        REFERENCES JOURNEY_IMPORT.STAGING_RECORD (ID),
    CONSTRAINT FK_STAGING_JOB_STEP FOREIGN KEY (STEP_ID)
        REFERENCES JOURNEY_IMPORT.IMPORT_JOB_STEP (ID)
);

CREATE SEQUENCE JOURNEY_IMPORT.STAGING_RECORD_ERROR_SEQ START WITH 1 INCREMENT BY 1;