INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_<PERSON>NROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRA<PERSON>ITION_TO_ENROLLMENT_ID) VALUES (10003, 1425421763, 10000, 10001, '2025-05-29 15:56:20', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10004, 1425478136, 10000, 10001, '2025-05-29 16:16:14', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, <PERSON><PERSON><PERSON>Y_ID, JO<PERSON>NEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10005, 1425478144, 10000, 10001, '2025-05-29 16:55:35', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10006, 1424541843, 10000, 10001, '2025-05-30 00:11:24', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10007, 1425442587, 10000, 10001, '2025-05-30 05:20:21', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10008, 1424586954, 10000, 10001, '2025-06-05 12:21:05', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10009, 1425478144, 10003, 10005, '2025-06-06 07:09:30', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10010, 1423117017, 10003, 10005, '2025-06-06 07:28:31', null, 'ACTIVE', null);
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT (JOURNEY_ENROLLMENT_ID, ENTITY_ID, JOURNEY_CATEGORY_ID, JOURNEY_PROGRAM_ID, ENROLLMENT_DATE, TERMINATION_DATE, STATUS, TRANSITION_TO_ENROLLMENT_ID) VALUES (10011, 1424541843, 10003, 10005, '2025-06-06 07:30:47', null, 'ACTIVE', null);
