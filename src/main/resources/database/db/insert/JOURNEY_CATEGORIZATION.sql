INSERT INTO JOURNEY.JOURNEY_CATEGORIZATION (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM, EFF_TO, STATUS) VALUES (21, 6, '2025-04-08 23:15:00', '9999-12-31 23:15:00', 'ACTIVE');
INSERT INTO JOURNEY.JOURNEY_CATEGORIZATION (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM, EFF_TO, STATUS) VALUES (10001, 10000, '2025-04-28 00:33:00', '2029-08-31 00:33:00', 'ACTIVE');
INSERT INTO JOURNEY.JOURNEY_CATEGORIZATION (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM, EFF_TO, STATUS) VALUES (10002, 10001, '2025-04-28 01:20:00', '2029-12-30 01:20:00', 'ACTIVE');
INSERT INTO JOURNEY.JOURNEY_CATEGORIZATION (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM, EFF_TO, STATUS) VALUES (10003, 10002, '2025-05-26 14:37:00', '2038-10-13 14:37:00', 'ACTIVE');
INSERT INTO JOURNEY.JOURNEY_CATEGORIZATION (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM, EFF_TO, STATUS) VALUES (10005, 10003, '2025-05-05 16:08:00', '2029-10-23 16:08:00', 'ACTIVE');
