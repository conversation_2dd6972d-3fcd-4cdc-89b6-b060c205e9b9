INSERT INTO JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID, NAME, RESOURCE_CODE, JOURNEY_CATEGORY_TYPE_ID, CATEGORY_CODE) VALUES (10000, 'Giorgi Test Category', null, 6, 'GTC');
INSERT INTO JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID, NAME, RESOURCE_CODE, JOURNEY_CATEGORY_TYPE_ID, CATEGORY_CODE) VALUES (6, 'Navigating stress and burnout', null, 6, 'AUG_NAVIGATION');
INSERT INTO JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID, NAME, RESOURCE_CODE, JOURNEY_CATEGORY_TYPE_ID, CATEGORY_CODE) VALUES (10001, 'Expired Category', null, 6, 'EXPIRED');
INSERT INTO JOURNEY.JOURNEY_CATEGORY (JO<PERSON><PERSON><PERSON>_CATEGORY_ID, NAM<PERSON>, RESOURCE_CODE, JOURNEY_CATEGORY_TYPE_ID, CATEGORY_CODE) VALUES (10002, 'Upcoming coaching journey', null, 6, 'UPCOMING_COACHING');
INSERT INTO JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID, NAME, RESOURCE_CODE, JOURNEY_CATEGORY_TYPE_ID, CATEGORY_CODE) VALUES (10003, 'IURI Navigating stress and burnout', null, 6, 'IURI_NAVIGATION');
INSERT INTO JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID, NAME, RESOURCE_CODE, JOURNEY_CATEGORY_TYPE_ID, CATEGORY_CODE) VALUES (10004, 'Navigating stress and burnout', null, 6, 'AUG_NAVIGATION_1');
