INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10001, 10001, 0, 'HLWL', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT1', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10000, 10000, 0, 'HLWL', 'COMPLETED', 1, 1, 'M', 'Icon APPOINT1', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10002, 10002, 0, 'ADPU', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT3', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10003, 10002, 0, 'LRPM', 'ACTIVE', 1, 0, 'M', null, '${activityname}', 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10004, 10003, 0, 'HLWL', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT1', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10005, 10004, 0, 'HLWL', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT1', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10006, 10005, 0, 'HLWL', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT1', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10007, 10006, 0, 'HLWL', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT1', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10008, 10007, 0, '3034', 'ACTIVE', 1, 0, 'M', 'https://cdn.powerofvitality.com/Vitality%203/PHP%20Journeys/icons/GroupCoaching.svg', 'Attend session on ${startTime:formatted:dd MMM}', 'APPOINTMENT');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10009, 10008, 0, '3034', 'ACTIVE', 1, 0, 'M', 'https://cdn.powerofvitality.com/Vitality%203/PHP%20Journeys/icons/GroupCoaching.svg', 'Attend session on ${startTime:formatted:dd MMM}', 'APPOINTMENT');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10010, 10009, 0, '3034', 'ACTIVE', 1, 0, 'M', 'https://cdn.powerofvitality.com/Vitality%203/PHP%20Journeys/icons/GroupCoaching.svg', 'Attend session on ${startTime:formatted:dd MMM}', 'APPOINTMENT');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10011, 10010, 0, 'ADPU', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT3', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10012, 10010, 0, 'LRPM', 'ACTIVE', 1, 0, 'M', null, '${activityname}', 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10013, 10011, 0, 'LRPM', 'ACTIVE', 1, 0, 'M', null, '${activityname}', 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10014, 10011, 0, 'ADPU', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT3', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10015, 10012, 0, 'LRPM', 'ACTIVE', 1, 0, 'M', null, '${activityname}', 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10016, 10012, 0, 'ADPU', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT3', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10017, 10013, 0, 'ADPU', 'ACTIVE', 1, 0, 'M', 'Icon APPOINT3', null, 'ACTIVITY');
INSERT INTO JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY (JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID, JOURNEY_ENROLLMENT_MILESTONE_ID, ACTIVITY_ALLOCATED_BY, ACTIVITY_MNEMONIC_ID, ACTIVITY_STATUS, ACTIVITY_AMOUNT, ACTIVITY_COMPLETION_COUNT, ACTIVITY_FLEXIBILITY, ACTIVITY_ICON, ACTIVITY_NAME, ACTIVITY_TYPE) VALUES (10018, 10013, 0, 'LRPM', 'ACTIVE', 1, 0, 'M', null, '${activityname}', 'ACTIVITY');
