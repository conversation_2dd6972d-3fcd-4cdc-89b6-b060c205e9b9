INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (21, 'Navigating stress and burnout', '', null, 'ACTIVE', '2025-03-31 22:24:00', '9999-02-06 22:24:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10001, '<PERSON><PERSON><PERSON> test program', 'Test program', null, 'ACTIVE', '2025-05-12 00:22:00', '2029-12-30 00:28:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10002, 'EXPIRED TESTING CATEGORY', 'Test program', null, 'ACTIVE', '2025-05-12 00:22:00', '2029-12-30 00:28:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10003, 'Upcoming Navigating stress and burnout', '', null, 'ACTIVE', '2025-03-31 22:24:00', '9999-02-06 22:24:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10004, 'Navigating stress and burnout', '', null, 'ACTIVE', '2025-03-31 22:24:00', '9999-02-06 22:24:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10005, 'Iuri Navigating stress and burnout', '', null, 'ACTIVE', '2025-03-31 22:24:00', '9999-02-06 22:24:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10006, 'NEW Navigating stress and burnout', '', null, 'ACTIVE', '2025-03-31 22:24:00', '9999-02-06 22:24:00');
INSERT INTO JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID, NAME, DESCRIPTION, RESOURCE_CODE, STATUS, EFF_FROM, EFF_TO) VALUES (10007, 'NEW Navigating stress and burnout', '', null, 'ACTIVE', '2025-03-31 22:24:00', '9999-02-06 22:24:00');
