CREATE SCHEMA JOURNEY;

CREATE SEQUENCE JOURNEY.JOURNEY_CATEGORY_TYPE_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_CATEGORY_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_RULES_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_VERSION_RULES_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_VERSION_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_MILESTONE_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_SEQ START WITH 1;
CREATE SEQUENCE JOURNEY.JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_SEQ START WITH 1;
--
-- CREATE TABLE JOURNEY.JOURNEY_CUSTOMER_DEFINITION
-- (
--     CUSTOMER_DEFINITION_ID NUMERIC(15) PRIMARY KEY,
--     ALLIANCE               VARCHAR(255),
--     GROUP_ID               VARCHAR(255),
--     BRANCH                 VARCHAR(255)
-- );

-- -- Used to manage the different types of programs that can be created or categories/dimensions we want to assign to a category
-- CREATE TABLE JOURNEY.JOURNEY_CATEGORY_TYPE
-- (
--     JOURNEY_CATEGORY_TYPE_ID NUMERIC(10) PRIMARY KEY, --1
--     NAME                     VARCHAR(255) NOT NULL    -- GROUP
-- );

-- Used to define the different categories that can be assigned to a program
-- CREATE TABLE JOURNEY.JOURNEY_CATEGORY
-- (
--     JOURNEY_CATEGORY_ID      NUMERIC(10) PRIMARY KEY, --1
--     NAME                     VARCHAR(255) NOT NULL,   -- Weight Management CATEGORY
--     JOURNEY_CATEGORY_TYPE_ID NUMERIC(10)  NOT NULL,   -- 1
--
--     CONSTRAINT JOURNEY_CATEGORY_FK
--         FOREIGN KEY (JOURNEY_CATEGORY_TYPE_ID)
--             REFERENCES JOURNEY.JOURNEY_CATEGORY_TYPE (JOURNEY_CATEGORY_TYPE_ID)
-- );

-- Used to define the different rules that can be used to determine if a program can be recommended to a customer, the activities, the transitions and the next program
-- CREATE TABLE JOURNEY.JOURNEY_RULES
-- (
--     JOURNEY_PROGRAM_VERSION_RULES_ID NUMERIC(10) PRIMARY KEY, --1
--     RULE_SET_NAME                    VARCHAR(255) NOT NULL,   -- ACTIVITIES TO RECOMMEND
--     RULE_SET_TYPE                    VARCHAR(255) NOT NULL,   -- DMN, SQL, JEXL
--     RULE_SET                         BLOB
--
-- );

--  Used to define the different programs that can be created for instance weight management
-- CREATE TABLE JOURNEY.JOURNEY_PROGRAM
-- (
--     JOURNEY_PROGRAM_ID NUMERIC(10) PRIMARY KEY, --1
--     NAME               VARCHAR(255) NOT NULL,   -- Weight Management
--     DESCRIPTION        VARCHAR(255),           -- Weight Management Program To Manage Weight
--     ACTIVE_VERSION     NUMERIC(10)  NOT NULL,   -- 1
--     STATUS             VARCHAR(255) NOT NULL,   -- ACTIVE, INACTIVE
--     EFF_FROM           TIMESTAMP,                -- 2021-08-01 00:00:00
--     EFF_TO             TIMESTAMP                 -- 2021-08-01 00:00:00
-- );

-- USED TO DETERMINE IF A PROGRAM CAN BE RECOMMENDED TO A CUSTOMER and if that customers has selected it
-- CREATE TABLE JOURNEY.JOURNEY_PROGRAM_RECOMMENDATION
-- (
--     JOURNEY_PROGRAM_ID               NUMERIC(10) PRIMARY KEY, --1
--     JOURNEY_PROGRAM_VERSION_RULES_ID NUMERIC(10) NOT NULL,    -- PROGRAM ACTIVATION RULES
--     CUSTOMER_ID                      NUMERIC(10) NOT NULL,    -- FILTER USED TO DETERMINE IF THIS PROGRAM CAN BE RECOMMENDED TO THE CUSTOMER
--
--     CONSTRAINT JOURNEY_PROGRAM_RECOMMENDATION_FK
--         FOREIGN KEY (JOURNEY_PROGRAM_ID)
--             REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),
--
--     CONSTRAINT JOURNEY_PROGRAM_RECOMMENDATION_FK2
--         FOREIGN KEY (CUSTOMER_ID)
--             REFERENCES JOURNEY.JOURNEY_CUSTOMER_DEFINITION (CUSTOMER_DEFINITION_ID),
--
--     CONSTRAINT JOURNEY_PROGRAM_RECOMMENDATION_FK3
--         FOREIGN KEY (JOURNEY_PROGRAM_VERSION_RULES_ID)
--             REFERENCES JOURNEY.JOURNEY_RULES (JOURNEY_PROGRAM_VERSION_RULES_ID)
-- );

-- Used to associate the program to a specific category for a period of time
-- CREATE TABLE JOURNEY.JOURNEY_CATEGORIZATION
-- (
--     JOURNEY_PROGRAM_ID  NUMERIC(10)  NOT NULL, -- 1
--     JOURNEY_CATEGORY_ID NUMERIC(10)  NOT NULL, -- 1
--     EFF_FROM            TIMESTAMP,              -- 2021-08-01 00:00:00
--     EFF_TO              TIMESTAMP,              -- 2021-08-01 00:00:00
--     STATUS              VARCHAR(255) NOT NULL, -- ACTIVE, INACTIVE
--
--     CONSTRAINT JOURNEY_CATEGORIZATION_PK PRIMARY KEY (JOURNEY_PROGRAM_ID, JOURNEY_CATEGORY_ID, EFF_FROM),
--     CONSTRAINT JOURNEY_CATEGORIZATION_FK
--         FOREIGN KEY (JOURNEY_PROGRAM_ID)
--             REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),
--
--     CONSTRAINT JOURNEY_CATEGORIZATION_FK2
--         FOREIGN KEY (JOURNEY_CATEGORY_ID)
--             REFERENCES JOURNEY.JOURNEY_CATEGORY (JOURNEY_CATEGORY_ID)
-- );

-- For this particular version of the program. Associate the set of characteristics to it.
-- CREATE TABLE JOURNEY.JOURNEY_PROGRAM_VERSION --> MIGHT NOT BE NECESSARY IF NOT COLLAPSE INTO SOMETHING ELSE
-- (
--     JOURNEY_PROGRAM_VERSION_ID         NUMERIC(10) PRIMARY KEY, --1
--     JOURNEY_PROGRAM_ID                 NUMERIC(10)  NOT NULL,   -- 1
--     VERSION                            NUMERIC(10)  NOT NULL,   -- 1
--     DESCRIPTION                        VARCHAR(255),           -- Weight Management Program To Manage Weight
--     JOURNEY_PROGRAM_VERSION_RULES_ID   NUMERIC(10),             -- ACTIVITIES TO RECOMMEND BASED ON THE MILESTONES
--     NEXT_MILESTONE_TRANSITION_RULES_ID NUMERIC(10),             -- RULES TO TRANSITION TO THE NEXT MILESTONE
--     NEXT_PROGRAM_ID                    NUMERIC(10) NULL,        -- 1
--     NEXT_PROGRAM_TRANSITION_RULES      NUMERIC(10),             -- RULES TO TRANSITION TO THE NEXT PROGRAM (IF YES THEN END THIS ONE AND ENROLL IN THE NEXT ONE)
--     EFF_FROM                           TIMESTAMP,                -- 2021-08-01 00:00:00
--     EFF_TO                             TIMESTAMP,                -- 2021-08-01 00:00:00
--     STATUS                             VARCHAR(255) NOT NULL,   -- ACTIVE, INACTIVE
--
--     CONSTRAINT JOURNEY_PROGRAM_VERSION_FK
--         FOREIGN KEY (JOURNEY_PROGRAM_ID)
--             REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),
--
--     CONSTRAINT JOURNEY_PROGRAM_VERSION_RULES_FK
--         FOREIGN KEY (JOURNEY_PROGRAM_VERSION_RULES_ID)
--             REFERENCES JOURNEY.JOURNEY_RULES (JOURNEY_PROGRAM_VERSION_RULES_ID),
--
--     CONSTRAINT JOURNEY_PROGRAM_VERSION_FK2
--         FOREIGN KEY (NEXT_PROGRAM_ID)
--             REFERENCES JOURNEY.JOURNEY_PROGRAM (JOURNEY_PROGRAM_ID),
--
--     CONSTRAINT JOURNEY_PROGRAM_VERSION_FK3
--         FOREIGN KEY (NEXT_PROGRAM_TRANSITION_RULES)
--             REFERENCES JOURNEY.JOURNEY_RULES (JOURNEY_PROGRAM_VERSION_RULES_ID),
--
--     CONSTRAINT MILESTONE_TRANSITION_RULES_FK
--         FOREIGN KEY (NEXT_MILESTONE_TRANSITION_RULES_ID)
--             REFERENCES JOURNEY.JOURNEY_RULES (JOURNEY_PROGRAM_VERSION_RULES_ID)
-- );

-- Defines the configuration for how milestones will be handled, for this a milestone could be 1 to 22 iterations of a Week, ie 22 WEEK PROGRAM
-- CREATE TABLE JOURNEY.JOURNEY_PROGRAM_MILESTONE
-- (
--     JOURNEY_PROGRAM_MILESTONE_ID NUMERIC(10) PRIMARY KEY, --1
--     JOURNEY_PROGRAM_VERSION_ID   NUMERIC(10)  NOT NULL,   -- 1
--     MILESTONE_RANGE_FROM         NUMERIC(10)  NOT NULL,   -- 1
--     MILESTONE_RANGE_TO           NUMERIC(10)  NOT NULL,   -- 22
--     MILESTONE_TYPE               VARCHAR(255) NOT NULL,   -- WEEKLY, WEEKLY BY ACTIVITY TERMINATING
--
--
--     CONSTRAINT JOURNEY_PROGRAM_MILESTONE_FK
--         FOREIGN KEY (JOURNEY_PROGRAM_VERSION_ID)
--             REFERENCES JOURNEY.JOURNEY_PROGRAM_VERSION (JOURNEY_PROGRAM_VERSION_ID)
-- );

-- CREATE CUSTOMER VALUES FOR EACH REWARD LEVEL FOR EACH CUSTOMER UPON COMPLETION for each milestone
-- CREATE TABLE JOURNEY.JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION
-- (                                                            --FIXED REWARD
--     JOURNEY_REWARD_CUSTOMIZATION_ID NUMERIC(10) PRIMARY KEY, --1
--     CUSTOMER_ID                     NUMERIC(10)  NOT NULL,   -- 1
--     JOURNEY_PROGRAM_MILESTONE_ID    NUMERIC(10)  NOT NULL,   -- 1 EVERY WEEK GET A REWARD, BUT WHAT ABOUT MILESTONE NUMBERS UPON EACH MILESTONE TYPE COMPLETION AWARD THIS
--     REWARD_TYPE                     VARCHAR(255) NOT NULL,   -- POINTS, BADGE, CERTIFICATE
--     REWARD_VALUE                    NUMERIC(10)  NOT NULL,   -- 100
--     EFF_FROM                        TIMESTAMP,                -- 2021-08-01 00:00:00
--     EFF_TO                          TIMESTAMP,                -- 2021-08-01 00:00:00
--
--     CONSTRAINT JOURNEY_REWARD_CUSTOMIZATION_FK
--         FOREIGN KEY (JOURNEY_PROGRAM_MILESTONE_ID)
--             REFERENCES JOURNEY.JOURNEY_PROGRAM_MILESTONE (JOURNEY_PROGRAM_MILESTONE_ID),
--     CONSTRAINT JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_FK2
--         FOREIGN KEY (CUSTOMER_ID)
--             REFERENCES JOURNEY.JOURNEY_CUSTOMER_DEFINITION (CUSTOMER_DEFINITION_ID)
-- );
-- ---
-- Assign a reward to the completion of a program for a customer
CREATE TABLE JOURNEY.JOURNEY_PROGRAM_REWARD_CUSTOMIZATION
(                                                            --FIXED REWARD
    JOURNEY_REWARD_CUSTOMIZATION_ID NUMERIC(10) PRIMARY KEY, --1
    CUSTOMER_ID                     NUMERIC(10)  NOT NULL,   -- 1
    JOURNEY_PROGRAM_VERSION_ID      NUMERIC(10)  NOT NULL,   -- 1 EVERY WEEK GET A REWARD, BUT WHAT ABOUT MILESTONE NUMBERS
    REWARD_TYPE                     VARCHAR(255) NOT NULL,   -- POINTS, BADGE, CERTIFICATE
    REWARD_VALUE                    NUMERIC(10)  NOT NULL,   -- 100
    EFF_FROM                        TIMESTAMP,               -- 2021-08-01 00:00:00
    EFF_TO                          TIMESTAMP,               -- 2021-08-01 00:00:00

    CONSTRAINT JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_FK
        FOREIGN KEY (JOURNEY_PROGRAM_VERSION_ID)
            REFERENCES JOURNEY.JOURNEY_PROGRAM_MILESTONE (JOURNEY_PROGRAM_MILESTONE_ID),
    CONSTRAINT JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_FK2
        FOREIGN KEY (CUSTOMER_ID)
            REFERENCES JOURNEY.JOURNEY_CUSTOMER_DEFINITION (CUSTOMER_DEFINITION_ID)
);
