<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/"
             id="definitions"
             name="definitions"
             namespace="http://camunda.org/schema/1.0/dmn">
    <decision id="decision" name="Journey Recommendation">
        <decisionTable hitPolicy="COLLECT">
            <input id="input1" label="Alliance">
                <inputExpression id="inputExpression1" typeRef="string">
                    <text>alliance</text>
                </inputExpression>
            </input>
            <input id="input2" label="Customer">
                <inputExpression id="inputExpression2" typeRef="string">
                    <text>customer</text>
                </inputExpression>
            </input>
            <input id="input3" label="Branch">
                <inputExpression id="inputExpression3" typeRef="string">
                    <text>branch</text>
                </inputExpression>
            </input>
            <output id="output1" label="JourneyCategory" name="journeyCategory" typeRef="string"/>
            <rule id="DecisionRule_1wcp84p">
                <inputEntry id="UnaryTests_0asw82z">
                    <text></text>
                </inputEntry>
                <inputEntry id="UnaryTests_04w2jej">
                    <text></text>
                </inputEntry>
                <inputEntry id="UnaryTests_02i6iy0">
                    <text></text>
                </inputEntry>
                <outputEntry id="LiteralExpression_0hj61gg">
                    <text>"Physical"</text>
                </outputEntry>
            </rule>
            <rule id="DecisionRule_1v3g5j7">
                <inputEntry id="UnaryTests_162xi43">
                    <text></text>
                </inputEntry>
                <inputEntry id="UnaryTests_130cmx7">
                    <text></text>
                </inputEntry>
                <inputEntry id="UnaryTests_0bzpa6l">
                    <text>"1"</text>
                </inputEntry>
                <outputEntry id="LiteralExpression_14h9xqd">
                    <text>"Blue"</text>
                </outputEntry>
            </rule>
        </decisionTable>
    </decision>
</definitions>
