{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:33666/v3/program", "description": "Generated server url"}], "paths": {"/api/v1/program/{id}": {"get": {"tags": ["program-controller"], "operationId": "getById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Program"}}}}}}, "put": {"tags": ["program-controller"], "operationId": "update", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Program"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Program"}}}}}}, "delete": {"tags": ["program-controller"], "operationId": "deleteById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-attribute/{id}": {"get": {"tags": ["program-attribute-controller"], "operationId": "getById_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramAttribute"}}}}}}, "put": {"tags": ["program-attribute-controller"], "operationId": "update_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramAttribute"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramAttribute"}}}}}}, "delete": {"tags": ["program-attribute-controller"], "operationId": "deleteById_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/dependent-activity-link/{id}": {"get": {"tags": ["dependent-activity-link-controller"], "operationId": "getById_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependentActivityLinkDTO"}}}}}}, "put": {"tags": ["dependent-activity-link-controller"], "operationId": "update_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependentActivityLinkRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependentActivityLinkDTO"}}}}}}, "delete": {"tags": ["dependent-activity-link-controller"], "operationId": "delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/batch/admin/import/publish": {"put": {"tags": ["admin-program-builder-controller"], "summary": "Publish a staged ZIP file stored in the database by id", "operationId": "publish", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminImportResponse"}}}}}}}, "/api/v1/builder/batch/admin/import/cancel": {"put": {"tags": ["admin-program-builder-controller"], "summary": "Cancel a staged ZIP file stored in the database by id", "operationId": "cancel", "parameters": [{"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminImportResponse"}}}}}}}, "/api/v1/admin/activity-category/{id}": {"put": {"tags": ["admin-category-controller"], "operationId": "update_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityCategoryCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityCategoryModel"}}}}}}, "delete": {"tags": ["admin-category-controller"], "operationId": "deleteById_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/activity-category-resource/{id}": {"get": {"tags": ["admin-resource-controller"], "operationId": "get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceWithHtmlDTO"}}}}}}, "put": {"tags": ["admin-resource-controller"], "operationId": "update_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceCreationRequestHtml"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceWithHtmlDTO"}}}}}}}, "/api/v1/activity/{id}": {"get": {"tags": ["activity-controller"], "operationId": "getById_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Activity"}}}}}}, "put": {"tags": ["activity-controller"], "operationId": "update_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Activity"}}}}}}, "delete": {"tags": ["activity-controller"], "operationId": "deleteById_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/{activityTransactionId}/status": {"put": {"tags": ["activity-transaction-controller"], "operationId": "updateStatus", "parameters": [{"name": "activityTransactionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateActivityTransactionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityTransactionResponse"}}}}}}}, "/api/v1/activity-transaction/status": {"put": {"tags": ["activity-transaction-controller"], "operationId": "updateStatusForEntity", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateActivityTransactionStatusForEntitiesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityTransactionResponse"}}}}}}}}, "/api/v1/activity-transaction/re-process": {"put": {"tags": ["activity-transaction-controller"], "operationId": "reprocessTransactions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReprocessTransactionsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-category/{id}": {"get": {"tags": ["category-controller"], "operationId": "getById_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCategoryModel"}}}}}}, "put": {"tags": ["category-controller"], "operationId": "update_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCategoryCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCategoryModel"}}}}}}, "delete": {"tags": ["category-controller"], "operationId": "deleteById_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-category-resource/{id}": {"get": {"tags": ["category-resource-controller"], "operationId": "getById_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceDTO"}}}}}}, "put": {"tags": ["category-resource-controller"], "operationId": "update_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceDTO"}}}}}}, "delete": {"tags": ["category-resource-controller"], "operationId": "deleteById_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-category-attribute/{id}": {"get": {"tags": ["category-attribute-controller"], "operationId": "getById_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryAttributeDTO"}}}}}}, "put": {"tags": ["category-attribute-controller"], "operationId": "update_8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryAttributeCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryAttributeDTO"}}}}}}, "delete": {"tags": ["category-attribute-controller"], "operationId": "deleteById_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-attribute/{id}": {"get": {"tags": ["activity-attribute-controller"], "operationId": "getById_8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityAttribute"}}}}}}, "put": {"tags": ["activity-attribute-controller"], "operationId": "update_9", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityAttribute"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityAttribute"}}}}}}, "delete": {"tags": ["activity-attribute-controller"], "operationId": "deleteById_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/recommendation/recommend/batch/result": {"post": {"tags": ["recommendation-controller"], "operationId": "recommendBatch", "parameters": [{"name": "activityCalculationResultMessage", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BatchRecommendationResponse"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/program": {"post": {"tags": ["program-controller"], "operationId": "add", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Program"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Program"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/fix/activity/statuses/{entityNo}": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "fixMemberActivityStatuses", "parameters": [{"name": "entityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/fix/activity/statuses/initiate/all": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "fixMemberActivityStatuses_1", "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/dto/mnemonic": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getMemberActivitiesByMnemonics", "parameters": [{"name": "entityNo", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "mnemonics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-PM-ON-B<PERSON><PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/mnemonic/exclude/custom": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getMemberActivitiesByMnemonicsWithCustomExclusion", "parameters": [{"name": "entityNo", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "mnemonics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-PM-ON-B<PERSON><PERSON><PERSON>", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberActivityExclusionFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}}, "/api/v1/program-enrolment-cohort-activity/activity/type/assessment/complete": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "completeAssessmentTypeActivity", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyAttributeProcessCompletedMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/activity/is-completed/{entityNo}": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "isActivityCompleted", "parameters": [{"name": "entityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCompletedRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/activity/complete": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "onActivityIdCompletion", "parameters": [{"name": "activityIdCompletionRequestMessage", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityIdCompletionRequestMessage"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/activity/complete/loop-legacy": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "onActivityCompletion", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCompletionRequestMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/activity/complete/bulk": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "onBulkActivityCompletion", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkActivityCompletionRequestMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-category-entity/filter": {"post": {"tags": ["program-category-controller"], "operationId": "filterMemberCategories", "parameters": [{"name": "entityId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramCategoryFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramActivityCategory"}}}}}}}, "/api/v1/program-category-entity/celebrate": {"post": {"tags": ["program-category-controller"], "operationId": "celebrateCategories", "parameters": [{"name": "entityId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CelebrateCategoriesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-attribute": {"post": {"tags": ["program-attribute-controller"], "operationId": "add_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramAttribute"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramAttribute"}}}}}}}, "/api/v1/onboarding/reset": {"post": {"tags": ["onboarding-controller"], "operationId": "resetOnboarding", "responses": {"200": {"description": "OK"}}}}, "/api/v1/model-notation/process": {"post": {"tags": ["model-notation-controller"], "operationId": "process", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModelNotationUpdateMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/enrolment/enrollment/cleanup/{entityNo}": {"post": {"tags": ["enrolment-controller"], "operationId": "cleanupEnrollment", "parameters": [{"name": "entityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/enrolment/enrollment/cleanup/bulk": {"post": {"tags": ["enrolment-controller"], "operationId": "cleanupEnrollmentBulk", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CleanupEnrollmentBulkRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/enrolment/enrol": {"post": {"tags": ["enrolment-controller"], "operationId": "enrol", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolment"}}}}}}}}, "/api/v1/enrolment/enrol/synchronous": {"post": {"tags": ["enrolment-controller"], "operationId": "enrolSynchronous", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolment"}}}}}}}}, "/api/v1/enrolment/bulk/enrol": {"post": {"tags": ["enrolment-controller"], "operationId": "bulkEnrol", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembersEnrollmentRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/dependent-activity-link": {"post": {"tags": ["dependent-activity-link-controller"], "operationId": "add_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependentActivityLinkRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependentActivityLinkDTO"}}}}}}}, "/api/v1/dependent-activity-link/{id}/customerDefinition": {"post": {"tags": ["dependent-activity-link-controller"], "operationId": "addCustomerDefinition", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDefinitionDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DependentActivityLinkDTO"}}}}}}, "delete": {"tags": ["dependent-activity-link-controller"], "operationId": "deleteCustomerDefinition", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDefinitionDTO"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/program/validate": {"post": {"tags": ["program-builder-controller"], "operationId": "validateActivities", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderProgram"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderProgramsAndActivitiesValidationResponse"}}}}}}}, "/api/v1/builder/program/validate/file": {"post": {"tags": ["program-builder-controller"], "operationId": "validateActivities_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderProgramsAndActivitiesValidationResponse"}}}}}}}, "/api/v1/builder/program/validate/category/bulk": {"post": {"tags": ["program-builder-controller"], "operationId": "validateCategoriesExist", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderProgram"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryValidationResponse"}}}}}}}, "/api/v1/builder/program/validate-dmn-zip": {"post": {"tags": ["dmn-builder-controller"], "operationId": "validateDMNZip", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderZipValidationResponse"}}}}}}}, "/api/v1/builder/program/upload-dmn-zip": {"post": {"tags": ["dmn-builder-controller"], "operationId": "uploadDMNZip", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/program/import": {"post": {"tags": ["program-builder-controller"], "operationId": "importPrograms", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderProgram"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderResponse"}}}}}}}, "/api/v1/builder/program/import/file": {"post": {"tags": ["program-builder-controller"], "operationId": "importPrograms_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderResponse"}}}}}}}, "/api/v1/builder/program/export": {"post": {"tags": ["program-builder-controller"], "operationId": "exportPrograms", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/builder/points/validate": {"post": {"tags": ["points-configuration-builder-controller"], "operationId": "validatePoints", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderPointsConfiguration"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderPointsValidationResponse"}}}}}}}, "/api/v1/builder/points/validate/file": {"post": {"tags": ["points-configuration-builder-controller"], "operationId": "validatePoints_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderPointsValidationResponse"}}}}}}}, "/api/v1/builder/points/import": {"post": {"tags": ["points-configuration-builder-controller"], "operationId": "importPointsConfigurations", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderPointsConfiguration"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/builder/points/import/file": {"post": {"tags": ["points-configuration-builder-controller"], "operationId": "importPointsConfigurations_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/v1/builder/points/export": {"post": {"tags": ["points-configuration-builder-controller"], "operationId": "exportPointsConfigurations", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityExportFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/builder/category/validate": {"post": {"tags": ["category-builder-controller"], "operationId": "validateCategories", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityCategory"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderCategoryValidationResponse"}}}}}}}, "/api/v1/builder/category/validate/file": {"post": {"tags": ["category-builder-controller"], "operationId": "validateCategories_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuilderCategoryValidationResponse"}}}}}}}, "/api/v1/builder/category/import": {"post": {"tags": ["category-builder-controller"], "operationId": "importCategories", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityCategory"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/category/import/file": {"post": {"tags": ["category-builder-controller"], "operationId": "importCategories_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/category/export": {"post": {"tags": ["category-builder-controller"], "operationId": "exportCategories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityExportFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/builder/category-additional-config/import": {"post": {"tags": ["category-additional-config-builder-controller"], "operationId": "importCategoryAdditionalConfig", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityCategoryAdditionalConfig"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/category-additional-config/import/file": {"post": {"tags": ["category-additional-config-builder-controller"], "operationId": "importCategoryAdditionalConfig_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/category-additional-config/export": {"post": {"tags": ["category-additional-config-builder-controller"], "operationId": "exportCategoryAdditionalConfig", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/builder/batch/validate/file": {"post": {"tags": ["batch-builder-controller"], "operationId": "validateZ<PERSON>", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/builder/batch/import/file": {"post": {"tags": ["batch-builder-controller"], "operationId": "importZip", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/builder/batch/export/file": {"post": {"tags": ["batch-builder-controller"], "operationId": "export", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/builder/batch/admin/import/upload": {"post": {"tags": ["admin-program-builder-controller"], "summary": "Upload an Excel file and store a corresponding ZIP file generated from it in the database", "operationId": "upload", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "File processed and uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminImportResponse"}}}}, "422": {"description": "Validation Failed - The provided Excel file contains errors.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResultsDTO"}}}}}}}, "/api/v1/admin/activity-category/": {"post": {"tags": ["admin-category-controller"], "operationId": "add_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityCategoryCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityCategoryModel"}}}}}}}, "/api/v1/admin/activity-category-resource": {"post": {"tags": ["admin-resource-controller"], "operationId": "add_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceCreationRequestHtml"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceWithHtmlDTO"}}}}}}}, "/api/v1/activity": {"post": {"tags": ["activity-controller"], "operationId": "add_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Activity"}}}}}}}, "/api/v1/activity/missing": {"post": {"tags": ["activity-controller"], "operationId": "findMissing", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MissingActivityRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NonExistentActivityResponse"}}}}}}}, "/api/v1/activity-transaction/nonexistent/recon": {"post": {"tags": ["activity-transaction-controller"], "operationId": "reconNonexistentTransactions", "parameters": [{"name": "thresholdDays", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NonexistentTransactionReconMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/nonexistent/recon/initiate": {"post": {"tags": ["activity-transaction-controller"], "operationId": "reconNonexistentTransactionsInitiate", "parameters": [{"name": "thresholdDays", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/nonexistent/recon/bulk": {"post": {"tags": ["activity-transaction-controller"], "operationId": "reconNonexistentTransactionsBulk", "parameters": [{"name": "thresholdDays", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NonexistentTransactionReconMessage"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/invalid/recon": {"post": {"tags": ["activity-transaction-controller"], "operationId": "reconInvalidTransactions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvalidTransactionReconMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/initiate/status-update": {"post": {"tags": ["activity-transaction-controller"], "operationId": "initiateTransactionStatusUpdate", "parameters": [{"name": "numHours", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/initiate/re-process": {"post": {"tags": ["activity-transaction-controller"], "operationId": "initiatePendingTransactionReprocess", "parameters": [{"name": "numDays", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 3}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/initiate/invalid/recon": {"post": {"tags": ["activity-transaction-controller"], "operationId": "initiateTransactionStatusUpdate_1", "responses": {"200": {"description": "OK"}}}}, "/api/v1/activity-transaction/filter": {"post": {"tags": ["activity-transaction-controller"], "operationId": "filter", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "minimum": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "minimum": 1}}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityTransactionFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageActivityTransactionResponse"}}}}}}}, "/api/v1/activity-transaction/filter-history": {"post": {"tags": ["activity-transaction-controller"], "operationId": "filter_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityTransactionFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityTransactionHistoryResponse"}}}}}}}}, "/api/v1/activity-category": {"post": {"tags": ["category-controller"], "operationId": "add_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCategoryCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCategoryModel"}}}}}}}, "/api/v1/activity-category/completion/{entityNo}": {"post": {"tags": ["category-controller"], "operationId": "checkCategoryCompletion", "parameters": [{"name": "entityNo", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryNamesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryCompletionDTO"}}}}}}}, "/api/v1/activity-category-resource": {"post": {"tags": ["category-resource-controller"], "operationId": "add_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceDTO"}}}}}}}, "/api/v1/activity-category-attribute": {"post": {"tags": ["category-attribute-controller"], "operationId": "add_8", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryAttributeCreationRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryAttributeDTO"}}}}}}}, "/api/v1/activity-attribute": {"post": {"tags": ["activity-attribute-controller"], "operationId": "add_9", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityAttribute"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityAttribute"}}}}}}}, "/api/v1/admin/program/activity/activity/{id}": {"patch": {"tags": ["admin-program-activity-controller"], "summary": "Update activity details by id", "operationId": "patchActivity", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchActivityRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityResponse"}}}}}}}, "/api/v1/program/filter": {"get": {"tags": ["program-controller"], "operationId": "filter_2", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProgramFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageProgram"}}}}}}}, "/api/v1/program/activity/standard-activities": {"get": {"tags": ["program-activity-controller"], "operationId": "getStandardActivities", "parameters": [{"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program/activity/custom-activities": {"get": {"tags": ["program-activity-controller"], "operationId": "getCustomActivities", "parameters": [{"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program/activity/custom-activities-employer": {"get": {"tags": ["program-activity-controller"], "operationId": "getCustomActivitiesEmployer", "parameters": [{"name": "empEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/{programEnrolmentCohortActivityId}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getByProgramEnrolmentCohortActivityId", "parameters": [{"name": "programEnrolmentCohortActivityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/name/{name}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getByProgramEnrolmentCohortActivityName", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/mnemonic/{mnemonic}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getByProgramEnrolmentCohortActivityMnemonic", "parameters": [{"name": "mnemonic", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/filter": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "summary": "Filter member activities", "operationId": "retrieveMemberActivities", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"]}}, {"name": "entityNo", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "categoryName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "parentCategoryName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "date", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "includeInvisible", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "includeManuallyAdded", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "programName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "excludedGroups", "in": "query", "description": "If null, categoryName filter will decide whether to return programs with groups. If categoryName is null, only non-group programs will be returned.If excludedGroups is true, only non-group programs will be returned.If excludedGroups is false, all type of programs will be returned.", "required": false, "schema": {"title": "Determines whether to exclude programs with associated groups based on categoryName filter.", "type": "string", "description": "If null, categoryName filter will decide whether to return programs with groups. If categoryName is null, only non-group programs will be returned.If excludedGroups is true, only non-group programs will be returned.If excludedGroups is false, all type of programs will be returned.", "example": true}, "example": true}, {"name": "mnemonics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageProgramEnrolmentCohortActivityDTO"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/activity-id/{activityId}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getByProgramEnrolmentCohortActivityByActivityId", "parameters": [{"name": "activityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/assessment/name/{assessmentName}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getAssessmentActivityByAssessmentName", "parameters": [{"name": "assessmentName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivity"}}}}}}}, "/api/v1/program-category-entity/list": {"get": {"tags": ["program-category-controller"], "operationId": "listMemberCategories", "parameters": [{"name": "entityId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramActivityCategory"}}}}}}}, "/api/v1/program-attribute/filter": {"get": {"tags": ["program-attribute-controller"], "operationId": "filter_3", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProgramAttributeFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageProgramAttribute"}}}}}}}, "/api/v1/onboarding/get-onboarding-activity": {"get": {"tags": ["onboarding-controller"], "operationId": "getOnboardingActivity", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}}}}}}, "/api/v1/onboarding/available": {"get": {"tags": ["onboarding-controller"], "operationId": "isOnboardingAssessmentAvailable", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/enrolment/list": {"get": {"tags": ["enrolment-controller"], "operationId": "getEntityEnrolments", "parameters": [{"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolment"}}}}}}}}, "/api/v1/dependent-activity-link/filter": {"get": {"tags": ["dependent-activity-link-controller"], "operationId": "filter_4", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/DependentActivityLinkFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageDependentActivityLinkDTO"}}}}}}}, "/api/v1/category/{categoryName}/activities": {"get": {"tags": ["activity-category-controller"], "operationId": "getActivitiesForCategory", "parameters": [{"name": "categoryName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "alliance", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "employer", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "branch", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryActivities"}}}}}}}, "/api/v1/builder/program/export-program-data": {"get": {"tags": ["program-builder-controller"], "operationId": "exportProgramData", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderProgram"}}}}}}}}, "/api/v1/builder/batch/admin/search": {"get": {"tags": ["admin-program-builder-controller"], "summary": "Search uploaded and imported files", "operationId": "search", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AdminImportFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAdminImportResponse"}}}}}}}, "/api/v1/builder/batch/admin/generatedZip/download/{id}": {"get": {"tags": ["admin-program-builder-controller"], "summary": "Retrieve a generated ZIP file by id", "operationId": "getGeneratedZipById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"multipart/form-data": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/admin/program/activity/standard-activities": {"get": {"tags": ["admin-program-activity-controller"], "summary": "Retrieve all standard activities", "operationId": "getStandardActivities_1", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAdminActivityResponse"}}}}}}}, "/api/v1/admin/program/activity/custom-activities": {"get": {"tags": ["admin-program-activity-controller"], "summary": "Retrieve all custom activities", "operationId": "getCustomActivities_1", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAdminActivityResponse"}}}}}}}, "/api/v1/admin/program/activity/activities/search": {"get": {"tags": ["admin-program-activity-controller"], "summary": "Search activities by criteria", "operationId": "searchActivities", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AdminActivityFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAdminActivityResponse"}}}}}}}, "/api/v1/admin/activity-category/standard-category/{id}": {"get": {"tags": ["admin-category-controller"], "operationId": "getStandardCategoryById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityCategoryDTO"}}}}}}}, "/api/v1/admin/activity-category/select/parent-categories": {"get": {"tags": ["admin-category-controller"], "operationId": "getParentCategories", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SmallCategoryDTO"}}}}}}}}, "/api/v1/admin/activity-category/select/health-risk-categories": {"get": {"tags": ["admin-category-controller"], "operationId": "getHealthRiskCategories", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SmallCategoryDTO"}}}}}}}}, "/api/v1/admin/activity-category/filter/admin-categories": {"get": {"tags": ["admin-category-controller"], "operationId": "filterAdminCategories", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityCategoryFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAdminSmallActivityCategoryDTO"}}}}}}}, "/api/v1/admin/activity-category/filter/admin-categories/export": {"get": {"tags": ["admin-category-controller"], "operationId": "filterAndExportAdminCategories", "parameters": [{"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityCategoryFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/admin/activity-category/customer-category/{id}": {"get": {"tags": ["admin-category-controller"], "operationId": "getCustomerCategoryById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminActivityCategoryDTO"}}}}}}}, "/api/v1/activity/type/all": {"get": {"tags": ["activity-type-controller"], "operationId": "findAll", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityType"}}}}}}}}, "/api/v1/activity/filter": {"get": {"tags": ["activity-controller"], "operationId": "filter_5", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"]}}, {"name": "descr", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "activityTypeId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "activityEvent", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageActivity"}}}}}}}, "/api/v1/activity/allocation-period/all": {"get": {"tags": ["activity-allocation-period-controller"], "operationId": "findAll_1", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAllocationPeriodDTO"}}}}}}}}, "/api/v1/activity/activity-type/{activityTypeId}": {"get": {"tags": ["activity-controller"], "operationId": "getById_4", "parameters": [{"name": "activityTypeId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}}}}}}}, "/api/v1/activity-category/filter": {"get": {"tags": ["category-controller"], "operationId": "filter_6", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityCategoryFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageActivityCategoryModel"}}}}}}}, "/api/v1/activity-category-type/select": {"get": {"tags": ["category-type-controller"], "operationId": "getCategoryTypes", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryTypeDTO"}}}}}}}}, "/api/v1/activity-category-resource/filter": {"get": {"tags": ["category-resource-controller"], "operationId": "filter_7", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ResourceFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResourceDTO"}}}}}}}, "/api/v1/activity-category-resource/filter/export": {"get": {"tags": ["category-resource-controller"], "operationId": "filterAndExport", "parameters": [{"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ResourceFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/activity-category-resource/all": {"get": {"tags": ["category-resource-controller"], "operationId": "getAll", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceDTO"}}}}}}}}, "/api/v1/activity-category-attribute/filter": {"get": {"tags": ["category-attribute-controller"], "operationId": "filter_8", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CategoryAttributeFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCategoryAttributeDTO"}}}}}}}, "/api/v1/activity-attribute/filter": {"get": {"tags": ["activity-attribute-controller"], "operationId": "filter_9", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityAttributeFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageActivityAttribute"}}}}}}}, "/api/v1/activity/cache/clear": {"delete": {"tags": ["activity-cache-controller"], "operationId": "clearCache", "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"Program": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "owner": {"type": "string"}, "programGroup": {"$ref": "#/components/schemas/ProgramGroup"}, "programAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramAssocAttribute"}}}}, "ProgramAssocAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "programAttribute": {"$ref": "#/components/schemas/ProgramAttribute"}, "value": {"type": "string"}}}, "ProgramAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}}}, "ProgramGroup": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "CustomerDefinitionDTO": {"type": "object", "properties": {"allianceId": {"type": "integer", "format": "int32"}, "employerId": {"type": "integer", "format": "int64"}, "branchId": {"type": "string"}, "memberRole": {"type": "string"}, "notCustomer": {"type": "boolean"}}}, "DependentActivityLinkRequest": {"required": ["dependentActivityDescription", "sourceActivityDescription"], "type": "object", "properties": {"sourceActivityDescription": {"type": "string"}, "dependentActivityDescription": {"type": "string"}, "activationPeriodMonth": {"minimum": 0, "type": "integer", "format": "int64"}, "customerDefinitions": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDefinitionDTO"}}}}, "DependentActivityLinkDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "sourceActivityDescription": {"type": "string"}, "dependentActivityDescription": {"type": "string"}, "activationPeriodMonth": {"type": "integer", "format": "int64"}, "customerDefinitions": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDefinitionDTO"}}}}, "AdminImportResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "excel_name": {"type": "string"}, "status": {"type": "string"}, "upload_date": {"type": "string", "format": "date-time"}, "status_change_date": {"type": "string", "format": "date-time"}}}, "AdminActivityCategoryBenefits": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}, "benefitId": {"type": "integer", "format": "int64"}}}, "AdminActivityCategoryCreationRequest": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}, "memberRole": {"type": "string"}, "showCategory": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "activityCategoryName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "integer", "format": "int64"}, "activityCategoryTypeId": {"type": "string"}, "activityCategoryAccumulationType": {"type": "string"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "healthCategory": {"type": "string"}, "effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}, "resourceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "benefits": {"type": "array", "items": {"$ref": "#/components/schemas/AdminActivityCategoryBenefits"}}, "categoryAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAttributeCategorizationRequest"}}}}, "CategoryAttributeCategorizationRequest": {"type": "object", "properties": {"activityCategoryAttributeId": {"type": "string"}, "activityCategoryAttributeValue": {"type": "string"}}}, "AdminActivityCategoryModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "activityCategoryName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "integer", "format": "int64"}, "activityCategoryTypeId": {"type": "string"}, "activityCategoryAccumulationType": {"type": "string"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceDTO"}}, "benefits": {"type": "array", "items": {"$ref": "#/components/schemas/AdminActivityCategoryBenefits"}}, "attributeCategorizations": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAttributeCategorizationDTO"}}, "activityCategoryRiskFactorId": {"type": "integer", "format": "int64"}}}, "CategoryAttributeCategorizationDTO": {"type": "object", "properties": {"activityCategoryAttributeId": {"type": "string"}, "activityCategoryDescription": {"type": "string"}, "activityCategoryAttributeValue": {"type": "string"}}}, "ResourceDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "link": {"type": "string"}, "image": {"type": "string"}}}, "ResourceCreationRequestHtml": {"required": ["html", "name"], "type": "object", "properties": {"name": {"type": "string"}, "imageLink": {"type": "string"}, "html": {"type": "string"}}}, "ResourceWithHtmlDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "image": {"type": "string"}, "html": {"type": "string"}}}, "ActivityAssocAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "activityAttribute": {"$ref": "#/components/schemas/ActivityAttribute"}, "value": {"type": "string"}, "customerDefinitionId": {"type": "integer", "format": "int64"}}}, "ActivityAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}}}, "ActivityCreationRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "activityAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAssocAttribute"}}, "categoryIds": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}, "typeId": {"type": "string"}, "cmsContentInfos": {"type": "array", "items": {"$ref": "#/components/schemas/CmsContent"}}, "relatedHealthTopic": {"type": "string"}, "timeToComplete": {"type": "string"}, "frequency": {"type": "integer", "format": "int64"}, "activityEvent": {"type": "string"}, "allocation": {"type": "string", "enum": ["UponEventReceipt", "UpFront"]}, "effFrom": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "allocationPeriodId": {"type": "string"}, "parentActivityId": {"type": "integer", "format": "int64"}, "parentActivityName": {"type": "string"}, "mainActivityName": {"type": "string"}, "activityCompletionDescription": {"type": "string"}, "visibility": {"type": "boolean"}, "rewardType": {"type": "string", "enum": ["POINTS", "NONE"]}, "dependentActivityIds": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ActivityInfoCreationRequest": {"type": "object", "properties": {"cmsName": {"type": "string"}, "instruction": {"type": "string"}, "description": {"type": "string"}, "about": {"type": "string"}, "articleContent": {"type": "string"}, "disclaimer": {"type": "string"}, "primaryCtaName": {"type": "string"}, "primaryCtaLink": {"type": "string"}, "primaryCtaLinkMobile": {"type": "string"}, "primaryCtaNameAlternative": {"type": "string"}, "primaryCtaLinkAlternative": {"type": "string"}, "primaryCtaLinkAlternativeMobile": {"type": "string"}, "secondaryCtaName": {"type": "string"}, "secondaryCtaLink": {"type": "string"}, "secondaryCtaLinkMobile": {"type": "string"}, "secondaryCtaNameAlternative": {"type": "string"}, "secondaryCtaLinkAlternative": {"type": "string"}, "secondaryCtaLinkAlternativeMobile": {"type": "string"}, "embeddedVideoLink": {"type": "string"}, "embeddedVideoTranscript": {"type": "string"}, "embeddedVideoSubtitles": {"type": "string"}, "imageLink": {"type": "string"}, "phpIconUrl": {"type": "string"}, "cardCtaLink": {"type": "string"}, "cardCtaLinkMobile": {"type": "string"}}}, "CmsContent": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "category": {"type": "string"}, "tag": {"type": "string"}, "activityState": {"type": "string", "enum": ["REQUIREMENTS_MET", "REQUIREMENTS_UNMET"]}, "activityInfoCreationRequest": {"$ref": "#/components/schemas/ActivityInfoCreationRequest"}}}, "Activity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "effFrom": {"type": "string", "format": "java8-date-time"}, "effTo": {"type": "string", "format": "java8-date-time"}, "frequency": {"type": "integer", "format": "int64"}, "rewardType": {"type": "string", "enum": ["POINTS", "NONE"]}, "activityEvent": {"type": "string"}, "relatedHealthTopic": {"type": "string"}, "timeToComplete": {"type": "string"}, "allocation": {"type": "string", "enum": ["UponEventReceipt", "UpFront"]}, "visibility": {"type": "boolean"}, "activityAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAssocAttribute"}}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "rootCategory": {"type": "string"}, "type": {"$ref": "#/components/schemas/ActivityType"}, "activityAllocationPeriod": {"$ref": "#/components/schemas/ActivityAllocationPeriodDTO"}, "parentActivityId": {"type": "integer", "format": "int64"}, "cmsContents": {"type": "array", "items": {"$ref": "#/components/schemas/CmsContent"}}, "dependentActivityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "manuallyAddable": {"type": "boolean"}}}, "ActivityAllocationPeriodDTO": {"type": "object", "properties": {"allocationPeriodId": {"type": "string"}, "descr": {"type": "string"}}}, "ActivityType": {"type": "object", "properties": {"activityTypeId": {"type": "string"}, "activityTypeDescription": {"type": "string"}}}, "UpdateActivityTransactionRequest": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ALLOCATED", "PENDING", "REJECTED", "REVERSED"]}, "activityTransactionId": {"type": "integer", "format": "int64"}, "preLimitPoints": {"type": "integer", "format": "int32"}, "postLimitPoints": {"type": "integer", "format": "int32"}, "awardDate": {"type": "string", "format": "date"}}}, "ActivityTransactionResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "activityAllocationStatus": {"type": "string"}, "pacProgramActivity": {"type": "string"}, "pointsAccumulationType": {"type": "string"}, "activityCategory": {"type": "string"}, "activitySubcategories": {"type": "array", "items": {"type": "string"}}, "transactionDate": {"type": "string", "format": "date-time"}, "policyId": {"type": "string"}, "entityNo": {"type": "integer", "format": "int64"}, "eventId": {"type": "string"}, "pointAward": {"type": "integer", "format": "int64"}, "activityValue": {"type": "string"}, "postPointAward": {"type": "integer", "format": "int64"}, "accumFrom": {"type": "string", "format": "date-time"}, "accumTo": {"type": "string", "format": "date-time"}, "awardDate": {"type": "string", "format": "date-time"}, "createdDate": {"type": "string", "format": "date-time"}}}, "UpdateActivityTransactionStatusForEntitiesRequest": {"type": "object", "properties": {"entityNumbers": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ReprocessTransactionsRequest": {"type": "object", "properties": {"transactionIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ActivityCategoryCreationRequest": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}, "memberRole": {"type": "string"}, "showCategory": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "integer", "format": "int64"}, "activityCategoryTypeId": {"type": "string"}, "activityCategoryAccumulationType": {"type": "string"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "healthCategory": {"type": "string"}, "effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}, "resourceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "benefitIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "categoryAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAttributeCategorizationRequest"}}}}, "ActivityCategoryModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "integer", "format": "int64"}, "activityCategoryTypeId": {"type": "string"}, "activityCategoryAccumulationType": {"type": "string"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceDTO"}}, "benefits": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "attributeCategorizations": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAttributeCategorizationDTO"}}, "activityCategoryRiskFactorId": {"type": "integer", "format": "int64"}}}, "ResourceCreationRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "link": {"type": "string"}, "image": {"type": "string"}}}, "CategoryAttributeCreationRequest": {"type": "object", "properties": {"activityCategoryAttributeId": {"type": "string"}, "activityCategoryDescription": {"type": "string"}}}, "CategoryAttributeDTO": {"type": "object", "properties": {"activityCategoryAttributeId": {"type": "string"}, "activityCategoryDescription": {"type": "string"}}}, "BatchRecommendationResponse": {"type": "object", "properties": {"recommendationResponses": {"type": "array", "items": {"$ref": "#/components/schemas/RecommendationResponse"}}}}, "FlexSubscriptionPeriod": {"type": "object", "properties": {"subscriptionStart": {"type": "string", "format": "date-time"}, "subscriptionEnd": {"type": "string", "format": "date-time"}}}, "RecommendationResponse": {"type": "object", "properties": {"programEnrolmentId": {"type": "integer", "format": "int64"}, "entityId": {"type": "integer", "format": "int64"}, "programId": {"type": "integer", "format": "int64"}, "programName": {"type": "string"}, "activities": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "decision": {"type": "string"}, "bulk": {"type": "boolean"}, "subscriptionPeriods": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FlexSubscriptionPeriod"}}}}, "ActivityInstantReward": {"type": "object", "properties": {"rewardType": {"type": "string"}, "rewardAmount": {"type": "string"}, "rewardCurrency": {"type": "string"}}}, "ActivityPoints": {"type": "object", "properties": {"rewardType": {"type": "string"}, "rewardAllocation": {"type": "string"}, "pointsAchieved": {"type": "integer", "format": "int64"}}}, "CategoryInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "parentCategoryInfo": {"$ref": "#/components/schemas/CategoryInfo"}}}, "ProgramEnrolmentCohortActivityDTO": {"type": "object", "properties": {"programId": {"type": "string"}, "programName": {"type": "string"}, "programEnrolmentId": {"type": "string"}, "programEnrolmentCohortActivityId": {"type": "string"}, "parentProgramEnrolmentCohortActivityId": {"type": "string"}, "activityId": {"type": "string"}, "activityName": {"type": "string"}, "activityStatus": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}, "measureAchievement": {"type": "string"}, "activityPoints": {"$ref": "#/components/schemas/ActivityPoints"}, "activityInstantReward": {"$ref": "#/components/schemas/ActivityInstantReward"}, "mnemonic": {"type": "string"}, "effForm": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "content": {"$ref": "#/components/schemas/CmsContent"}, "activityType": {"type": "string"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "categoryInfos": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryInfo"}}, "frequency": {"type": "integer", "format": "int64"}, "timeToComplete": {"type": "string"}, "activityCompletedDate": {"type": "string", "format": "date-time"}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}}, "roles": {"type": "array", "items": {"type": "string"}}, "isPhysicalActivity": {"type": "boolean"}, "entityId": {"type": "string"}, "manuallyAdded": {"type": "boolean"}}}, "MemberActivityExclusionFilter": {"type": "object", "properties": {"includeOutdated": {"type": "boolean"}, "includeInvisible": {"type": "boolean"}, "includeManuallyAdded": {"type": "boolean"}, "includeCancelled": {"type": "boolean"}, "excludeNoPointActivities": {"type": "boolean"}}}, "SurveyAttributeProcessCompletedMessage": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "surveyTemplateIdentifier": {"type": "string"}, "surveyName": {"type": "string"}, "submissionDate": {"type": "string", "format": "date-time"}}}, "ActivityCompletedRequest": {"type": "object", "properties": {"mnemonic": {"type": "string"}, "activityName": {"type": "string"}}}, "ActivityIdCompletionRequestMessage": {"type": "object", "properties": {"programEnrolmentCohortActivityId": {"type": "integer", "format": "int64"}, "activityValue": {"type": "string"}, "date": {"type": "string", "format": "date-time"}}}, "ActivityCompletionRequestMessage": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "policyNo": {"type": "integer", "format": "int64"}, "mnemonic": {"type": "string"}, "activityName": {"type": "string"}, "program": {"type": "string"}, "date": {"type": "string", "format": "date"}, "score": {"type": "integer", "format": "int64"}, "goalId": {"type": "integer", "format": "int64"}, "sendingEntity": {"type": "integer", "format": "int64"}}}, "BulkActivityCompletionRequestMessage": {"type": "object", "properties": {"activityIdCompletionRequestMessages": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityIdCompletionRequestMessage"}}}}, "ProgramCategoryFilter": {"type": "object", "properties": {"parentCategoryId": {"type": "string"}, "categoryTypeId": {"type": "string"}, "categoryNames": {"type": "array", "items": {"type": "string"}}}}, "ActivityCategoryDTO": {"type": "object", "properties": {"activityCategoryId": {"type": "string"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "string"}, "activityCategoryType": {"type": "string"}, "activityCategoryRanking": {"type": "string"}, "categoryImageUrl": {"type": "string"}, "primaryRisk": {"type": "string"}, "healthCategory": {"type": "string"}, "activityCompletion": {"$ref": "#/components/schemas/ActivityCompletion"}, "activityPoint": {"$ref": "#/components/schemas/ActivityPoint"}, "resourceList": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryResource"}}, "benefitList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "recommended": {"type": "boolean"}}}, "ActivityCategoryIdName": {"type": "object", "properties": {"activityCategoryId": {"type": "string"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}}}, "ActivityCategoryResource": {"type": "object", "properties": {"resourceId": {"type": "string"}, "resourceName": {"type": "string"}, "resourceType": {"type": "string"}, "resourceLink": {"type": "string"}, "resourceImage": {"type": "string"}}}, "ActivityCompletion": {"type": "object", "properties": {"activitiesCompleted": {"type": "integer", "format": "int32"}, "activitiesTotal": {"type": "integer", "format": "int32"}}}, "ActivityPoint": {"type": "object", "properties": {"activityCategoryPointAchieved": {"type": "integer", "format": "int64"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "activityPointsPerActivity": {"type": "string"}, "activityPointsAllocation": {"type": "string"}}}, "ProgramActivityCategory": {"type": "object", "properties": {"activityCategoryList": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryDTO"}}, "categoriesToCelebrate": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryIdName"}}}}, "CelebrateCategoriesRequest": {"type": "object", "properties": {"categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ModelNotationUpdateMessage": {"type": "object", "properties": {"fileNames": {"type": "array", "items": {"type": "string"}}}}, "CleanupEnrollmentBulkRequest": {"type": "object", "properties": {"employerNo": {"type": "integer", "format": "int64"}}}, "EntityInformation": {"type": "object", "properties": {"allianceId": {"type": "integer", "format": "int32"}, "employerId": {"type": "integer", "format": "int64"}, "branchId": {"type": "string"}, "memberRole": {"type": "string"}, "entityNo": {"type": "string"}, "policyId": {"type": "string"}, "age": {"type": "integer", "format": "int32"}, "gender": {"type": "string"}, "notCustomer": {"type": "boolean"}}}, "ProgramEnrolmentRequest": {"type": "object", "properties": {"entityId": {"type": "integer", "format": "int64"}, "programId": {"type": "integer", "format": "int64"}, "programName": {"type": "string"}, "bulk": {"type": "boolean"}, "entityInformation": {"$ref": "#/components/schemas/EntityInformation"}}}, "ProgramEnrolment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "entityId": {"type": "integer", "format": "int64"}, "program": {"$ref": "#/components/schemas/Program"}, "lastEnrolmentTime": {"type": "string", "format": "date-time"}, "effFrom": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["ACTIVE", "TERMINATED"]}, "state": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}}}, "MembersEnrollmentRequest": {"type": "object", "properties": {"lowerLimit": {"type": "integer", "format": "int32"}, "upperLimit": {"type": "integer", "format": "int32"}, "alliances": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "customers": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "branches": {"type": "array", "items": {"type": "string"}}, "enrollAllExistingMembers": {"type": "boolean"}, "programName": {"type": "string"}, "chunkSize": {"type": "integer", "format": "int32"}}}, "BuilderActivity": {"type": "object", "properties": {"name": {"type": "string"}, "completion": {"type": "string"}, "allocation": {"type": "string"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expiryDate": {"type": "string", "format": "date-time"}, "frequency": {"type": "integer", "format": "int64"}, "allocationDuration": {"type": "string"}, "activityEvent": {"type": "string"}, "activityRewardType": {"type": "string", "enum": ["POINTS", "NONE"]}, "contents": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/BuilderCmsContent"}}, "parentEventName": {"type": "string"}, "mainActivityName": {"type": "string"}, "activityCompletionDescription": {"type": "string"}, "visibility": {"type": "boolean"}, "relatedHealthTopic": {"type": "string"}, "timeToComplete": {"type": "string"}, "attributes": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/BuilderAttribute"}}, "categories": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}, "dependentActivities": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "BuilderAttribute": {"type": "object", "properties": {"allianceId": {"type": "string"}, "employerId": {"type": "string"}, "branchId": {"type": "string"}, "key": {"type": "string"}, "value": {"type": "string"}}}, "BuilderCmsContent": {"type": "object", "properties": {"activityState": {"type": "string", "enum": ["REQUIREMENTS_MET", "REQUIREMENTS_UNMET"]}, "category": {"type": "string"}, "tag": {"type": "string"}, "cmsName": {"type": "string"}, "cmsDescription": {"type": "string"}, "cmsDisclaimer": {"type": "string"}, "cmsAbout": {"type": "string"}, "cmsArticleContent": {"type": "string"}, "cmsInstruction": {"type": "string"}, "primaryCTAName": {"type": "string"}, "primaryCTA": {"type": "string"}, "primaryCTAMobile": {"type": "string"}, "secondaryCTAName": {"type": "string"}, "secondaryCTA": {"type": "string"}, "secondaryCTALinkMobile": {"type": "string"}, "primaryCTANameAlternative": {"type": "string"}, "primaryCTALinkAlternative": {"type": "string"}, "primaryCTALinkAlternativeMobile": {"type": "string"}, "secondaryCTANameAlternative": {"type": "string"}, "secondaryCTALinkAlternative": {"type": "string"}, "secondaryCTALinkAlternativeMobile": {"type": "string"}, "embeddedVideoLink": {"type": "string"}, "embeddedVideoTranscript": {"type": "string"}, "embeddedVideoSubtitles": {"type": "string"}, "imageLink": {"type": "string"}, "phpIconUrl": {"type": "string"}}}, "BuilderProgram": {"type": "object", "properties": {"name": {"type": "string"}, "groupName": {"type": "string"}, "attributes": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/BuilderAttribute"}}, "activities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/BuilderActivity"}}}}, "BuilderActivityUpdateInfo": {"type": "object", "properties": {"newEntry": {"$ref": "#/components/schemas/BuilderActivity"}, "oldEntry": {"$ref": "#/components/schemas/BuilderActivity"}}}, "BuilderProgramResponse": {"type": "object", "properties": {"name": {"type": "string"}, "groupName": {"type": "string"}, "attributes": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/BuilderAttribute"}}}}, "BuilderProgramUpdateInfo": {"type": "object", "properties": {"newEntry": {"$ref": "#/components/schemas/BuilderProgramResponse"}, "oldEntry": {"$ref": "#/components/schemas/BuilderProgramResponse"}}}, "BuilderProgramsAndActivitiesValidationResponse": {"type": "object", "properties": {"builderProgramsToCreate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderProgramResponse"}}, "builderProgramsToUpdate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderProgramUpdateInfo"}}, "builderActivitiesToCreate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivity"}}, "builderActivitiesToUpdate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityUpdateInfo"}}}}, "CategoryValidationModel": {"type": "object", "properties": {"parentCategoryName": {"type": "string"}, "categoryName": {"type": "string"}}}, "CategoryValidationResponse": {"type": "object", "properties": {"categoriesThatDoesNotExist": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/CategoryValidationModel"}}}}, "BuilderZipValidationResponse": {"type": "object", "properties": {"newFiles": {"type": "array", "items": {"type": "string"}}, "updatedFiles": {"type": "array", "items": {"type": "string"}}}}, "BuilderResponse": {"type": "object", "properties": {"programCount": {"type": "integer", "format": "int32"}, "cohortCount": {"type": "integer", "format": "int32"}, "activityCount": {"type": "integer", "format": "int32"}, "updatedActivityCount": {"type": "integer", "format": "int32"}}}, "BuilderPointsConfiguration": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}, "isVitalityDefault": {"type": "string"}, "activityId": {"type": "string"}, "activityName": {"type": "string"}, "rewardType": {"type": "string"}, "pointsType": {"type": "string"}, "staticPointsValue": {"type": "string"}, "offerId": {"type": "string"}, "giftcardProvider": {"type": "string"}, "rewardId": {"type": "string"}, "onlineSku": {"type": "string"}, "offlineSku": {"type": "string"}, "rewardAmount": {"type": "string"}, "rewardCurrency": {"type": "string"}, "rewardMemberRole": {"type": "string"}, "effFromDate": {"type": "string", "format": "date"}, "effToDate": {"type": "string", "format": "date"}}}, "BuilderPointsUpdateInfo": {"type": "object", "properties": {"newEntry": {"$ref": "#/components/schemas/BuilderPointsConfiguration"}, "oldEntry": {"$ref": "#/components/schemas/BuilderPointsConfiguration"}}}, "BuilderPointsValidationResponse": {"type": "object", "properties": {"pointConfigurationsToCreate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderPointsConfiguration"}}, "pointConfigurationsToUpdate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderPointsUpdateInfo"}}}}, "ActivityExportFilter": {"type": "object", "properties": {"retrieveAll": {"type": "boolean"}, "activityNames": {"type": "array", "items": {"type": "string"}}, "activityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "BuilderActivityCategory": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}, "memberRole": {"type": "string"}, "showCategory": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "integer", "format": "int64"}, "activityCategoryTypeName": {"type": "string"}, "healthCategory": {"type": "string"}}}, "BuilderCategoryUpdateInfo": {"type": "object", "properties": {"updatedCategory": {"$ref": "#/components/schemas/BuilderActivityCategory"}, "categoriesAffected": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityCategory"}}}}, "BuilderCategoryValidationResponse": {"type": "object", "properties": {"categoriesToCreate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityCategory"}}, "categoriesToUpdate": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderCategoryUpdateInfo"}}}}, "BuilderActivityCategoryAdditionalConfig": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}, "currentRole": {"type": "string"}, "categoryId": {"type": "integer", "format": "int64"}, "categoryName": {"type": "string"}, "categoryType": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceName": {"type": "string"}, "resourceLink": {"type": "string"}, "resourceImage": {"type": "string"}, "benefitId": {"type": "integer", "format": "int64"}, "pointsMax": {"type": "integer", "format": "int64"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/BuilderActivityCategoryAttribute"}}}}, "BuilderActivityCategoryAttribute": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "ErrorResultsDTO": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "string"}}}}, "MissingActivityRequest": {"type": "object", "properties": {"activityNames": {"type": "array", "items": {"type": "string"}}}}, "NonExistentActivityResponse": {"type": "object", "properties": {"nonExistActivities": {"type": "array", "items": {"type": "string"}}}}, "NonexistentTransactionReconMessage": {"type": "object", "properties": {"entityNo": {"type": "string"}, "thresholdDays": {"type": "integer", "format": "int32"}}}, "InvalidTransactionReconMessage": {"type": "object", "properties": {"entityNo": {"type": "string"}}}, "Pageable": {"type": "object", "properties": {"page": {"minimum": 0, "type": "integer", "format": "int32"}, "size": {"minimum": 1, "type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ActivityTransactionFilter": {"type": "object", "properties": {"activityTransactionIds": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}, "activityId": {"type": "integer", "format": "int64"}, "allocationStatusId": {"type": "string"}, "activityCategory": {"type": "string"}, "transactionDate": {"type": "string", "format": "date-time"}, "transactionFromDate": {"type": "string", "format": "date-time"}, "transactionToDate": {"type": "string", "format": "date-time"}, "pointsAccumulationTypeId": {"type": "string"}, "policyId": {"type": "string"}, "entityNo": {"type": "integer", "format": "int64"}, "eventId": {"type": "string"}}}, "PageActivityTransactionResponse": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityTransactionResponse"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "unpaged": {"type": "boolean"}, "paged": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "offset": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int32"}}}, "SortObject": {"type": "object", "properties": {"sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "ActivityTransactionHistoryResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "pacProgramActivity": {"type": "string"}, "activityCategory": {"type": "string"}, "activitySubcategories": {"type": "array", "items": {"type": "string"}}, "transactionDate": {"type": "string", "format": "date-time"}, "awardDate": {"type": "string", "format": "date-time"}}}, "CategoryNamesRequest": {"type": "object", "properties": {"categoryNames": {"type": "array", "items": {"type": "string"}}}}, "CategoryCompletionDTO": {"type": "object", "properties": {"completed": {"type": "boolean"}}}, "PatchActivityRequest": {"type": "object", "properties": {"program": {"type": "string"}, "activity_state": {"type": "string"}, "updated_override_name": {"type": "string"}, "updated_about_content": {"type": "string"}}}, "AdminActivityResponse": {"type": "object", "properties": {"activity_id": {"type": "integer", "format": "int64"}, "employer": {"type": "string"}, "program": {"type": "string"}, "activity_state": {"type": "string"}, "activity_name": {"type": "string"}, "activity_override_name": {"type": "string"}, "about_content": {"type": "string"}}}, "PagingRequest": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sortField": {"type": "string"}, "sortDirection": {"type": "string", "enum": ["ASC", "DESC"]}}}, "ProgramFilter": {"type": "object", "properties": {"name": {"type": "string"}, "owner": {"type": "string"}}}, "PageProgram": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Program"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "ActivityResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "effFrom": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "frequency": {"type": "integer", "format": "int64"}, "activityEvent": {"type": "string"}, "relatedHealthTopic": {"type": "string"}, "timeToComplete": {"type": "string"}, "allocation": {"type": "string", "enum": ["UponEventReceipt", "UpFront"]}, "visibility": {"type": "boolean"}, "activityAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAssocAttribute"}}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "rootCategory": {"type": "string"}, "type": {"$ref": "#/components/schemas/ActivityType"}, "activityAllocationPeriod": {"$ref": "#/components/schemas/ActivityAllocationPeriodDTO"}, "parentActivityId": {"type": "integer", "format": "int64"}, "cmsContents": {"type": "array", "items": {"$ref": "#/components/schemas/CmsContent"}}, "pointsAward": {"type": "integer", "format": "int64"}, "entityInformation": {"$ref": "#/components/schemas/EntityInformation"}, "dependentActivityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "activityOverrideName": {"type": "string"}}}, "PageProgramEnrolmentCohortActivityDTO": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTO"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "ActivityCompletionStatusModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}, "dateFrom": {"type": "string", "format": "date-time"}}}, "ProgramEnrolmentCohortActivity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "programActivity": {"$ref": "#/components/schemas/Activity"}, "itemId": {"type": "integer", "format": "int64"}, "activityCompletionStatus": {"$ref": "#/components/schemas/ActivityCompletionStatusModel"}, "activityComplete": {"type": "string", "format": "date-time"}, "activityValue": {"type": "string"}, "exposureDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "subActivities": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivity"}}, "entityId": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int64"}, "manuallyAdded": {"type": "boolean"}, "programEnrolment": {"$ref": "#/components/schemas/ProgramEnrolment"}, "activityCompleteStatusEnum": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}}}, "ProgramAttributeFilter": {"type": "object", "properties": {"descr": {"type": "string"}}}, "PageProgramAttribute": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramAttribute"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "DependentActivityLinkFilter": {"type": "object", "properties": {"sourceActivityDescription": {"type": "string"}, "dependentActivityDescription": {"type": "string"}}}, "PageDependentActivityLinkDTO": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/DependentActivityLinkDTO"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "CategoryActivities": {"type": "object", "properties": {"activityCatId": {"type": "integer", "format": "int64"}, "activityCatDescription": {"type": "string"}, "activityCatOverrideDescription": {"type": "string"}, "activityCatAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAssocAttribute"}}, "activityCatActivities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "activityCatSubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryActivities"}}}}, "CategoryAssocAttribute": {"type": "object", "properties": {"id": {"type": "string"}, "activityCatAttribute": {"$ref": "#/components/schemas/CategoryAttribute"}, "value": {"type": "string"}, "effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}}}, "CategoryAttribute": {"type": "object", "properties": {"id": {"type": "string"}, "descr": {"type": "string"}}}, "AdminImportFilter": {"type": "object", "properties": {"excelNameMatch": {"type": "string"}, "statuses": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["STAGED", "PROCESSING", "COMPLETED", "CANCELED", "ERRORS"]}}}}, "PageAdminImportResponse": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AdminImportResponse"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageAdminActivityResponse": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AdminActivityResponse"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AdminActivityFilter": {"type": "object", "properties": {"employerPrefix": {"type": "string"}, "programPrefix": {"type": "string"}, "activityPrefix": {"type": "string"}}}, "AdminActivityCategoryDTO": {"type": "object", "properties": {"customerDefinition": {"type": "array", "items": {"$ref": "#/components/schemas/AdminCategoryCustomerDefinition"}}, "memberRole": {"type": "string"}, "showCategory": {"type": "boolean"}, "activityCategoryId": {"type": "integer", "format": "int64"}, "customerCategoryId": {"type": "integer", "format": "int64"}, "activityCategoryName": {"type": "string"}, "activityCategoryParentCategoryName": {"type": "string"}, "activityCategoryTypeName": {"type": "string"}, "activityCategoryAccumulationType": {"type": "string"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "healthCategoryName": {"type": "string"}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceDTO"}}, "benefits": {"type": "array", "items": {"$ref": "#/components/schemas/AdminActivityCategoryBenefits"}}, "categoryAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAttributeCategorizationDTO"}}}}, "AdminCategoryCustomerDefinition": {"type": "object", "properties": {"allianceId": {"type": "string"}, "customerId": {"type": "string"}, "branchId": {"type": "string"}}}, "SmallCategoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "overrideDescr": {"type": "string"}}}, "ActivityCategoryFilter": {"type": "object", "properties": {"descr": {"type": "string"}}}, "AdminSmallActivityCategoryDTO": {"type": "object", "properties": {"activityCategoryId": {"type": "integer", "format": "int64"}, "categoryName": {"type": "string"}, "parentCategoryName": {"type": "string"}, "categoryType": {"type": "string"}}}, "PageAdminSmallActivityCategoryDTO": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AdminSmallActivityCategoryDTO"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "ActivityFilter": {"type": "object", "properties": {"descr": {"type": "string"}, "activityTypeId": {"type": "string"}, "activityEvent": {"type": "array", "items": {"type": "string"}}}}, "PageActivity": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageActivityCategoryModel": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryModel"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "CategoryTypeDTO": {"type": "object", "properties": {"id": {"type": "string"}, "descr": {"type": "string"}}}, "ResourceFilter": {"type": "object", "properties": {"resourceName": {"type": "string"}}}, "PageResourceDTO": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceDTO"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "CategoryAttributeFilter": {"type": "object", "properties": {"descr": {"type": "string"}}}, "PageCategoryAttributeDTO": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAttributeDTO"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "ActivityAttributeFilter": {"type": "object", "properties": {"descr": {"type": "string"}}}, "PageActivityAttribute": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAttribute"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}}}}