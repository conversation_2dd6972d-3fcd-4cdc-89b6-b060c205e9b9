{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "paths": {"/api/not-existing": {"post": {"tags": ["Entity"], "operationId": "getNotExistingByMemberAudience", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberAudienceEntityNo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}}}}, "/api/employer/employers/alliance-ids": {"post": {"tags": ["Employer"], "operationId": "getEmployerAllianceIds", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EmployerAllianceResponse"}}}}}}}, "/api/employer/config/filter": {"post": {"tags": ["Employer"], "operationId": "filterEmployerConfigs", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployerConfigFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EmployerConfigFilterResponse"}}}}}}}}, "/api/member-entity-nos": {"get": {"tags": ["Entity"], "operationId": "streamMemberEntityNos", "parameters": [{"name": "alliance", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "employer", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "branch", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/x-ndjson": {"schema": {"$ref": "#/components/schemas/FluxLong"}}}}}}}, "/api/member-data/by-entity-no": {"get": {"tags": ["Entity"], "operationId": "getByEntityNo", "parameters": [{"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MonoMemberData"}}}}}}}, "/api/getByEntityId": {"get": {"tags": ["Entity"], "operationId": "getMemberEmployerView", "parameters": [{"name": "entityId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/VMemberEmployerView"}}}}}}}, "/api/getByEmployerEntityNo": {"get": {"tags": ["Entity"], "operationId": "getMemberEmployerViewByEmployerEntityNo", "parameters": [{"name": "employerEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VMemberEmployerView"}}}}}}}}, "/api/get-by-entity-nos": {"get": {"tags": ["Entity"], "operationId": "getByEntityNos", "parameters": [{"name": "entityNos", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FluxMemberData"}}}}}}}, "/api/findByEmployeeNoAndEmpEntNo": {"get": {"tags": ["Entity"], "operationId": "getMemberEmployerViewByEmployeeNoAndEmpEntNo", "parameters": [{"name": "employeeNo", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "employerEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VMemberEmployerView"}}}}}}}}, "/api/entity-nos/by-k-numbers": {"get": {"tags": ["Entity"], "operationId": "getEntityNosByKNumbers", "parameters": [{"name": "kNumbers", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FluxKNumberToEntityMapping"}}}}}}}, "/api/entity-no/by-k-number": {"get": {"tags": ["Entity"], "operationId": "getEntityNoByKNumber", "parameters": [{"name": "kNumber", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/api/employer/list/active": {"get": {"tags": ["Employer"], "operationId": "getActiveEmployers", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employer"}}}}}}}}, "/api/employer/config": {"get": {"tags": ["Employer"], "operationId": "getEmployerConfig", "parameters": [{"name": "alliance", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "employer", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "branchId", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EmployerConfigResponse"}}}}}}}, "/api/delivery-details": {"get": {"tags": ["DeliveryDetails"], "operationId": "getDeliveryDetails", "parameters": [{"name": "entityId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DeliveryDetails"}}}}}}}, "/api/count-by-member-profile/": {"get": {"tags": ["Entity"], "operationId": "getCountByMemberProfile", "parameters": [{"name": "allianceId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "employerEntityNo", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "branch", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}}}}, "/api/communication/preferenses": {"get": {"tags": ["Entity"], "operationId": "getCommPreferences", "parameters": [{"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MemberCommPreferences"}}}}}}}}, "components": {"schemas": {"MemberAudienceEntityNo": {"required": ["alliance", "entityNos"], "type": "object", "properties": {"alliance": {"type": "integer", "format": "int32"}, "employerEntityNo": {"type": "integer", "format": "int64"}, "branch": {"type": "string"}, "entityNos": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "EmployerAllianceResponse": {"type": "object", "properties": {"employerAllianceMap": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}}, "EmployerConfigFilter": {"type": "object", "properties": {"mnemonics": {"type": "array", "items": {"type": "string"}}}}, "EmployerConfigFilterResponse": {"type": "object", "properties": {"alliance": {"type": "integer", "format": "int64"}, "employer": {"type": "integer", "format": "int64"}, "branch": {"type": "string"}, "key": {"type": "string"}, "value": {"type": "string"}}}, "FluxLong": {"type": "object", "properties": {"prefetch": {"type": "integer", "format": "int32"}}}, "MonoMemberData": {"type": "object"}, "VMemberEmployerView": {"type": "object", "properties": {"memberEnityNo": {"type": "integer", "format": "int64"}, "empEntNo": {"type": "integer", "format": "int64"}, "empInternalId": {"type": "integer", "format": "int32"}, "empExtId": {"type": "string"}, "empName": {"type": "string"}, "empContractNo": {"type": "integer", "format": "int64"}, "contractStartDate": {"type": "string"}, "contractEndDate": {"type": "string"}, "allianceId": {"type": "integer", "format": "int32"}, "subCompany": {"type": "integer", "format": "int32"}, "allianceName": {"type": "string"}, "curntBrncdA": {"type": "string"}, "descr": {"type": "string"}, "memNo": {"type": "integer", "format": "int64"}, "memDepNo": {"type": "integer", "format": "int64"}, "coverageEffDate": {"type": "string"}, "wdActualDate": {"type": "string"}, "employeeNo": {"type": "string"}, "particStat": {"type": "string"}, "depExtCardNo": {"type": "string"}, "riskModelId": {"type": "integer", "format": "int64"}, "riskModelDescription": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "polCardNo": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date-time"}, "gender": {"type": "string"}, "countryCd": {"type": "string"}, "employmentDate": {"type": "string"}, "email": {"type": "string"}, "telNo": {"type": "string"}, "active": {"type": "boolean"}}}, "FluxMemberData": {"type": "object", "properties": {"prefetch": {"type": "integer", "format": "int32"}}}, "FluxKNumberToEntityMapping": {"type": "object", "properties": {"prefetch": {"type": "integer", "format": "int32"}}}, "Employer": {"type": "object", "properties": {"tenant": {"type": "integer", "format": "int32"}, "alliance": {"type": "integer", "format": "int32"}, "empEntNo": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}}}, "EmployerConfigResponse": {"type": "object", "properties": {"alliance": {"type": "integer", "format": "int64"}, "employer": {"type": "integer", "format": "int64"}, "branch": {"type": "string"}, "flags": {"type": "object", "additionalProperties": {"type": "string"}}}}, "DeliveryDetails": {"type": "object", "properties": {"address": {"$ref": "#/components/schemas/UserAddress"}, "email": {"type": "string"}, "employer_entity_no": {"type": "integer", "format": "int64"}, "employer_name": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "offline": {"type": "boolean"}, "phone": {"type": "string"}}}, "UserAddress": {"type": "object", "properties": {"city": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "postal_code": {"type": "string"}, "region": {"type": "string"}}}, "MemberCommPreferences": {"type": "object", "properties": {"memberEntityNo": {"type": "integer", "format": "int64"}, "emailAllowed": {"type": "boolean"}, "pushAllowed": {"type": "boolean"}}}}}}