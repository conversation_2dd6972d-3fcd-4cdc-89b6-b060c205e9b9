{"swagger": "2.0", "info": {"description": "VAP", "title": "VAP Rest API"}, "host": "vgtweblogic04.discsrv.co.za:7002", "basePath": "/vap", "tags": [{"name": "activity-register-controller", "description": "Activity Register Controller"}], "paths": {"/rest/activity/values/filter/{entityNo}/{activityType}": {"get": {"tags": ["activity-register-controller"], "summary": "filterActivityValues", "operationId": "filterActivityValuesUsingGET", "produces": ["application/json"], "parameters": [{"name": "activityType", "in": "path", "description": "activityType", "required": true, "type": "string"}, {"name": "entityNo", "in": "path", "description": "entityNo", "required": true, "type": "integer", "format": "int64"}, {"name": "from", "in": "query", "description": "from", "required": false, "type": "string", "format": "date-time"}, {"name": "to", "in": "query", "description": "to", "required": false, "type": "string", "format": "date-time"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/WorkoutActivity"}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/rest/activity/values/{eventCode}/event/{entityNo}/{activityType}": {"get": {"tags": ["activity-register-controller"], "summary": "getLatestActivityValuesByEventCode", "operationId": "getLatestActivityValuesByEventCodeUsingGET", "produces": ["application/json"], "parameters": [{"name": "activityType", "in": "path", "description": "activityType", "required": true, "type": "string"}, {"name": "entityNo", "in": "path", "description": "entityNo", "required": true, "type": "integer", "format": "int64"}, {"name": "eventCode", "in": "path", "description": "eventCode", "required": true, "type": "string"}, {"name": "limit", "in": "query", "description": "limit", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"output": {"type": "array", "items": {"$ref": "#/definitions/WorkoutActivity"}}, "status": {"type": "integer", "format": "int32", "description": "Response status code"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/rest/activity/value/{entityNo}/{activityType}": {"get": {"tags": ["activity-register-controller"], "summary": "getLatestActivityValue", "operationId": "getLatestActivityValueUsingGET", "produces": ["application/json"], "parameters": [{"name": "activityType", "in": "path", "description": "activityType", "required": true, "type": "string"}, {"name": "entityNo", "in": "path", "description": "entityNo", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkoutActivity"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/rest/activity/values/{entityNo}/{activityType}": {"get": {"tags": ["activity-register-controller"], "summary": "getLatestActivityValues", "operationId": "getLatestActivityValuesUsingGET", "produces": ["application/json"], "parameters": [{"name": "activityType", "in": "path", "description": "activityType", "required": true, "type": "string"}, {"name": "entityNo", "in": "path", "description": "entityNo", "required": true, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "description": "limit", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"output": {"type": "array", "items": {"$ref": "#/definitions/WorkoutActivity"}}, "status": {"type": "integer", "format": "int32", "description": "Response status code"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "securityDefinitions": {"basicAuth": {"type": "basic"}}, "definitions": {"WorkoutActivity": {"type": "object", "properties": {"date": {"type": "string", "example": "yyyy-MM-dd"}, "type": {"type": "string"}, "value": {"type": "integer", "format": "int64"}, "alternative_value": {"type": "integer", "format": "int64"}}, "title": "WorkoutActivity"}}}