{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://integrationtest.powerofvitality.com/v3/data-attribute-service"}], "paths": {"/data/demographic/fact": {"put": {"tags": ["data-controller"], "operationId": "updateDemographicFactsForEntity", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberAttributesChanged"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["data-controller"], "operationId": "addDemographicFact", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoopLegacyProgramEnrolmentMessage"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/data/{memberEntityNo}/vitality-check": {"get": {"tags": ["data-controller"], "operationId": "getMemberVitalityCheckData", "parameters": [{"name": "memberEntityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/VitalityCheckMemberData"}}}}}}, "post": {"tags": ["data-controller"], "operationId": "addMemberVitalityCheckData", "parameters": [{"name": "memberEntityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VitalityCheckMemberData"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/VitalityCheckMemberData"}}}}}}}, "/data/search": {"post": {"tags": ["data-controller"], "operationId": "search", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataSupplyRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataSupplyResponse"}}}}}, "deprecated": true}}, "/data/medication/fact": {"post": {"tags": ["data-controller"], "operationId": "addMedicationFact", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityMedicationFactCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/data/gaps/open": {"post": {"tags": ["data-controller"], "operationId": "getOpenEntityGaps", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindAttributesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EntityGapDTO"}}}}}}}}, "/data/find": {"post": {"tags": ["data-controller"], "operationId": "find", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindAttributesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EntityDemographicFactDto"}}}}}}}, "/data/find/_many": {"post": {"tags": ["data-controller"], "operationId": "findForMany", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindAttributesForMany"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/DemographicFactFindDto"}}}}}}}}}, "/data/find-with-subscriptions": {"post": {"tags": ["data-controller"], "operationId": "findWithSubscription", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindAttributesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FindAttributesResponse"}}}}}}}, "/data/find-from-core": {"post": {"tags": ["data-controller"], "operationId": "findFromCore", "parameters": [{"name": "effFrom", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "effTo", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindAttributesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EntityDemographicFactDto"}}}}}}}, "/data/biometric/fact": {"post": {"tags": ["data-controller"], "operationId": "addBiometricFact", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityBiometricFactCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/data/{memberEntityNo}/latest-trusted-biometrics": {"get": {"tags": ["data-controller"], "operationId": "getLatestTrustedBiometrics", "parameters": [{"name": "memberEntityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TrustedBiometricMemberData"}}}}}}}, "/data/subscriptions/{entityNo}": {"get": {"tags": ["data-controller"], "operationId": "subscriptions", "parameters": [{"name": "entityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FlexSubscriptionDto"}}}}}}}}, "/data/medication/fact/{entity_no}": {"get": {"tags": ["data-controller"], "operationId": "getMedicationFacts", "parameters": [{"name": "entity_no", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MedicationFactDto"}}}}}}}}, "/data/demographic/unique-indexes": {"get": {"tags": ["data-controller"], "operationId": "getUniqueDemographicIndexes", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/DemographicIndexDto"}}}}}}}}, "/data/biometric/fact/{entity_no}": {"get": {"tags": ["data-controller"], "operationId": "getBiometricFacts", "parameters": [{"name": "entity_no", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BiometricFactDto"}}}}}}}}}, "components": {"schemas": {"MemberAttribute": {"type": "object", "properties": {"memberAttributeName": {"type": "string"}, "memberAttributeValue": {"type": "string"}, "source": {"type": "string"}, "capturedAt": {"type": "string", "format": "date-time"}}}, "MemberAttributesChanged": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "syncToCore": {"type": "boolean"}, "memberAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/MemberAttribute"}}}}, "DemographicFactDto": {"type": "object", "properties": {"index": {"type": "string", "enum": ["MEDICAL_HEIGHT_VALUE", "MEDICAL_WEIGHT_VALUE", "MEDICAL_HBA1C_LEVEL", "MEDICAL_HDL_CHOLESTEROL_VALUE", "MEDICAL_WAIST_CIRCUMFERENCE_VALUE", "MEDICAL_TOTAL_CHOLESTEROL_VALUE", "MEDICAL_TRIGLYCERIDE_VALUE", "MEDICAL_BODY_MASS_INDEX_GRADE", "MEDICAL_FASTING_GLUCOSE_VALUE", "MEDICAL_DIASTOLIC", "MEDICAL_SYSTOLIC", "MEDICAL_CARE_DIABETES", "MEDICAL_CAREHIGH_BLOOD_PRESSURE", "MEDICAL_LDL_CHOLESTEROL_VALUE", "LIFESTYLE_DO_YOU_SMOKE_CIGARETTES", "LIFESTYLE_ALCOHOL_DRINKER", "LIFESTYLE_SLEEP_HOURS_PER_DAY", "MEDICAL_PREGNANT", "MENTAL_HEALTH_ANXIETY", "MEDICAL_ASTHMA", "MEDICAL_HISTORY_BACK_PAIN", "MEDICAL_CANCER", "MEDICAL_CHRONIC_LUNG_DISEASE", "MEDICAL_HISTORY_CHRONIC_PAIN", "MENTAL_HEALTH_DEPRESSION", "MEDICAL_DIABETES", "MEDICAL_HEART_DISEASE", "MEDICAL_HYPERTENSION", "MEDICAL_CHOLESTEROL_HIGH", "MEDICAL_MENOPAUSE", "MEDICAL_OSTEOPOROSIS", "MEDICAL_PREDIABETES", "MEDICAL_STROKE"]}, "value": {"type": "string"}, "captureDate": {"type": "string", "format": "date-time"}, "source": {"type": "string"}, "indexUnit": {"type": "string"}}}, "VitalityCheckMemberData": {"type": "object", "properties": {"memberEntityNo": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DemographicFactDto"}}}}, "DataSupplyRequest": {"type": "object", "properties": {"query": {"$ref": "#/components/schemas/Query"}, "filter": {"$ref": "#/components/schemas/Filter"}, "supportDataObjects": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Filter": {"type": "object", "properties": {"fieldName": {"type": "array", "items": {"$ref": "#/components/schemas/FilterTerm"}}}}, "FilterTerm": {"type": "object", "properties": {"termName": {"type": "string"}}}, "Query": {"type": "object", "properties": {"queryParameter": {"type": "array", "items": {"$ref": "#/components/schemas/QueryParameter"}}}}, "QueryParameter": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "Column": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["stringType", "longType", "doubleType", "dateType", "dateTimeType", "booleanType"]}}}, "DataSupplyResponse": {"type": "object", "properties": {"columns": {"type": "array", "items": {"$ref": "#/components/schemas/Column"}}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/Row"}}}}, "Row": {"type": "object", "properties": {"row": {"type": "array", "items": {"type": "object"}}}}, "EntityMedicationFactCreateRequest": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "medicationId": {"type": "string"}, "factDate": {"type": "string", "format": "date-time"}, "medicationName": {"type": "string"}, "medicationDose": {"type": "string"}, "medicationUnit": {"type": "string"}, "medicationStartDate": {"type": "string", "format": "date-time"}, "medicationEndDate": {"type": "string", "format": "date-time"}, "medicationDosingInterval": {"type": "string"}, "sourceSystemId": {"type": "string"}, "archive": {"type": "boolean"}}}, "FindAttributesRequest": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "demographicIndexIds": {"type": "array", "items": {"type": "string"}}}}, "EntityGapDTO": {"type": "object", "properties": {"gapShortName": {"type": "string"}, "status": {"type": "string"}, "identificationDate": {"type": "string", "format": "date-time"}}}, "DemographicFactFindDto": {"type": "object", "properties": {"index": {"type": "string"}, "value": {"type": "string"}, "captureDate": {"type": "string", "format": "date-time"}, "source": {"type": "string"}}}, "EntityDemographicFactDto": {"type": "object", "properties": {"memberEntityNo": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DemographicFactFindDto"}}}}, "FindAttributesForMany": {"type": "object", "properties": {"entityNos": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}, "demographicIndexIds": {"type": "array", "items": {"type": "string"}}}}, "FindAttributesResponse": {"type": "object", "properties": {"entityDemographicFactDto": {"$ref": "#/components/schemas/EntityDemographicFactDto"}, "flexSubscriptionDtos": {"type": "array", "items": {"$ref": "#/components/schemas/FlexSubscriptionDto"}}, "entityGapDTOs": {"type": "array", "items": {"$ref": "#/components/schemas/EntityGapDTO"}}}}, "FlexSubscriptionDto": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "subscriptionId": {"type": "integer", "format": "int64"}, "subscriptionMnemonic": {"type": "string"}, "subscriptionDate": {"type": "string", "format": "date-time"}, "subscriptionStart": {"type": "string", "format": "date-time"}, "subscriptionEnd": {"type": "string", "format": "date-time"}}}, "LoopLegacyProgramEnrolmentMessage": {"type": "object", "properties": {"gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "birthDate": {"type": "string"}, "plan_code": {"type": "string"}, "employer_entity_number": {"type": "integer", "format": "int64"}}}, "EntityBiometricFactCreateRequest": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "biometricIndexId": {"type": "string"}, "factDate": {"type": "string", "format": "date-time"}, "biometricValue": {"type": "string"}, "biometricUnit": {"type": "string"}, "sourceSystemId": {"type": "string"}, "archive": {"type": "boolean"}}}, "TrustedBiometricMemberData": {"type": "object", "properties": {"memberEntityNo": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DemographicFactDto"}}}}, "MedicationFactDto": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "medicationId": {"type": "string"}, "factDate": {"type": "string", "format": "date-time"}, "medicationName": {"type": "string"}, "medicationDose": {"type": "string"}, "medicationUnit": {"type": "string"}, "medicationStartDate": {"type": "string", "format": "date-time"}, "medicationEndDate": {"type": "string", "format": "date-time"}, "medicationDosingInterval": {"type": "string"}, "sourceSystemId": {"type": "string"}, "archive": {"type": "boolean"}}}, "DemographicIndexDto": {"type": "object", "properties": {"index": {"type": "string"}}}, "BiometricFactDto": {"type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "biometricIndexId": {"type": "string"}, "factDate": {"type": "string", "format": "date-time"}, "biometricValue": {"type": "string"}, "biometricUnit": {"type": "string"}, "sourceSystemId": {"type": "string"}, "archive": {"type": "boolean"}}}}}}