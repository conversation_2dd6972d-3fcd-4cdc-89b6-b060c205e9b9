{"openapi": "3.0.1", "info": {"title": "Journey Importer API", "description": "API for importing and processing journey data for health and wellness programs. Supports CSV file uploads, batch processing, and job monitoring.", "version": "1.0.0"}, "tags": [{"name": "Import", "description": "Journey data import operations"}, {"name": "<PERSON><PERSON>", "description": "Spring Batch job monitoring and status operations"}], "paths": {"/api/import/upload/dpp-csv": {"post": {"tags": ["Import"], "summary": "Import DPP CSV file", "description": "Uploads and imports a Diabetes Prevention Program CSV file for the specified employer. The file must be in CSV format. Returns the import job details if successful. As result it will create a personal journey template data for each member from the file.", "operationId": "importDppCsv", "parameters": [{"name": "employerId", "in": "query", "description": "Employer identifier", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "description": "CSV file containing DPP journey data", "format": "binary"}}}}}}, "responses": {"500": {"description": "Failed to handle uploaded file", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Invalid file format or missing parameters", "content": {"application/json": {"schema": {"type": "object"}}}}, "200": {"description": "Import started successfully", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/import/generate/dpp-csv": {"get": {"tags": ["import-data-generator-controller"], "summary": "Generate mock DPP CSV file", "description": "Generates a mock DPP CSV file with the specified number of users and weeks. The file can be downloaded and used for testing purposes.", "operationId": "generateDppCsv", "parameters": [{"name": "users", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "weeks", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 26}}, {"name": "className", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "classDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK", "content": {"text/csv": {"schema": {"type": "string", "format": "byte"}}}}}}, "post": {"tags": ["import-data-generator-controller"], "summary": "Generate mock DPP CSV file for users in CSV file", "description": "Generates a mock DPP CSV file for the users specified in the uploaded CSV file. Intended to generated test data for real Vitality members. The file can be downloaded and used for testing purposes.", "operationId": "generateDppCsvForUsers", "parameters": [{"name": "employerNo", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "weeks", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 26}}, {"name": "className", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "classDate", "in": "query", "required": false, "schema": {"type": "string", "format": "date"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"text/csv": {"schema": {"type": "string", "format": "byte"}}}}}}}, "/api/staging/records": {"get": {"tags": ["staging-data-controller"], "summary": "Get staging records for import job", "description": "Returns the staging records for the specified import job.", "operationId": "getStagingRecords", "parameters": [{"name": "importJobId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelStagingRecordDto"}}}}}}}, "/api/members/": {"get": {"tags": ["members-controller"], "summary": "Get all members", "description": "Returns a list of all members or members for the specified employer.", "operationId": "getAll", "parameters": [{"name": "employerId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedModelMemberDto"}}}}}}}, "/api/journey-templates/{entityNo}": {"get": {"tags": ["journey-template-controller"], "summary": "Get journey template for member by entity no", "description": "Returns the journey template for the specified member and employer. This is the data that will be used to enroll the member in the journey.", "operationId": "getJourneyTemplate", "parameters": [{"name": "journeyType", "in": "query", "required": false, "schema": {"type": "string", "default": "DPP", "enum": ["DPP"]}}, {"name": "entityNo", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MemberJourneyTemplateResponse"}}}}}}}, "/api/journey-templates/": {"get": {"tags": ["journey-template-controller"], "summary": "Get journey template for member", "description": "Returns the journey template for the specified member and employer. This is the data that will be used to enroll the member in the journey.", "operationId": "getJourneyTemplate_1", "parameters": [{"name": "journeyType", "in": "query", "required": false, "schema": {"type": "string", "default": "DPP", "enum": ["DPP"]}}, {"name": "memberUniqueId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "employerId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MemberJourneyTemplateResponse"}}}}}}}, "/api/batch/jobs": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get all batch jobs", "description": "Retrieves all Spring Batch job executions", "operationId": "getAllJobs", "responses": {"200": {"description": "Jobs retrieved successfully", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatchJobDto"}}}}}}}}, "/api/batch/jobs/{jobExecutionId}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get batch job by ID", "description": "Retrieves a specific batch job with its steps", "operationId": "getJobById", "parameters": [{"name": "jobExecutionId", "in": "path", "description": "Job execution ID", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 123}], "responses": {"200": {"description": "Job retrieved successfully", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BatchJobDto"}}}}, "404": {"description": "Job not found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BatchJobDto"}}}}}}}, "/api/import/clear": {"delete": {"tags": ["Import"], "summary": "Delete all staging and normalized data", "description": "WARNING: This operation will irreversibly delete all staging and normalized data. Use with caution.", "operationId": "deleteAllData", "responses": {"200": {"description": "All data deleted successfully"}}}}}, "components": {"schemas": {"Pageable": {"type": "object", "properties": {"page": {"minimum": 0, "type": "integer", "format": "int32"}, "size": {"minimum": 1, "type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "PageMetadata": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "number": {"type": "integer", "format": "int64"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int64"}}}, "PagedModelStagingRecordDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/StagingRecordDto"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "StagingRecordDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "jsonPayload": {"type": "object", "additionalProperties": {"type": "object"}}}}, "MemberDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "entityNo": {"type": "integer", "format": "int64"}, "uniqueId": {"type": "string"}, "employerId": {"type": "integer", "format": "int64"}, "cdcId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "PagedModelMemberDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/MemberDto"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "MemberJourneyTemplateDto": {"type": "object", "properties": {"journeyType": {"type": "string"}, "journeyMilestoneType": {"type": "string"}, "journeyName": {"type": "string"}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}}, "member": {"$ref": "#/components/schemas/MemberDto"}, "program": {"$ref": "#/components/schemas/MemberProgramDto"}}}, "MemberJourneyTemplateResponse": {"type": "object", "properties": {"journeyTemplate": {"$ref": "#/components/schemas/MemberJourneyTemplateDto"}}}, "MemberMilestoneDto": {"type": "object", "properties": {"iteration": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "weight": {"type": "number", "format": "double"}, "activityMinutes": {"type": "integer", "format": "int32"}, "attendanceMode": {"type": "string"}, "sessionDate": {"type": "string", "format": "date"}}}, "MemberProgramDto": {"type": "object", "properties": {"programStartDate": {"type": "string", "format": "date-time"}, "programEndDate": {"type": "string", "format": "date-time"}, "memberStartDate": {"type": "string", "format": "date"}, "memberLastAttendedDate": {"type": "string", "format": "date"}, "startingWeight": {"type": "number", "format": "float"}, "targetWeight": {"type": "number", "format": "float"}, "milestones": {"type": "array", "items": {"$ref": "#/components/schemas/MemberMilestoneDto"}}}}, "BatchJobDto": {"type": "object", "properties": {"jobExecutionId": {"type": "integer", "description": "Unique job execution identifier", "format": "int64", "example": 123}, "jobName": {"type": "string", "description": "Name of the batch job", "example": "importDppCsvJob"}, "status": {"type": "string", "description": "Current job status", "example": "COMPLETED", "enum": ["STARTING", "STARTED", "STOPPING", "STOPPED", "FAILED", "COMPLETED", "ABANDONED"]}, "startTime": {"type": "string", "description": "Job start timestamp", "format": "date-time"}, "endTime": {"type": "string", "description": "Job end timestamp", "format": "date-time"}, "exitCode": {"type": "string", "description": "Job exit code", "example": "COMPLETED"}, "parameters": {"type": "object", "additionalProperties": {"type": "string", "description": "Job execution parameters"}, "description": "Job execution parameters"}, "steps": {"type": "array", "description": "List of job steps with execution details", "items": {"$ref": "#/components/schemas/BatchStepDto"}}}, "description": "Batch job execution details with steps and parameters"}, "BatchStepDto": {"type": "object", "properties": {"stepExecutionId": {"type": "integer", "description": "Unique step execution identifier", "format": "int64", "example": 456}, "stepName": {"type": "string", "description": "Name of the batch step", "example": "stagingStep"}, "status": {"type": "string", "description": "Current step status", "example": "COMPLETED"}, "startTime": {"type": "string", "description": "Step start timestamp", "format": "date-time"}, "endTime": {"type": "string", "description": "Step end timestamp", "format": "date-time"}, "exitCode": {"type": "string", "description": "Step exit code", "example": "COMPLETED"}, "readCount": {"type": "integer", "description": "Number of items read", "format": "int64", "example": 100}, "writeCount": {"type": "integer", "description": "Number of items written", "format": "int64", "example": 95}, "skipCount": {"type": "integer", "description": "Number of items skipped due to errors", "format": "int64", "example": 5}}, "description": "Batch step execution details with processing statistics"}}}}