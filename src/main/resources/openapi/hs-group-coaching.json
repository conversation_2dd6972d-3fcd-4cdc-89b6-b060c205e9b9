{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://integrationtest.powerofvitality.com/v3/group-coaching"}], "tags": [{"name": "Session Category - controller", "description": "Endpoints for managing group coaching sessions categories"}, {"name": "Appointment controller", "description": "Endpoints for managing appointments"}, {"name": "Attendee controller", "description": "Endpoints for managing Session attendees"}, {"name": "Session controller", "description": "Endpoints for managing group coaching sessions"}], "paths": {"/api/v1/session": {"put": {"tags": ["Session controller"], "summary": "Update a session info", "operationId": "updateSession", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSessionDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseDto"}}}}}}, "post": {"tags": ["Session controller"], "summary": "Create a new session", "operationId": "createSession", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SessionWithAppointmentsRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseWithAppointmentsDto"}}}}}}}, "/api/v1/session/recurring": {"put": {"tags": ["Session controller"], "summary": "Update recurring session time range", "operationId": "updateRecurringSessionDetails", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRecurringSessionDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseWithAppointmentsDto"}}}}}}, "post": {"tags": ["Session controller"], "summary": "Create a new recurring session", "operationId": "createRecurringSession", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecurringSessionRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseWithAppointmentsDto"}}}}}}}, "/api/v1/appointment/{appointmentId}": {"get": {"tags": ["Appointment controller"], "summary": "Get Appointment by id", "operationId": "getAppointmentById", "parameters": [{"name": "appointmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "includeAttendees", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}, "put": {"tags": ["Appointment controller"], "summary": "Update the appointment details", "operationId": "updateAppointmentDetails", "parameters": [{"name": "appointmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointmentDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}}, "/api/v1/session/{sessionId}/cancel": {"post": {"tags": ["Session controller"], "summary": "Update the appointment state", "operationId": "cancelSession", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseDto"}}}}}}}, "/api/v1/session/filter": {"post": {"tags": ["Session controller"], "operationId": "filterSessions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SessionFilterDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageSessionResponseWithStatsDto"}}}}}}}, "/api/v1/attendee/{attendeeId}/state": {"post": {"tags": ["Attendee controller"], "summary": "Update the attendee state", "operationId": "updateAppointmentAttendeeState", "parameters": [{"name": "attendeeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "state", "in": "query", "required": true, "schema": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AttendeeResponseDto"}}}}}}}, "/api/v1/attendee/session/{sessionId}": {"post": {"tags": ["Attendee controller"], "summary": "Add attendee(s) to the session - add in all appointment of the session", "operationId": "addAttendeeToSession", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/AddAttendeeRequest"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentAttendeeDto"}}}}}}}}, "/api/v1/attendee/session/{sessionId}/state": {"post": {"tags": ["Attendee controller"], "summary": "Update attendee state in all active appointments of the session", "operationId": "updateAttendeeStateInSession", "parameters": [{"name": "sessionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "role", "in": "query", "required": true, "schema": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}}, {"name": "state", "in": "query", "required": true, "schema": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttendeeResponseDto"}}}}}}}}, "/api/v1/attendee/booking/{bookingId}/state": {"post": {"tags": ["Attendee controller"], "summary": "Update attendee state By appointment Id", "operationId": "updateAttendeeStateByBooking", "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "role", "in": "query", "required": true, "schema": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}}, {"name": "state", "in": "query", "required": true, "schema": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AttendeeResponseDto"}}}}}}}, "/api/v1/attendee/appointment/{appointmentId}/state": {"post": {"tags": ["Attendee controller"], "summary": "Update attendee state By appointment Id", "operationId": "updateAttendeeStateByAppointmentId", "parameters": [{"name": "appointmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "role", "in": "query", "required": true, "schema": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}}, {"name": "state", "in": "query", "required": true, "schema": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AttendeeResponseDto"}}}}}}}, "/api/v1/attendee/appointment/add": {"post": {"tags": ["Attendee controller"], "summary": "Add attendee(s) to the appointment", "operationId": "addAttendeeToAppointment", "parameters": [{"name": "appointmentId", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}], "requestBody": {"content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/AddAttendeeRequest"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentAttendeeDto"}}}}}}}}, "/api/v1/attendee/_getByIds": {"post": {"tags": ["Attendee controller"], "summary": "Update the attendee state", "operationId": "getAttendeesByIds", "requestBody": {"content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentAttendeeDto"}}}}}}}}, "/api/v1/appointment": {"post": {"tags": ["Appointment controller"], "summary": "Add appointment to the session", "operationId": "addAppointmentToSession", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SessionNewAppointmentDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}}, "/api/v1/appointment/{appointmentId}/submit": {"post": {"tags": ["Appointment controller"], "summary": "Submit the appointment", "operationId": "submitAppointment", "parameters": [{"name": "appointmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}}, "/api/v1/appointment/{appointmentId}/state": {"post": {"tags": ["Appointment controller"], "summary": "Update the appointment state", "operationId": "updateAppointmentState", "parameters": [{"name": "appointmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "appointmentState", "in": "query", "required": true, "schema": {"type": "string", "enum": ["SCHEDULED", "IN_PROGRESS", "FINALISED", "CANCELLED", "ABANDONED"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}}, "/api/v1/appointment/_ids": {"post": {"tags": ["Appointment controller"], "summary": "Get Appointment by ids", "operationId": "getAppointmentByIds", "parameters": [{"name": "includeAttendees", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}}}, "/api/v1/session/{id}": {"get": {"tags": ["Session controller"], "operationId": "getSessionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseWithAppointmentsDto"}}}}}}}, "/api/v1/session/{id}/detailed": {"get": {"tags": ["Session controller"], "operationId": "getDetailedSessionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SessionResponseDetailedDto"}}}}}}}, "/api/v1/category": {"get": {"tags": ["Session Category - controller"], "operationId": "getAllCategories", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}}}}}}, "/api/v1/attendee/session/{id}": {"get": {"tags": ["Attendee controller"], "operationId": "getSessionAttendees", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "appointmentIds", "in": "query", "description": "Appointments ", "required": false, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "roles", "in": "query", "description": "attendees will be filtered by passed roles", "required": false, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}}}, {"name": "states", "in": "query", "description": "Attendee states, <PERSON><PERSON><PERSON> should be in passed state in one of appointments", "required": false, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}}, {"name": "includePersonalInfo", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SessionAttendeeDto"}}}}}}}}, "/api/v1/attendee/appointment/{appointmentId}": {"get": {"tags": ["Attendee controller"], "summary": "Retrieve appointment's attendees", "operationId": "retrieveAppoint<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "appointmentId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "role", "in": "query", "description": "attendees will be filtered by passed roles", "required": false, "schema": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}}, {"name": "states", "in": "query", "description": "Attendee states, <PERSON><PERSON><PERSON> should be in passed state in one of appointments", "required": false, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}}, {"name": "includePersonalInfo", "in": "query", "description": "If true, personal info fetched from member-info / provider-service will fetched e.g firstname, lastname", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentAttendeeDto"}}}}}}}}, "/api/v1/appointment/booking/{bookingId}": {"get": {"tags": ["Appointment controller"], "summary": "Get Appointment by bookingId", "operationId": "getAppointmentByBookingId", "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "includeAttendees", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}}}}}, "components": {"schemas": {"UpdateSessionDto": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "SESSION"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32"}}}, "CategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "description": {"type": "string"}, "state": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "imageUrl": {"type": "string"}}, "description": "The category to which the session belongs."}, "SessionResponseDto": {"required": ["category", "description", "joinLevel", "title", "type"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "type": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "APPOINTMENT"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32", "default": 0}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "recurrencePeriod": {"type": "string", "description": "When Session is recurring this will be populated with the recurrence period. e.g WEEKLY", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "frequency": {"type": "integer", "description": "When Session is recurring this will be populated with the recurrence frequency. e.g 1 with recurrencePeriod Weekly represents every week", "format": "int32"}, "state": {"type": "string", "description": "The current state of the session (e.g., active, inactive).", "enum": ["SCHEDULED", "ON_GOING", "FINALISED", "CANCELLED"]}, "startDate": {"type": "string", "description": "Session start date time, matches start time of first appointment of the session", "format": "date-time"}, "endDate": {"type": "string", "description": "Session end date time, matches end time of last appointment of the session", "format": "date-time"}}}, "UpdateRecurringSessionDto": {"required": ["endTime", "frequency", "id", "period", "startTime"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "period": {"type": "string", "description": "Recurrence period of the session", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "frequency": {"type": "integer", "description": "The frequency of the booking", "format": "int32"}, "startTime": {"type": "string", "description": "The start time of the first appointment", "format": "date-time"}, "endTime": {"type": "string", "description": "The end time of the first appointment", "format": "date-time"}}}, "AppointmentResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "description": "The name of the appointment"}, "description": {"type": "string", "description": "A detailed description of the appointment"}, "state": {"type": "string", "description": "The current state of the appointment", "enum": ["SCHEDULED", "IN_PROGRESS", "FINALISED", "CANCELLED", "ABANDONED"]}, "locked": {"type": "boolean", "description": "Indicates if the members can Join past buffer time "}, "bufferTime": {"type": "integer", "description": "Time in minutes before the appointment starts that members can join", "format": "int32"}, "autoRecord": {"type": "boolean", "description": "Specifies whether the appointment is set to auto-record"}, "minParticipants": {"type": "integer", "description": "The minimum number of participants required", "format": "int32"}, "maxParticipants": {"type": "integer", "description": "The maximum number of participants allowed", "format": "int32"}, "sessionId": {"type": "integer", "format": "int64"}, "summary": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "bookingId": {"type": "integer", "format": "int64"}, "bookingSeriesId": {"type": "integer", "format": "int64"}, "vacantSpots": {"type": "integer", "format": "int32"}, "attendees": {"type": "array", "items": {"$ref": "#/components/schemas/AttendeeResponseDto"}}}, "description": "A list of appointments for the session."}, "AttendeeResponseDto": {"required": ["entityNo", "role"], "type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "role": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}, "id": {"type": "integer", "format": "int64"}, "state": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "displayName": {"type": "string"}, "bmi": {"type": "string"}, "gender": {"type": "string"}, "dateOfBirth": {"type": "string"}}}, "SessionResponseWithAppointmentsDto": {"required": ["category", "description", "joinLevel", "title", "type"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "type": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "APPOINTMENT"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32", "default": 0}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "recurrencePeriod": {"type": "string", "description": "When Session is recurring this will be populated with the recurrence period. e.g WEEKLY", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "frequency": {"type": "integer", "description": "When Session is recurring this will be populated with the recurrence frequency. e.g 1 with recurrencePeriod Weekly represents every week", "format": "int32"}, "state": {"type": "string", "description": "The current state of the session (e.g., active, inactive).", "enum": ["SCHEDULED", "ON_GOING", "FINALISED", "CANCELLED"]}, "startDate": {"type": "string", "description": "Session start date time, matches start time of first appointment of the session", "format": "date-time"}, "endDate": {"type": "string", "description": "Session end date time, matches end time of last appointment of the session", "format": "date-time"}, "appointments": {"type": "array", "description": "A list of appointments for the session.", "items": {"$ref": "#/components/schemas/AppointmentResponseDto"}}}}, "UpdateAppointmentDto": {"type": "object", "properties": {"title": {"type": "string", "description": "The name of the appointment"}, "description": {"type": "string", "description": "A detailed description of the appointment"}, "locked": {"type": "boolean", "description": "Indicates if the members can Join past buffer time "}, "bufferTime": {"type": "integer", "description": "Time in minutes before the appointment starts that members can join", "format": "int32"}, "autoRecord": {"type": "boolean", "description": "Specifies whether the appointment is set to auto-record"}, "minParticipants": {"type": "integer", "description": "The minimum number of participants required", "format": "int32"}, "maxParticipants": {"type": "integer", "description": "The maximum number of participants allowed", "format": "int32"}, "startTime": {"type": "string", "description": "The start time of the appointment", "format": "date-time"}, "endTime": {"type": "string", "description": "The end time of the appointment", "format": "date-time"}}}, "AppointmentRequestDto": {"required": ["endTime", "startTime"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "description": "The name of the appointment"}, "description": {"type": "string", "description": "A detailed description of the appointment"}, "state": {"type": "string", "description": "The current state of the appointment", "enum": ["SCHEDULED", "IN_PROGRESS", "FINALISED", "CANCELLED", "ABANDONED"]}, "locked": {"type": "boolean", "description": "Indicates if the members can Join past buffer time "}, "bufferTime": {"type": "integer", "description": "Time in minutes before the appointment starts that members can join", "format": "int32"}, "autoRecord": {"type": "boolean", "description": "Specifies whether the appointment is set to auto-record"}, "minParticipants": {"type": "integer", "description": "The minimum number of participants required", "format": "int32"}, "maxParticipants": {"type": "integer", "description": "The maximum number of participants allowed", "format": "int32"}, "attendees": {"type": "array", "description": "A list of attendees for the session appointment.", "items": {"$ref": "#/components/schemas/AttendeeDto"}}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}}, "description": "A list of appointments for the session."}, "AttendeeDto": {"required": ["entityNo", "role"], "type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "role": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}, "id": {"type": "integer", "format": "int64"}, "state": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}, "description": "A list of attendees for the session appointment."}, "MemberAudienceDto": {"required": ["alliance"], "type": "object", "properties": {"alliance": {"type": "integer", "format": "int32"}, "employer": {"type": "integer", "format": "int64"}, "branch": {"type": "string"}}, "description": "Member audience to which the session is available."}, "SessionWithAppointmentsRequestDto": {"required": ["categoryId", "description", "joinLevel", "memberAudiences", "title", "type"], "type": "object", "properties": {"title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "type": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "APPOINTMENT"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32", "default": 0}, "categoryId": {"type": "integer", "description": "The category to which the session belongs.", "format": "int64"}, "memberAudiences": {"uniqueItems": true, "type": "array", "description": "Member audience to which the session is available.", "items": {"$ref": "#/components/schemas/MemberAudienceDto"}}, "appointments": {"type": "array", "description": "A list of appointments for the session.", "items": {"$ref": "#/components/schemas/AppointmentRequestDto"}}}}, "RecurringAppointmentRequest": {"required": ["period", "slot"], "type": "object", "properties": {"state": {"type": "string", "description": "The current state of the appointment", "enum": ["SCHEDULED", "IN_PROGRESS", "FINALISED", "CANCELLED", "ABANDONED"]}, "locked": {"type": "boolean", "description": "Indicates if the members can Join past buffer time "}, "bufferTime": {"type": "integer", "description": "Time in minutes before the appointment starts that members can join", "format": "int32"}, "autoRecord": {"type": "boolean", "description": "Specifies whether the appointment is set to auto-record"}, "minParticipants": {"type": "integer", "description": "The minimum number of participants required", "format": "int32"}, "maxParticipants": {"type": "integer", "description": "The maximum number of participants allowed", "format": "int32"}, "attendees": {"type": "array", "description": "A list of attendees for the session appointment.", "items": {"$ref": "#/components/schemas/AttendeeDto"}}, "slot": {"$ref": "#/components/schemas/RecurringTimeSlot"}, "period": {"type": "string", "description": "Recurrence period", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "frequency": {"type": "integer", "description": "The frequency of the booking", "format": "int32"}, "appointmentCount": {"type": "integer", "description": "Total number of appointments in the recurring series", "format": "int32"}, "appointmentNames": {"type": "array", "description": "Names of appointment, first element is name of first appointment", "items": {"type": "string", "description": "Names of appointment, first element is name of first appointment"}}}, "description": "Details of the recurring appointment pattern for the session."}, "RecurringSessionRequestDto": {"required": ["categoryId", "description", "joinLevel", "memberAudiences", "recurringAppointment", "title", "type"], "type": "object", "properties": {"title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "type": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "APPOINTMENT"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32", "default": 0}, "categoryId": {"type": "integer", "description": "The category to which the session belongs.", "format": "int64"}, "memberAudiences": {"uniqueItems": true, "type": "array", "description": "Member audience to which the session is available.", "items": {"$ref": "#/components/schemas/MemberAudienceDto"}}, "recurringAppointment": {"$ref": "#/components/schemas/RecurringAppointmentRequest"}}}, "RecurringTimeSlot": {"required": ["endTime", "startTime"], "type": "object", "properties": {"startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}}, "description": "Initial time slot"}, "PagingRequest": {"type": "object", "properties": {"page": {"type": "integer", "description": "The page number to retrieve, must be greater than or equal to 0.", "format": "int32", "example": 0}, "size": {"type": "integer", "description": "The number of items per page, must be greater than or equal to 1.", "format": "int32", "example": 10}, "sortingColumn": {"type": "string", "description": "The column by which sorting should be applied.", "example": "id"}, "sortOrder": {"type": "string", "description": "The sort order for the selected column, can be ASC or DESC.", "example": "ASC", "enum": ["ASC", "DESC"]}}, "description": "The paging request."}, "SessionAttendeeFilter": {"required": ["entityNo"], "type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "exists": {"type": "boolean"}, "role": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}, "states": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}}}, "description": "Attendee exists in one of appointments of the session"}, "SessionFilterDto": {"type": "object", "properties": {"states": {"uniqueItems": true, "type": "array", "description": "The states to which the session is in.", "items": {"type": "string", "description": "The states to which the session is in.", "enum": ["SCHEDULED", "ON_GOING", "FINALISED", "CANCELLED"]}}, "memberAudiences": {"uniqueItems": true, "type": "array", "description": "Member audience to which the session is available.", "items": {"$ref": "#/components/schemas/MemberAudienceDto"}}, "joinLevel": {"type": "string", "description": "The join level of the session.", "enum": ["SESSION", "APPOINTMENT"]}, "sessionType": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "categoryIds": {"uniqueItems": true, "type": "array", "description": "The category to which the session belongs.", "items": {"type": "integer", "description": "The category to which the session belongs.", "format": "int64"}}, "attendee": {"$ref": "#/components/schemas/SessionAttendeeFilter"}, "paging": {"$ref": "#/components/schemas/PagingRequest"}}}, "PageSessionResponseWithStatsDto": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/SessionResponseWithStatsDto"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"sort": {"$ref": "#/components/schemas/SortObject"}, "offset": {"type": "integer", "format": "int64"}, "unpaged": {"type": "boolean"}, "pageSize": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}}}, "SessionResponseWithStatsDto": {"required": ["category", "description", "joinLevel", "title", "type"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "type": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "APPOINTMENT"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32", "default": 0}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "recurrencePeriod": {"type": "string", "description": "When Session is recurring this will be populated with the recurrence period. e.g WEEKLY", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "frequency": {"type": "integer", "description": "When Session is recurring this will be populated with the recurrence frequency. e.g 1 with recurrencePeriod Weekly represents every week", "format": "int32"}, "state": {"type": "string", "description": "The current state of the session (e.g., active, inactive).", "enum": ["SCHEDULED", "ON_GOING", "FINALISED", "CANCELLED"]}, "startDate": {"type": "string", "description": "Session start date time, matches start time of first appointment of the session", "format": "date-time"}, "endDate": {"type": "string", "description": "Session end date time, matches end time of last appointment of the session", "format": "date-time"}, "appointmentInfo": {"type": "object", "additionalProperties": {"type": "integer", "description": "The statistics of the session. Key value pair where keys are the appointment states and values are the count of appointments in that state.", "format": "int32"}, "description": "The statistics of the session. Key value pair where keys are the appointment states and values are the count of appointments in that state.", "example": {"PENDING": 2, "CONFIRMED": 3, "CANCELLED": 1}}}}, "SortObject": {"type": "object", "properties": {"empty": {"type": "boolean"}, "unsorted": {"type": "boolean"}, "sorted": {"type": "boolean"}}}, "AddAttendeeRequest": {"required": ["entityNo", "role"], "type": "object", "properties": {"entityNo": {"type": "integer", "format": "int64"}, "role": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}}}, "AppointmentAttendeeDto": {"required": ["entityNo", "role"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "sessionId": {"type": "integer", "format": "int64"}, "appointmentId": {"type": "integer", "format": "int64"}, "entityNo": {"type": "integer", "format": "int64"}, "role": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}, "state": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "displayName": {"type": "string"}, "bmi": {"type": "string"}, "gender": {"type": "string"}, "dateOfBirth": {"type": "string"}}}, "SessionNewAppointmentDto": {"required": ["endTime", "sessionId", "startTime"], "type": "object", "properties": {"sessionId": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "description": "The name of the appointment"}, "description": {"type": "string", "description": "A detailed description of the appointment"}, "state": {"type": "string", "description": "The current state of the appointment", "enum": ["SCHEDULED", "IN_PROGRESS", "FINALISED", "CANCELLED", "ABANDONED"]}, "locked": {"type": "boolean", "description": "Indicates if the members can Join past buffer time "}, "bufferTime": {"type": "integer", "description": "Time in minutes before the appointment starts that members can join", "format": "int32"}, "autoRecord": {"type": "boolean", "description": "Specifies whether the appointment is set to auto-record"}, "minParticipants": {"type": "integer", "description": "The minimum number of participants required", "format": "int32"}, "maxParticipants": {"type": "integer", "description": "The maximum number of participants allowed", "format": "int32"}, "attendees": {"type": "array", "description": "A list of attendees for the session appointment.", "items": {"$ref": "#/components/schemas/AttendeeDto"}}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}}}, "SessionResponseDetailedDto": {"required": ["category", "description", "joinLevel", "title", "type"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique identifier of the session.", "format": "int64"}, "title": {"type": "string", "description": "The title of the recurring session."}, "description": {"type": "string", "description": "A detailed description of the recurring session."}, "type": {"type": "string", "description": "The type of the session.", "enum": ["SINGLE", "MULTIPLE", "RECURRING"]}, "locked": {"type": "boolean", "description": "Indicates that members can't join if session is in ON_GOING state."}, "joinLevel": {"type": "string", "description": "The level at which attendees can join the session.", "enum": ["SESSION", "APPOINTMENT"], "default": "APPOINTMENT"}, "enrollmentStartDate": {"type": "string", "description": "The date when enrollment starts (date without time).", "format": "date"}, "enrollmentEndDate": {"type": "string", "description": "The date when enrollment ends (date without time).", "format": "date"}, "gracePeriod": {"type": "integer", "description": "The grace period value (in days).", "format": "int32", "default": 0}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "recurrencePeriod": {"type": "string", "description": "When Session is recurring this will be populated with the recurrence period. e.g WEEKLY", "enum": ["DAILY", "WEEKLY", "MONTHLY"]}, "frequency": {"type": "integer", "description": "When Session is recurring this will be populated with the recurrence frequency. e.g 1 with recurrencePeriod Weekly represents every week", "format": "int32"}, "state": {"type": "string", "description": "The current state of the session (e.g., active, inactive).", "enum": ["SCHEDULED", "ON_GOING", "FINALISED", "CANCELLED"]}, "startDate": {"type": "string", "description": "Session start date time, matches start time of first appointment of the session", "format": "date-time"}, "endDate": {"type": "string", "description": "Session end date time, matches end time of last appointment of the session", "format": "date-time"}, "appointments": {"type": "array", "description": "A list of appointments for the session.", "items": {"$ref": "#/components/schemas/AppointmentResponseDto"}}, "memberAudiences": {"uniqueItems": true, "type": "array", "description": "Member audience to which the session is available.", "items": {"$ref": "#/components/schemas/MemberAudienceDto"}}}}, "SessionAttendeeDto": {"required": ["entityNo", "role"], "type": "object", "properties": {"sessionId": {"type": "integer", "format": "int64"}, "entityNo": {"type": "integer", "format": "int64"}, "role": {"type": "string", "enum": ["PATIENT", "PROVIDER", "ORGANISER"]}, "id": {"type": "integer", "format": "int64"}, "state": {"type": "string", "enum": ["WAIT_LIST", "CONFIRMED", "REQUESTED", "DECLINED", "ATTENDED", "MISSED", "CANCELLED"]}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "displayName": {"type": "string"}, "bmi": {"type": "string"}, "gender": {"type": "string"}, "dateOfBirth": {"type": "string"}, "stateCount": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}}}}}