{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://integrationtest.powerofvitality.com/v3/config-flag-aggregator"}], "paths": {"/api/configuration-flags": {"get": {"tags": ["configuration-flag-controller"], "operationId": "getAllConfigurationFlags", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigurationFlagDto"}}}}}}}, "post": {"tags": ["configuration-flag-controller"], "operationId": "getConfigurationFlags", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigurationFlagRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigurationFlagDto"}}}}}}}}, "/api/configuration-flags/entity/{entityId}": {"get": {"tags": ["configuration-flag-controller"], "operationId": "getConfigurationFlagsForEntity", "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigurationFlagDto"}}}}}}}}, "/api/configuration-flags/cache": {"delete": {"tags": ["configuration-flag-controller"], "operationId": "clearCache", "responses": {"200": {"description": "OK"}}}}, "/api/configuration-flags/cache/entity/{entityId}": {"delete": {"tags": ["configuration-flag-controller"], "operationId": "clearCacheForEntity", "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"ConfigurationFlagRequest": {"type": "object", "properties": {"entityId": {"type": "integer", "format": "int64"}, "includeVapConfig": {"type": "boolean"}, "includeEmployeeIncentivePlan": {"type": "boolean"}, "includeAudience": {"type": "boolean"}, "includeControlCenterFlags": {"type": "boolean"}, "includeDasAttributes": {"type": "boolean"}, "dasCached": {"type": "boolean"}, "controlCenterCached": {"type": "boolean"}}}, "ConfigurationFlagDto": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "object"}}}}}}