{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://integrationtest.powerofvitality.com/v3/entity"}], "paths": {"/api/member-entity-nos": {"get": {"tags": ["Entity"], "operationId": "streamMemberEntityNos", "parameters": [{"name": "alliance", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "employer", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "branch", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/x-ndjson": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}}}}}}