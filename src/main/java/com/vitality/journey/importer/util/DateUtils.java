package com.vitality.journey.importer.util;

import lombok.NoArgsConstructor;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Parses multiple date formats into LocalDate.
 * Supported:
 * - MM/dd/yyyy
 * - M/dd/yy (and generally M/d/yy)
 * - yyyyMMdd
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public final class DateUtils {

    private static final DateTimeFormatter DEFAULT_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final List<DateTimeFormatter> FORMATTERS = new ArrayList<>();

    static {
        // Compact ISO-like
        FORMATTERS.add(DateTimeFormatter.ofPattern("yyyyMMdd", Locale.US));
        // US styles
        FORMATTERS.add(DateTimeFormatter.ofPattern("MM/dd/yyyy", Locale.US));
        FORMATTERS.add(DateTimeFormatter.ofPattern("M/d/yyyy", Locale.US));
        FORMATTERS.add(DateTimeFormatter.ofPattern("M/d/yy", Locale.US));
        FORMATTERS.add(DateTimeFormatter.ofPattern("MM/d/yy", Locale.US));
        FORMATTERS.add(DateTimeFormatter.ofPattern("M/dd/yy", Locale.US));
    }

    public static LocalDate parse(String input) {
        if (input == null) {
            return null;
        }

        String trimmed = input.trim();
        if (trimmed.isEmpty()) {
            return null;
        }

        // Normalize common separators/spaces
        String candidate = trimmed.replace("\\n", " ")
                .replace("\\r", " ")
                .replaceAll("\\s+", " ");

        for (DateTimeFormatter f : FORMATTERS) {
            try {
                return java.time.LocalDate.parse(candidate, f);
            } catch (DateTimeParseException ignored) {
                // ignore
            }
        }
        throw new IllegalArgumentException("Unparseable date: '" + input + "'");
    }

    public static String format(LocalDate date) {
        return date.format(DEFAULT_DATE_FMT);
    }

    public static LocalDateTime startOfDay(LocalDate d) {
        return d == null ? null : d.atStartOfDay().truncatedTo(ChronoUnit.SECONDS);
    }

    public static LocalDateTime endOfDay(LocalDate d) {
        return d == null ? null : d.atTime(LocalTime.MAX).truncatedTo(ChronoUnit.SECONDS);
    }

    public static LocalDate shiftToMonday(LocalDate date) {
        if (date == null) return null;
        DayOfWeek dow = date.getDayOfWeek();
        int delta = (DayOfWeek.MONDAY.getValue() - dow.getValue() + 7) % 7;
        return date.plusDays(delta);
    }

    public static LocalDate[] weekIterationWindow(LocalDate programStartMonday, int iteration) {
        // iteration is 1-based
        LocalDate start = programStartMonday.plusWeeks(iteration - 1L);
        LocalDate end = start.plusDays(6);
        return new LocalDate[]{start, end};
    }

    public static boolean isMonday(LocalDate date) {
        return date != null && date.getDayOfWeek() == DayOfWeek.MONDAY;
    }
}

