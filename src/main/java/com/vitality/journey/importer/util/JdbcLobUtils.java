package com.vitality.journey.importer.util;

import lombok.NoArgsConstructor;

import javax.sql.rowset.serial.SerialClob;
import java.io.BufferedReader;
import java.io.IOException;
import java.sql.Clob;
import java.sql.SQLException;

/**
 * Utility helper for working with JDBC LOB types.
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public final class JdbcLobUtils {

    /**
     * Reads the full contents of the given CLOB into a String.
     *
     * @param clob the CLOB to read; may be null
     * @return the CLOB contents as a String, or null if clob is null or empty
     * @throws IllegalStateException if an I/O or SQL error occurs while reading
     */
    public static String readClobAsString(Clob clob) {
        if (clob == null) {
            return null;
        }
        try (BufferedReader reader = new BufferedReader(clob.getCharacterStream())) {
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            String result = builder.toString();
            return result.isEmpty() ? null : result;
        } catch (IOException | SQLException e) {
            throw new IllegalStateException("Failed to read CLOB", e);
        }
    }

    public static Clob toClob(String value) {
        try {
            return new SerialClob(value.toCharArray());
        } catch (Exception e) {
            return null;
        }
    }
}
