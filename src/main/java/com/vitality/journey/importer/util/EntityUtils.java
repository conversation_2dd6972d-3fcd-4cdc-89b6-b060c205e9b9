package com.vitality.journey.importer.util;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class EntityUtils {
    public static String getTableName(Class<?> entityClass) {
        if (entityClass.getAnnotation(Entity.class) == null) {
            throw new IllegalArgumentException("Class " + entityClass.getName() + " is not an entity");
        }

        Table table = entityClass.getAnnotation(Table.class);
        return String.format("%s.%s", table.schema(), table.name());
    }
}
