package com.vitality.journey.importer.util;

import lombok.NoArgsConstructor;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class CsvUtils {
    public static String[] parseCsvHeader(Path filePath) throws IOException {
        // Read and parse headers from first line
        String headerLine = readFirstLine(filePath);
        String[] headers = headerLine.split(",");
        // Trim headers and handle quotes
        for (int i = 0; i < headers.length; i++) {
            headers[i] = headers[i].trim().replaceAll("(^\")|(\"$)", "");
        }
        return headers;
    }

    private static String readFirstLine(Path filePath) throws IOException {
        try (var reader = Files.newBufferedReader(filePath)) {
            String line = reader.readLine();
            return line != null ? line : "";
        }
    }
}
