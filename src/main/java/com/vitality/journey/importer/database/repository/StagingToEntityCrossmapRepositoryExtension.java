package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.StagingToEntityCrossmap;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface StagingToEntityCrossmapRepositoryExtension extends StagingToEntityCrossmapRepository {

    @Query("""
            SELECT c
            FROM StagingToEntityCrossmap c
            WHERE c.id.entityRefId = :entityId
              AND c.id.entityRefType = :entityType
              AND c.id.importJobId = :importJobId
        """)
    Optional<StagingToEntityCrossmap> findByEntityReference(long importJobId, long entityId, String entityType);

    @Query("""
            SELECT c
            FROM StagingToEntityCrossmap c
            WHERE c.id.entityRefId = :entityId
              AND c.id.entityRefType = :entityType
              AND c.id.importJobId = (
                  SELECT MAX(c2.id.importJobId)
                  FROM StagingToEntityCrossmap c2
                  WHERE c2.id.entityRefId = :entityId
                    AND c2.id.entityRefType = :entityType
              )
        """)
    Optional<StagingToEntityCrossmap> findLatestByEntityReference(long entityId, String entityType);
}
