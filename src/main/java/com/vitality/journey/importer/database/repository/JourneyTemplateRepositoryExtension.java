package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.JourneyTemplate;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Primary
@Repository
public interface JourneyTemplateRepositoryExtension extends JourneyTemplateRepository {

    @Query("select jt from JourneyTemplate jt where jt.journeyType = :journeyType and jt.milestoneType = :milestoneType")
    Optional<JourneyTemplate> findByType(String journeyType, String milestoneType);
}
