package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.JourneyProgramTemplate;
import jakarta.persistence.LockModeType;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

@Primary
@Repository
public interface JourneyProgramTemplateRepositoryExtension extends JourneyProgramTemplateRepository {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("select jpt from JourneyProgramTemplate jpt " +
            "where jpt.journeyTemplate.id = :journeyTemplateId " +
            "and jpt.name = :name " +
            "and jpt.startDate = :startDate " +
            "and jpt.endDate = :endDate")
    Optional<JourneyProgramTemplate> findByNameAndDates(long journeyTemplateId,
                                                        String name,
                                                        LocalDateTime startDate,
                                                        LocalDateTime endDate);
}
