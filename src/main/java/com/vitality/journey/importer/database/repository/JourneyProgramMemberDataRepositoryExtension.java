package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.JourneyProgramMemberData;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Primary
@Repository
public interface JourneyProgramMemberDataRepositoryExtension extends JourneyProgramMemberDataRepository {

    @Query("select jpmd from JourneyProgramMemberData jpmd " +
            "where jpmd.member.id = :memberId " +
            "and jpmd.journeyProgramTemplate.id = :jptId")
    Optional<JourneyProgramMemberData> findForMember(Long memberId, long jptId);

    @Query("select jpmd from JourneyProgramMemberData jpmd " +
            "join fetch jpmd.journeyProgramTemplate jpt " +
            "join fetch jpmd.journeyMilestoneMemberDatas jmmd " +
            "join fetch jmmd.journeyMilestoneTemplate " +
            "join fetch jpmd.member m " +
            "join jpt.journeyTemplate jt " +
            "where m.uniqueId = :memberUniqueId " +
            "and m.employerId = :employerId " +
            "and jt.journeyType = :journeyType " +
            "order by jpmd.updatedAt desc, jpmd.createdAt desc")
    Optional<JourneyProgramMemberData> findLatest(String memberUniqueId, long employerId, String journeyType);

    @Query("select jpmd from JourneyProgramMemberData jpmd " +
            "join fetch jpmd.journeyProgramTemplate jpt " +
            "join fetch jpmd.journeyMilestoneMemberDatas jmmd " +
            "join fetch jmmd.journeyMilestoneTemplate " +
            "join fetch jpmd.member m " +
            "join jpt.journeyTemplate jt " +
            "where m.entityNo = :entityNo " +
            "and jt.journeyType = :journeyType " +
            "order by jpmd.updatedAt desc, jpmd.createdAt desc")
    Optional<JourneyProgramMemberData> findLatest(long entityNo, String journeyType);
}
