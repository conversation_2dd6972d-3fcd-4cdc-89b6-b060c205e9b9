package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Primary
@Repository
public interface StagingRecordRepositoryExtension extends StagingRecordRepository {
    String FIND_BY_ID_PAGED = "findByIdPaged";

    @Query("SELECT s FROM StagingRecord s WHERE s.id = :id")
    Page<StagingRecord> findByIdPaged(@Param("id") Long id, Pageable pageable);

    String FIND_BY_JOB_ID_AND_STATUS = "findByJobIdAndStatus";

    @Query("SELECT s FROM StagingRecord s " +
        "JOIN StagingToEntityCrossmap c ON s.id = c.id.stagingRecordId AND c.id.entityRefId = :jobExecutionId " +
        "LEFT JOIN FETCH s.stagingRecordErrors se " +
        "WHERE c.id.importJobId = :jobExecutionId AND c.id.entityRefType = 'BATCH_JOB_EXECUTION' AND s.status = :status " +
        "ORDER BY s.id")
    Page<StagingRecord> findByJobIdAndStatus(@Param("jobExecutionId") Long jobExecutionId,
                                             @Param("status") String status,
                                             Pageable pageable);

    @Query("SELECT s FROM StagingRecord s " +
        "JOIN StagingToEntityCrossmap c ON s.id = c.id.stagingRecordId AND c.id.entityRefId = :jobExecutionId " +
        "LEFT JOIN FETCH s.stagingRecordErrors se " +
        "WHERE c.id.importJobId = :jobExecutionId AND c.id.entityRefType = 'BATCH_JOB_EXECUTION' " +
        "ORDER BY s.id")
    Page<StagingRecord> findByJobId(@Param("jobExecutionId") Long jobExecutionId, Pageable pageable);

    @Query("SELECT e FROM StagingRecordError e " +
        "JOIN FETCH StagingRecord s ON e.stagingRecord = s " +
        "JOIN StagingToEntityCrossmap c ON s.id = c.id.stagingRecordId AND c.id.entityRefId = :jobExecutionId " +
        "WHERE c.id.importJobId = :jobExecutionId AND c.id.entityRefType = 'BATCH_JOB_EXECUTION' " +
        "ORDER BY e.id")
    Page<StagingRecordError> findErrorsByJobId(@Param("jobExecutionId") Long jobExecutionId, Pageable pageable);
}
