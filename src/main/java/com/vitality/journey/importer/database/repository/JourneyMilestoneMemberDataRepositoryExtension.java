package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneMemberData;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Primary
@Repository
public interface JourneyMilestoneMemberDataRepositoryExtension extends JourneyMilestoneMemberDataRepository {

    @Query("select jmmd from JourneyMilestoneMemberData jmmd " +
            "where jmmd.member.id = :memberId " +
            "and jmmd.journeyMilestoneTemplate.id = :jmtId")
    Optional<JourneyMilestoneMemberData> findForMember(Long memberId, long jmtId);
}
