package com.vitality.journey.importer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import za.co.discovery.health.hs.resourceserver.config.JwtDecoderConfig;
import za.co.discovery.health.hs.resourceserver.config.OAuth2WebClientConfig;
import za.co.discovery.health.hs.resourceserver.config.PlatformResourceServerConfig;

@Import({
    JwtDecoderConfig.class,
    OAuth2WebClientConfig.class,
    PlatformResourceServerConfig.class
})
@SpringBootApplication
public class JourneyImporter {
    public static void main(String[] args) {
        SpringApplication.run(JourneyImporter.class, args);
    }
}

