package com.vitality.journey.importer.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.model.MemberRecord;
import com.vitality.journey.importer.model.dto.MemberDto;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public abstract class MemberMapper {

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "uniqueId", source = "uniqueId")
    @Mapping(target = "employerId", source = "employerId")
    @Mapping(target = "cdcIdentifier", source = "cdcId")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "lastName")
    @Mapping(target = "createdAt", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "updatedAt", expression = "java(java.time.LocalDateTime.now())")
    public abstract Member toEntity(MemberRecord dto);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "entityNo", source = "entityNo")
    @Mapping(target = "uniqueId", source = "uniqueId")
    @Mapping(target = "employerId", source = "employerId")
    @Mapping(target = "cdcId", source = "cdcIdentifier")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "lastName")
    @Mapping(target = "createdAt", source = "createdAt")
    @Mapping(target = "updatedAt", source = "updatedAt")
    public abstract MemberDto toDto(Member entity);

    @Mapping(target = "uniqueId", source = "uniqueId")
    @Mapping(target = "employerId", source = "employerId")
    @Mapping(target = "cdcId", source = "cdcIdentifier")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "lastName")
    public abstract MemberRecord toRecord(Member dto);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "uniqueId", source = "uniqueId")
    @Mapping(target = "employerId", source = "employerId")
    @Mapping(target = "cdcIdentifier", source = "cdcId")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "lastName")
    @Mapping(target = "updatedAt", expression = "java(java.time.LocalDateTime.now())")
    public abstract void mergeToEntity(MemberRecord dto, @MappingTarget Member entity);

    public static String getText(JsonNode node, String key) {
        if (node.has(key) && node.get(key).isTextual()) {
            return node.get(key).asText();
        }
        return null;
    }
}
