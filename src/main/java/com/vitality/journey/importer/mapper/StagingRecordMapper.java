package com.vitality.journey.importer.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import com.vitality.journey.importer.exception.DataProcessingException;
import com.vitality.journey.importer.model.dto.StagingRecordDto;
import com.vitality.journey.importer.model.dto.StagingRecordErrorDto;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.csv.ParsedCsvRow;
import com.vitality.journey.importer.util.CustomMediaTypes;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.sql.Clob;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Mapper(componentModel = "spring")
public abstract class StagingRecordMapper {

    @Autowired
    protected ObjectMapper mapper;

    public StagingRecord toEntity(ParsedCsvRow csvRow) {
        LocalDateTime now = LocalDateTime.now();

        StagingRecord stagingRecord = new StagingRecord();
        stagingRecord.setCreatedAt(now);
        if (csvRow.isSuccess()) {
            Clob clob = JdbcLobUtils.toClob(csvRow.json().toString());
            stagingRecord.setPayload(clob);
            stagingRecord.setType(MediaType.APPLICATION_JSON.getSubtype());
            stagingRecord.setStatus(RecordStatus.LOADED.name());
        } else {
            stagingRecord.setPayload(JdbcLobUtils.toClob(csvRow.rawLine()));
            stagingRecord.setType(CustomMediaTypes.TEXT_CSV.getSubtype());
            stagingRecord.setStatus(RecordStatus.ERROR.name());

            StagingRecordError error = new StagingRecordError();
            error.setStagingRecord(stagingRecord);
            error.setCreatedAt(now);

            if (csvRow.error() != null) {
                String stackTrace = ExceptionUtils.getStackTrace(csvRow.error());
                Clob clob = JdbcLobUtils.toClob(stackTrace);
                error.setErrorMessage(clob);
            }

            stagingRecord.getStagingRecordErrors().add(error);
        }
        return stagingRecord;
    }

    @Mapping(target = "jsonPayload", expression = "java(parseJsonPayload(entity))")
    public abstract StagingRecordDto toDto(StagingRecord entity);

    public StagingRecordErrorDto toDto(StagingRecordError entity) {
        String errorMessage = JdbcLobUtils.readClobAsString(entity.getErrorMessage());
        StagingRecord stagingRecord = entity.getStagingRecord();

        return new StagingRecordErrorDto(
            entity.getId(),
            stagingRecord.getId(),
            entity.getStepId(),
            stagingRecord.getStatus(),
            entity.getCreatedAt(),
            errorMessage
        );
    }

    protected Map<String, Object> parseJsonPayload(StagingRecord entity) {
        if (entity == null) {
            return Collections.emptyMap();
        }
        if (!MediaType.APPLICATION_JSON.getSubtype().equals(entity.getType())) {
            return Collections.emptyMap();
        }
        Clob clob = entity.getPayload();
        if (clob == null) {
            return Collections.emptyMap();
        }
        try {
            String json = JdbcLobUtils.readClobAsString(clob);
            if (StringUtils.hasText(json)) {
                return mapper.readValue(json, new TypeReference<>() {
                });
            }
            return Collections.emptyMap();
        } catch (IOException e) {
            throw new DataProcessingException("Failed to parse JSON from CLOB", e);
        }
    }
}
