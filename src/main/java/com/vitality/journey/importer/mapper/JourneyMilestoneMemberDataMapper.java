package com.vitality.journey.importer.mapper;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneMemberData;
import com.vitality.journey.importer.model.dto.MemberMilestoneDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface JourneyMilestoneMemberDataMapper {

    @Mapping(target = "iteration", source = "journeyMilestoneTemplate.iteration")
    @Mapping(target = "startDate", source = "journeyMilestoneTemplate.startDate")
    @Mapping(target = "endDate", source = "journeyMilestoneTemplate.endDate")
    @Mapping(target = "weight", source = "weight")
    @Mapping(target = "activityMinutes", source = "physicalActivityMin")
    @Mapping(target = "attendanceMode", source = "sessionAttendanceMode")
    @Mapping(target = "sessionDate", source = "sessionAttendedDate")
    MemberMilestoneDto toDto(JourneyMilestoneMemberData entity);
}
