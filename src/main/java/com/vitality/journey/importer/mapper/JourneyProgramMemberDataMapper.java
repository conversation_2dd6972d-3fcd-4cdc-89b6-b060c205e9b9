package com.vitality.journey.importer.mapper;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyTemplate;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.model.dto.MemberJourneyTemplateDto;
import com.vitality.journey.importer.model.dto.MemberMilestoneDto;
import com.vitality.journey.importer.model.dto.MemberProgramDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Mapper(componentModel = "spring")
public abstract class JourneyProgramMemberDataMapper {

    private final Map<String, String> wellsparkDppMigrationAttrs =
            Map.of("WELLSPARK_DPP_MIGRATION", Boolean.TRUE.toString());

    @Autowired
    protected MemberMapper memberMapper;

    @Autowired
    protected JourneyMilestoneMemberDataMapper milestoneMapper;

    @Mapping(target = "programStartDate", source = "journeyProgramTemplate.startDate")
    @Mapping(target = "programEndDate", source = "journeyProgramTemplate.endDate")
    @Mapping(target = "memberStartDate", source = "memberStartDate")
    @Mapping(target = "memberLastAttendedDate", source = "memberLastAttendedDate")
    @Mapping(target = "startingWeight", source = "startingWeight")
    @Mapping(target = "targetWeight", source = "targetWeight")
    @Mapping(target = "milestones", expression = "java(toMemberMilestoneDtoList(programMemberData.getJourneyMilestoneMemberDatas()))")
    public abstract MemberProgramDto toProgramDto(JourneyProgramMemberData programMemberData);

    public MemberJourneyTemplateDto toJourneyDto(JourneyProgramMemberData programMemberData) {
        Objects.requireNonNull(programMemberData);

        MemberProgramDto memberProgramDto = toProgramDto(programMemberData);

        Member member = programMemberData.getMember();
        JourneyTemplate journeyTemplate = programMemberData.getJourneyProgramTemplate().getJourneyTemplate();
        return new MemberJourneyTemplateDto(
                journeyTemplate.getJourneyType(),
                journeyTemplate.getMilestoneType(),
                programMemberData.getJourneyProgramTemplate().getName(),
                wellsparkDppMigrationAttrs,
                memberMapper.toDto(member),
                memberProgramDto
        );
    }

    protected List<MemberMilestoneDto> toMemberMilestoneDtoList(Collection<JourneyMilestoneMemberData> milestoneMemberData) {
        return milestoneMemberData.stream()
                .map(milestoneMapper::toDto)
                .sorted(Comparator.comparing(MemberMilestoneDto::iteration)).toList();
    }
}
