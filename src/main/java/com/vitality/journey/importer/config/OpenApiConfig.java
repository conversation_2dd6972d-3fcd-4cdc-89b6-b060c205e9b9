package com.vitality.journey.importer.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Value("${openapi.servers:}")
    private List<String> serverUrls;

    @Bean
    public OpenAPI customOpenAPI() {
        List<Server> serverList = serverUrls.stream().map(url -> {
            Server server = new Server();
            server.setUrl(url);
            return server;
        }).toList();

        return new OpenAPI()
            .servers(serverList)
            .info(new Info()
                .title("Journey Importer API")
                .description("API for importing and processing journey data for health and wellness programs. Supports CSV file uploads, batch processing, and job monitoring.")
                .version("1.0.0"));
    }
}
