package com.vitality.journey.importer.service.imprt.staging;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.exception.DataProcessingException;
import com.vitality.journey.importer.model.AttendanceMode;
import com.vitality.journey.importer.model.EntryType;
import com.vitality.journey.importer.model.MemberMilestoneData;
import com.vitality.journey.importer.model.MemberRecord;
import com.vitality.journey.importer.model.ProgramMemberData;
import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.util.DateUtils;

import java.io.IOException;
import java.sql.Clob;
import java.sql.SQLException;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StagingRecordReader {
    private static final Pattern SESSION_DATA_PATTERN = Pattern.compile("^\\s*([LAX])\\s*(?:-)?\\s*(\\d{8})?\\s*$", Pattern.CASE_INSENSITIVE);
    private static final Pattern HEADER_PATTERN = Pattern.compile("^Week\\s+(\\d+)\\s+(Weight|Activity|Session)$", Pattern.CASE_INSENSITIVE);

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private final Map<String, String> parsedRecord;
    private final long employerId;

    public static StagingRecordReader of(StagingRecord stagingRecord, long employerId) {
        return new StagingRecordReader(stagingRecord, employerId);
    }

    @SuppressWarnings("unchecked")
    private StagingRecordReader(StagingRecord stagingRecord, long employerId) {
        this.employerId = employerId;
        try {
            Clob payload = stagingRecord.getPayload();
            parsedRecord = MAPPER.readValue(payload.getCharacterStream(), Map.class);
        } catch (SQLException | IOException e) {
            throw new DataProcessingException("Failed to parse JSON from CLOB", e);
        }
    }

    public MemberRecord readMember() {
        try {
            return new MemberRecord(
                    parsedRecord.get(DppCsvHeader.UNIQUE_ID),
                    employerId,
                    parsedRecord.get(DppCsvHeader.CDC_IDENTIFIER),
                    parsedRecord.get(DppCsvHeader.FIRST_NAME),
                    parsedRecord.get(DppCsvHeader.LAST_NAME)
            );
        } catch (Exception e) {
            throw new StagingRecordReaderException("Failed to read member data", e);
        }
    }

    public ProgramMemberData readMemberProgramData() {
        try {
            List<MemberMilestoneData> iterations = readIterationsData();

            return new ProgramMemberData(
                    readMember(),
                    parsedRecord.get(DppCsvHeader.CLASS),
                    Float.parseFloat(parsedRecord.get(DppCsvHeader.STARTING_WEIGHT)),
                    Float.parseFloat(parsedRecord.get(DppCsvHeader.TARGET_WEIGHT)),
                    DateUtils.parse(parsedRecord.get(DppCsvHeader.CLASS_DATE)),
                    DateUtils.parse(parsedRecord.get(DppCsvHeader.PROGRAM_START_DATE)),
                    DateUtils.parse(parsedRecord.get(DppCsvHeader.LAST_SESSION_DATE)),
                    iterations
            );
        } catch (Exception e) {
            throw new StagingRecordReaderException("Failed to read member program data", e);
        }
    }

    public List<MemberMilestoneData> readIterationsData() {
        try {
            Map<Short, MemberMilestoneData> iterations = new TreeMap<>();

            parsedRecord.forEach((k, v) -> {
                Matcher matcher = HEADER_PATTERN.matcher(k);
                if (matcher.matches()) {
                    short week = Short.parseShort(matcher.group(1));
                    String type = matcher.group(2);

                    MemberMilestoneData iterationData = iterations.computeIfAbsent(week, MemberMilestoneData::new);

                    EntryType entryType = EntryType.fromName(type);

                    if (EntryType.SESSION == entryType) {
                        setSessionData(v, iterationData);
                    } else if (EntryType.WEIGHT == entryType) {
                        iterationData.setWeight(Double.parseDouble(v));
                    } else if (EntryType.ACTIVITY == entryType) {
                        iterationData.setActivityMinutes(Integer.parseInt(v));
                    }
                }
            });
            return iterations.values().stream()
                    .sorted(Comparator.comparing(MemberMilestoneData::getIteration))
                    .toList();
        } catch (Exception e) {
            throw new StagingRecordReaderException("Failed to read iterations data", e);
        }
    }

    private static void setSessionData(String v, MemberMilestoneData iterationData) {
        Matcher sessionMatcher = SESSION_DATA_PATTERN.matcher(v);
        if (sessionMatcher.matches()) {
            String mode = sessionMatcher.group(1);
            String date = sessionMatcher.group(2);

            AttendanceMode attendanceMode = AttendanceMode.fromId(mode);
            iterationData.setAttendanceMode(attendanceMode);
            if (attendanceMode != AttendanceMode.NOT_ATTENDED) {
                iterationData.setSessionDate(DateUtils.parse(date));
            }
        }
    }
}
