package com.vitality.journey.importer.service.batch;

import com.vitality.journey.importer.model.batch.BatchJobDto;
import com.vitality.journey.importer.model.batch.BatchStepDto;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class BatchJobService {

    private final JdbcTemplate jdbcTemplate;
    private final BatchJobRowMapper batchJobRowMapper = new BatchJobRowMapper();
    private final BatchStepRowMapper batchStepRowMapper = new BatchStepRowMapper();

    public List<BatchJobDto> getAllJobs() {
        String sql = """
            SELECT je.JOB_EXECUTION_ID, ji.JOB_NAME, je.STATUS, je.START_TIME, je.END_TIME, je.EXIT_CODE
            FROM JOURNEY_IMPORT.BATCH_JOB_EXECUTION je
            JOIN JOURNEY_IMPORT.BATCH_JOB_INSTANCE ji ON je.JOB_INSTANCE_ID = ji.JOB_INSTANCE_ID
            ORDER BY je.CREATE_TIME DESC
            """;

        List<BatchJobDto> jobs = jdbcTemplate.query(sql, batchJobRowMapper);
        jobs.forEach(job -> job.setParameters(getJobParameters(job.getJobExecutionId())));
        return jobs;
    }

    public Optional<BatchJobDto> getJobById(Long jobExecutionId) {
        String sql = """
            SELECT je.JOB_EXECUTION_ID, ji.JOB_NAME, je.STATUS, je.START_TIME, je.END_TIME, je.EXIT_CODE
            FROM JOURNEY_IMPORT.BATCH_JOB_EXECUTION je
            JOIN JOURNEY_IMPORT.BATCH_JOB_INSTANCE ji ON je.JOB_INSTANCE_ID = ji.JOB_INSTANCE_ID
            WHERE je.JOB_EXECUTION_ID = ?
            """;

        Optional<BatchJobDto> job = Optional.ofNullable(jdbcTemplate.queryForObject(sql, batchJobRowMapper, jobExecutionId));

        job.ifPresent(j -> {
            j.setParameters(getJobParameters(jobExecutionId));
            j.setSteps(getStepsByJobExecutionId(jobExecutionId));
        });

        return job;
    }

    private List<BatchStepDto> getStepsByJobExecutionId(Long jobExecutionId) {
        String sql = """
            SELECT STEP_EXECUTION_ID, STEP_NAME, STATUS, START_TIME, END_TIME, EXIT_CODE, EXIT_MESSAGE,
                   READ_COUNT, WRITE_COUNT, (READ_SKIP_COUNT + WRITE_SKIP_COUNT + PROCESS_SKIP_COUNT) as SKIP_COUNT
            FROM JOURNEY_IMPORT.BATCH_STEP_EXECUTION
            WHERE JOB_EXECUTION_ID = ?
            ORDER BY STEP_EXECUTION_ID
            """;

        return jdbcTemplate.query(sql, batchStepRowMapper, jobExecutionId);
    }

    private static class BatchJobRowMapper implements RowMapper<BatchJobDto> {
        @Override
        public BatchJobDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            return BatchJobDto.builder()
                .jobExecutionId(rs.getLong("JOB_EXECUTION_ID"))
                .jobName(rs.getString("JOB_NAME"))
                .status(rs.getString("STATUS"))
                .startTime(rs.getTimestamp("START_TIME") != null ? rs.getTimestamp("START_TIME").toLocalDateTime() : null)
                .endTime(rs.getTimestamp("END_TIME") != null ? rs.getTimestamp("END_TIME").toLocalDateTime() : null)
                .exitCode(rs.getString("EXIT_CODE"))
                .build();
        }
    }

    private static class BatchStepRowMapper implements RowMapper<BatchStepDto> {
        @Override
        public BatchStepDto mapRow(ResultSet rs, int rowNum) throws SQLException {
            return BatchStepDto.builder()
                .stepExecutionId(rs.getLong("STEP_EXECUTION_ID"))
                .stepName(rs.getString("STEP_NAME"))
                .status(rs.getString("STATUS"))
                .startTime(rs.getTimestamp("START_TIME") != null ? rs.getTimestamp("START_TIME").toLocalDateTime() : null)
                .endTime(rs.getTimestamp("END_TIME") != null ? rs.getTimestamp("END_TIME").toLocalDateTime() : null)
                .exitCode(rs.getString("EXIT_CODE"))
                .exitMessage(rs.getString("EXIT_MESSAGE"))
                .readCount(rs.getLong("READ_COUNT"))
                .writeCount(rs.getLong("WRITE_COUNT"))
                .skipCount(rs.getLong("SKIP_COUNT"))
                .build();
        }
    }

    private Map<String, String> getJobParameters(Long jobExecutionId) {
        String sql = """
            SELECT PARAMETER_NAME, PARAMETER_VALUE
            FROM JOURNEY_IMPORT.BATCH_JOB_EXECUTION_PARAMS
            WHERE JOB_EXECUTION_ID = ?
            """;

        return jdbcTemplate.query(sql, rs -> {
            Map<String, String> params = new HashMap<>();
            while (rs.next()) {
                params.put(rs.getString("PARAMETER_NAME"), rs.getString("PARAMETER_VALUE"));
            }
            return params;
        }, jobExecutionId);
    }
}
