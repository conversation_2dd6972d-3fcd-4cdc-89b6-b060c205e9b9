package com.vitality.journey.importer.service.imprt.staging;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import com.vitality.journey.importer.database.repository.StagingRecordErrorRepository;
import com.vitality.journey.importer.database.repository.StagingRecordRepository;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class StagingRecordUpdater {

    private final StagingRecordRepository stagingRecordRepository;
    private final StagingRecordErrorRepository stagingRecordErrorRepository;

    @Transactional
    public void saveError(StagingRecord stagingRecord, long stepId, Throwable t) {
        // Update staging record status to ERROR
        stagingRecord.setStatus(RecordStatus.ERROR.name());
        stagingRecordRepository.save(stagingRecord);

        // Create error record
        StagingRecordError error = new StagingRecordError();
        error.setStagingRecord(stagingRecord);
        error.setStepId(stepId);
        error.setErrorMessage(JdbcLobUtils.toClob(ExceptionUtils.getStackTrace(t)));
        error.setCreatedAt(LocalDateTime.now());
        stagingRecordErrorRepository.save(error);
    }

    @Transactional
    public void updateStatus(long stagingRecordId, RecordStatus status) {
        stagingRecordRepository.findById(stagingRecordId)
            .ifPresent(stagingRecord -> {
                stagingRecord.setStatus(status.name());
                stagingRecordRepository.save(stagingRecord);
            });
    }
}
