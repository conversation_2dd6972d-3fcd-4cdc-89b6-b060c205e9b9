package com.vitality.journey.importer.service.journey;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramTemplate;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.JourneyMilestoneMemberDataRepositoryExtension;
import com.vitality.journey.importer.database.repository.JourneyProgramMemberDataRepositoryExtension;
import com.vitality.journey.importer.database.repository.JourneyProgramTemplateRepositoryExtension;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import com.vitality.journey.importer.mapper.JourneyProgramMemberDataMapper;
import com.vitality.journey.importer.model.AttendanceMode;
import com.vitality.journey.importer.model.MemberMilestoneData;
import com.vitality.journey.importer.model.ProgramMemberData;
import com.vitality.journey.importer.model.dto.MemberJourneyTemplateDto;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class JourneyMemberService {

    private final MemberRepositoryExtension memberRepository;
    private final JourneyProgramTemplateRepositoryExtension jptRepository;
    private final JourneyProgramMemberDataRepositoryExtension jpmdRepository;
    private final JourneyMilestoneMemberDataRepositoryExtension jmmdRepository;
    private final JourneyProgramMemberDataMapper jpmdMapper;

    public Optional<MemberJourneyTemplateDto> getJourneyTemplateDto(String memberUniqueId, long employerId, String journeyType) {
        return jpmdRepository.findLatest(memberUniqueId, employerId, journeyType).map(jpmdMapper::toJourneyDto);
    }

    public Optional<MemberJourneyTemplateDto> getJourneyTemplateDto(long entityNo, String journeyType) {
        return jpmdRepository.findLatest(entityNo, journeyType).map(jpmdMapper::toJourneyDto);
    }

    @Transactional(noRollbackFor = {DataIntegrityViolationException.class})
    public JourneyProgramMemberData saveProgramMemberData(long journeyProgramTemplateId,
                                                          ProgramMemberData memberDataDto) {

        JourneyProgramMemberData memberData = findOrCreate(journeyProgramTemplateId, memberDataDto);

        Map<Short, MemberMilestoneData> memberIterations = memberDataDto.iterations().stream()
            .collect(Collectors.toMap(MemberMilestoneData::getIteration, Function.identity(),
                (a, b) -> b, LinkedHashMap::new));

        JourneyProgramTemplate journeyProgramTemplate = memberData.getJourneyProgramTemplate();
        for (JourneyMilestoneTemplate milestoneTemplate : journeyProgramTemplate.getJourneyMilestoneTemplates()) {
            MemberMilestoneData iterationData = memberIterations.get(milestoneTemplate.getIteration());

            Set<JourneyMilestoneMemberData> journeyMilestoneMemberDatas = memberData.getJourneyMilestoneMemberDatas();

            Member member = memberData.getMember();

            JourneyMilestoneMemberData milestoneMemberData = jmmdRepository
                .findForMember(member.getId(), milestoneTemplate.getId())
                .orElseGet(() -> {
                    JourneyMilestoneMemberData memberMilestone = new JourneyMilestoneMemberData();
                    memberMilestone.setCreatedAt(LocalDateTime.now());
                    memberMilestone.setJourneyMilestoneTemplate(milestoneTemplate);
                    memberMilestone.setJourneyProgramMemberData(memberData);
                    memberMilestone.setMember(member);
                    journeyMilestoneMemberDatas.add(memberMilestone);
                    return memberMilestone;
                });

            if (iterationData == null) {
                eraseMilestoneMemberData(milestoneMemberData);
            } else {
                populateMilestoneMemberData(iterationData, milestoneMemberData);
            }
        }

        jpmdRepository.save(memberData);

        return memberData;
    }

    private static void populateMilestoneMemberData(MemberMilestoneData iterationData,
                                                    JourneyMilestoneMemberData milestoneMemberData) {
        if (iterationData.getWeight() == null) {
            milestoneMemberData.setWeight(null);
        } else {
            milestoneMemberData.setWeight(BigDecimal.valueOf(iterationData.getWeight()));
            milestoneMemberData.setWeightUnit("lbs");
        }

        milestoneMemberData.setPhysicalActivityMin(iterationData.getActivityMinutes());

        AttendanceMode attendanceMode = iterationData.getAttendanceMode();
        milestoneMemberData.setSessionAttendanceMode(attendanceMode.name());
        if (attendanceMode == AttendanceMode.NOT_ATTENDED) {
            milestoneMemberData.setSessionAttendedDate(null);
        } else {
            milestoneMemberData.setSessionAttendedDate(iterationData.getSessionDate());
        }

        milestoneMemberData.setUpdatedAt(LocalDateTime.now());
    }

    private static void eraseMilestoneMemberData(JourneyMilestoneMemberData milestoneMemberData) {
        milestoneMemberData.setWeight(null);
        milestoneMemberData.setWeightUnit(null);
        milestoneMemberData.setPhysicalActivityMin(null);
        milestoneMemberData.setSessionAttendanceMode(null);
        milestoneMemberData.setSessionAttendedDate(null);
    }

    private JourneyProgramMemberData findOrCreate(long journeyProgramTemplateId, ProgramMemberData memberDataDto) {
        Member member = memberRepository.findByUniqueIdAndEmployerId(
            memberDataDto.member().getUniqueId(),
            memberDataDto.member().getEmployerId()
        ).orElseThrow(
            () -> new IllegalArgumentException("Member not found: " + memberDataDto.member().getUniqueId())
        );

        JourneyProgramMemberData memberData = jpmdRepository.findForMember(member.getId(), journeyProgramTemplateId)
            .orElseGet(() -> {
                try {
                    JourneyProgramTemplate programTemplate = jptRepository.findById(journeyProgramTemplateId)
                        .orElseThrow(
                            () -> new IllegalArgumentException("Program template not found: " + journeyProgramTemplateId)
                        );

                    JourneyProgramMemberData programMemberData = new JourneyProgramMemberData();
                    programMemberData.setJourneyProgramTemplate(programTemplate);
                    programMemberData.setMember(member);
                    programMemberData.setCreatedAt(LocalDateTime.now());

                    updateProgramMemberData(memberDataDto, programMemberData);

                    programTemplate.getJourneyProgramMemberDatas().add(programMemberData);
                    return programMemberData;
                } catch (DataIntegrityViolationException | ConstraintViolationException ex) {
                    JourneyProgramMemberData programMemberData = jpmdRepository.findForMember(member.getId(), journeyProgramTemplateId)
                        .orElseThrow(() -> ex);
                    updateProgramMemberData(memberDataDto, programMemberData);
                    return programMemberData;
                }
            });

        jpmdRepository.save(memberData);
        return memberData;
    }

    private static void updateProgramMemberData(ProgramMemberData programMemberData, JourneyProgramMemberData memberData) {
        memberData.setMemberStartDate(programMemberData.memberStartDate());
        memberData.setMemberLastAttendedDate(programMemberData.memberLastAttendedDate());
        memberData.setMemberStartDate(programMemberData.memberStartDate());
        memberData.setUpdatedAt(LocalDateTime.now());
        memberData.setStartingWeight(BigDecimal.valueOf(programMemberData.startingWeight()));
        memberData.setTargetWeight(BigDecimal.valueOf(programMemberData.targetWeight()));
        memberData.setWeightUnit("lbs");
    }
}
