package com.vitality.journey.importer.service.generator;

import com.vitality.journey.importer.exception.InvalidInputException;
import com.vitality.journey.importer.model.AttendanceMode;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.model.csv.DppMemberWeekData;
import com.vitality.journey.importer.model.csv.MemberInfo;
import com.vitality.journey.importer.util.DateUtils;
import net.datafaker.Faker;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * Generates a list of DppMemberRecord objects for testing, which can then be serialized to CSV or other formats.
 * <p>
 * Usage: autowire and call generate DppMemberRecord objects.
 */
@Component
public class DppDataGenerator {

    private static final SecureRandom random = new SecureRandom();
    private static final AtomicInteger ID_COUNTER = new AtomicInteger(10000 + random.nextInt(500000));
    private static final String ID_PREFIX = "MK";
    private static final int MIN_WEIGHT = 151;
    private static final int MAX_WEIGHT = 309;

    private final Faker faker = new Faker();

    public List<DppMemberRecord> generate(int numberOfUsers, int numberOfWeeks) {
        return generate(numberOfUsers, numberOfWeeks, null);
    }

    public List<DppMemberRecord> generate(int numberOfUsers, int numberOfWeeks, String className) {
        return generate(numberOfUsers, numberOfWeeks, className, null);
    }

    public List<DppMemberRecord> generate(int numberOfUsers, int numberOfWeeks, String className, LocalDate classDate) {
        if (numberOfUsers <= 0) {
            throw new InvalidInputException("numberOfUsers must be > 0");
        }

        String finalClassName = StringUtils.hasText(className) ? className : buildClassName();
        LocalDate finalClassDate = DateUtils.shiftToMonday(classDate != null ? classDate : randomPastDateWithinDays(365, LocalDate.now()));

        List<MemberInfo> members = Stream.generate(() -> new MemberInfo(nextMemberUniqueId(), faker.name().firstName(), faker.name().lastName()))
                .limit(numberOfUsers)
                .toList();

        return generate(members, numberOfWeeks, finalClassName, finalClassDate);
    }

    public List<DppMemberRecord> generate(List<MemberInfo> members, int numberOfWeeks, String className, LocalDate classDate) {
        if (members == null || members.isEmpty()) {
            throw new InvalidInputException("members list must be provided and not empty");
        }

        String finalClassName = StringUtils.hasText(className) ? className : buildClassName();
        LocalDate finalClassDate = DateUtils.shiftToMonday(classDate != null ? classDate : randomPastDateWithinDays(365, LocalDate.now()));

        return members.stream()
                .map(member -> buildMemberRecord(member, numberOfWeeks, finalClassName, finalClassDate))
                .toList();
    }

    private DppMemberRecord buildMemberRecord(MemberInfo memberDto, int numberOfWeeks, String className, LocalDate classDate) {
        DppMemberRecord memberRecord = new DppMemberRecord();

        // Dates
        LocalDate today = LocalDate.now();
        LocalDate programStartDate = randomDateOnOrAfter(classDate, today);

        // Weights
        int startingWeight = randomIntInclusive(MIN_WEIGHT, MAX_WEIGHT);
        int targetWeight = computeTargetWeight(startingWeight);

        memberRecord.setUniqueId(memberDto.getMemberId());
        memberRecord.setFirstName(memberDto.getFirstName());
        memberRecord.setLastName(memberDto.getLastName());
        memberRecord.setCdcIdentifier(maybeCdcId());
        memberRecord.setClassName(className);
        memberRecord.setClassDate(classDate);
        memberRecord.setProgramStartDate(programStartDate);
        memberRecord.setStartingWeight(startingWeight);
        memberRecord.setTargetWeight(targetWeight);

        List<DppMemberWeekData> weeklyData = buildWeeklyData(numberOfWeeks, classDate);
        memberRecord.setLastSessionDate(weeklyData.stream()
                .map(DppMemberWeekData::getSessionDate)
                .filter(java.util.Objects::nonNull)
                .max(LocalDate::compareTo)
                .orElse(null));

        memberRecord.setWeeks(weeklyData);

        return memberRecord;
    }

    private List<DppMemberWeekData> buildWeeklyData(int numberOfWeeks, LocalDate classDate) {
        if (numberOfWeeks <= 0) {
            return Collections.emptyList();
        }

        LocalDate today = LocalDate.now();
        return IntStream.rangeClosed(1, numberOfWeeks)
                .mapToObj(w -> {
                    DppMemberWeekData weekData = new DppMemberWeekData();

                    LocalDate sessionDate = classDate.plusWeeks(w - 1L);
                    if (sessionDate.isAfter(today)) {
                        sessionDate = today;
                    }

                    AttendanceMode mode = randomSessionMode();
                    weekData.setAttendanceMode(mode);
                    if (mode != AttendanceMode.NOT_ATTENDED) {
                        weekData.setSessionDate(sessionDate);
                    }

                    if (random.nextDouble() < 0.6) {
                        weekData.setWeight(randomIntInclusive(140, 320));
                    }

                    if (random.nextDouble() < 0.75) {
                        int steps = randomIntInclusive(3, 60);
                        weekData.setActivityMinutes(steps * 5);
                    }
                    return weekData;
                })
                .toList();
    }

    private String nextMemberUniqueId() {
        for (int i = 0; i < random.nextInt(100); i++) {
            ID_COUNTER.incrementAndGet();
        }
        return ID_PREFIX + String.format("%06d", ID_COUNTER.getAndIncrement());
    }

    private String maybeCdcId() {
        if (random.nextDouble() < 0.65) {
            return IntStream.range(0, 10)
                    .mapToObj(i -> String.valueOf(random.nextInt(10)))
                    .collect(Collectors.joining());
        }
        return "";
    }

    private String buildClassName() {
        return "DPP Class - Cohort " + (random.nextInt(90) + 10);
    }

    private LocalDate randomPastDateWithinDays(int daysBack, LocalDate today) {
        int back = randomIntInclusive(14, daysBack);
        return today.minusDays(back);
    }

    private LocalDate randomDateOnOrAfter(LocalDate startInclusive, LocalDate today) {
        if (startInclusive.isAfter(today)) {
            return startInclusive;
        }
        long days = java.time.temporal.ChronoUnit.DAYS.between(startInclusive, today);
        long add = days == 0 ? 0 : random.nextLong(days + 1);
        return startInclusive.plusDays(add);
    }

    private int randomIntInclusive(int min, int max) {
        return min + random.nextInt((max - min) + 1);
    }

    private int computeTargetWeight(int startingWeight) {
        int percent = randomIntInclusive(5, 15);
        double target = startingWeight * (1.0 - (percent / 100.0));
        return Math.min((int) Math.round(target), startingWeight - 1);
    }

    private AttendanceMode randomSessionMode() {
        int r = random.nextInt(100);
        if (r < 50) return AttendanceMode.LIVE;
        if (r < 75) return AttendanceMode.RECORDING;
        return AttendanceMode.NOT_ATTENDED;
    }
}