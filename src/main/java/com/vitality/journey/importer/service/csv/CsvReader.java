package com.vitality.journey.importer.service.csv;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Component
public class CsvReader {

    private static final char DELIM = ',';
    private static final char QUOTE = '"';

    public Stream<ParsedCsvRow> toJsonStream(InputStream inputStream) throws IOException {
        return toJsonStream(inputStream, true);
    }

    public Stream<ParsedCsvRow> toJsonStream(InputStream inputStream, boolean omitEmpty) throws IOException {
        final BufferedReader reader =
                new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

        final String headerLine = reader.readLine();
        if (headerLine == null) {
            return Stream.empty();
        }

        final CSVFormat baseFormat = CSVFormat.DEFAULT.builder()
                .setTrim(true)
                .setDelimiter(DELIM)
                .setQuote(QUOTE)
                .get();

        final List<String> headers = parseColumns(headerLine, baseFormat);
        if (headers.isEmpty()) {
            return Stream.empty();
        }

        return reader.lines()
                .map(rawLine -> {
                    try {
                        CSVRecord rec = parseSingleRecord(rawLine, baseFormat);
                        if (rec == null) {
                            return new ParsedCsvRow(null, rawLine, new IllegalArgumentException("Failed to read CSV record"));
                        }

                        ObjectNode node = toJsonObject(omitEmpty, headers, rec);

                        return new ParsedCsvRow(node, rawLine, null);
                    } catch (Exception e) {
                        return new ParsedCsvRow(null, rawLine, e);
                    }
                })
                .onClose(() -> close(reader, inputStream));
    }

    private static ObjectNode toJsonObject(boolean omitEmpty, List<String> headers, CSVRecord rec) {
        ObjectNode node = JsonNodeFactory.instance.objectNode();
        for (int i = 0; i < headers.size(); i++) {
            String key = headers.get(i);
            String value = (i < rec.size()) ? rec.get(i) : null;

            if (value == null || value.trim().isEmpty()) {
                if (!omitEmpty) {
                    node.putNull(key);
                }
            } else {
                node.put(key, value);
            }
        }
        return node;
    }

    private static CSVRecord parseSingleRecord(String raw, CSVFormat baseFormat) throws IOException {
        try (CSVParser parser = CSVParser.parse(raw, baseFormat)) {
            Iterator<CSVRecord> it = parser.iterator();
            return it.hasNext() ? it.next() : null;
        }
    }

    private static List<String> parseColumns(String headerRaw, CSVFormat baseFormat) throws IOException {
        try (CSVParser parser = CSVParser.parse(headerRaw, baseFormat)) {
            Iterator<CSVRecord> it = parser.iterator();
            if (!it.hasNext()) {
                return List.of();
            }
            CSVRecord headerRec = it.next();
            List<String> headers = new ArrayList<>(headerRec.size());
            for (String h : headerRec) {
                headers.add(h);
            }
            return headers;
        }
    }

    private static void close(Closeable... resources) {
        for (Closeable r : resources) {
            if (r != null) {
                try {
                    r.close();
                } catch (IOException ignore) {
                    // ignore
                }
            }
        }
    }
}
