package com.vitality.journey.importer.service.validation;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class FileValidationService {

    private static final Set<String> ALLOWED_CONTENT_TYPES = Set.of(
            "text/csv", "application/csv", "text/plain"
    );

    public ValidationResult validateFile(MultipartFile file) {
        List<String> errors = new ArrayList<>();

        if (file == null || file.isEmpty()) {
            errors.add("File is required and cannot be empty");
            return new ValidationResult(false, errors);
        }

        validateContentType(file, errors);
        validateFileName(file, errors);

        return new ValidationResult(errors.isEmpty(), errors);
    }

    private void validateContentType(MultipartFile file, List<String> errors) {
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType.toLowerCase())) {
            errors.add("Invalid file type. Only CSV files are allowed");
        }
    }

    private void validateFileName(MultipartFile file, List<String> errors) {
        String filename = file.getOriginalFilename();
        if (filename == null || !filename.toLowerCase().endsWith(".csv")) {
            errors.add("File must have .csv extension");
        }
    }

    public record ValidationResult(boolean valid, List<String> errors) {}
}