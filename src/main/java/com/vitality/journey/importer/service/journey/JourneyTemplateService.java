package com.vitality.journey.importer.service.journey;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyTemplate;
import com.vitality.journey.importer.database.repository.JourneyProgramTemplateRepositoryExtension;
import com.vitality.journey.importer.database.repository.JourneyTemplateRepositoryExtension;
import com.vitality.journey.importer.model.JourneyMilestoneType;
import com.vitality.journey.importer.model.JourneyType;
import com.vitality.journey.importer.util.DateUtils;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class JourneyTemplateService {

    private final JourneyTemplateRepositoryExtension jtRepository;
    private final JourneyProgramTemplateRepositoryExtension jptRepository;

    private static final Map<JourneyType, JourneyMilestoneType> JOURNEY_MILESTONE_TYPE_MAP = Map.of(
            JourneyType.DPP, JourneyMilestoneType.WEEK
    );

    @Transactional(readOnly = true)
    public JourneyTemplate findTemplate(JourneyType journeyType) {
        final String milestoneType = JOURNEY_MILESTONE_TYPE_MAP.get(journeyType).name();
        return jtRepository.findByType(journeyType.name(), milestoneType)
                .orElseThrow(() ->
                        new IllegalStateException(
                                "Failed to find JourneyTemplate: journeyType="
                                        + journeyType.name() + ", milestoneType=" + milestoneType)
                );
    }

    @Transactional
    public JourneyProgramTemplate findOrCreate(JourneyTemplate journeyTemplate,
                                               String programName,
                                               LocalDate startDate,
                                               LocalDate endDate,
                                               int iterations) {
        LocalDateTime programStartDate = DateUtils.startOfDay(startDate);
        LocalDateTime programEndDate = DateUtils.endOfDay(endDate);

        JourneyProgramTemplate journeyProgramTemplate = jptRepository.findByNameAndDates(
                journeyTemplate.getId(), programName, programStartDate, programEndDate
        ).orElseGet(() -> {
            try {
                JourneyProgramTemplate template = new JourneyProgramTemplate();
                template.setJourneyTemplate(journeyTemplate);
                template.setName(programName);
                template.setStartDate(programStartDate);
                template.setEndDate(programEndDate);
                template.setCreatedAt(LocalDateTime.now());
                template.setUpdatedAt(LocalDateTime.now());
                return jptRepository.saveAndFlush(template);
            } catch (DataIntegrityViolationException | ConstraintViolationException ex) {
                if (log.isDebugEnabled()) {
                    log.debug("Race condition on findOrCreate JourneyProgramTemplate, re-fetching {} {} {}",
                            programName, programStartDate, programEndDate, ex);
                } else {
                    log.warn("Race condition on findOrCreate JourneyProgramTemplate, re-fetching {} {} {}",
                            programName, programStartDate, programEndDate);
                }

                return jptRepository.findByNameAndDates(
                        journeyTemplate.getId(), programName, programStartDate, programEndDate
                ).orElseThrow(() -> new IllegalStateException(
                        String.format("Failed to find JourneyProgramTemplate: %s %s %s",
                                programName, programStartDate, programEndDate), ex)
                );
            }
        });

        Set<JourneyMilestoneTemplate> journeyMilestoneTemplates = journeyProgramTemplate.getJourneyMilestoneTemplates();
        if (CollectionUtils.isEmpty(journeyMilestoneTemplates) && iterations > 0) {
            for (int i = 1; i <= iterations; i++) {
                addMilestoneTemplate(journeyProgramTemplate, i);
            }
            jptRepository.save(journeyProgramTemplate);
        }

        return journeyProgramTemplate;
    }

    private void addMilestoneTemplate(JourneyProgramTemplate programTemplate, int iteration) {
        LocalDate[] iterationDates = DateUtils.weekIterationWindow(
                programTemplate.getStartDate().toLocalDate(), iteration
        );

        JourneyMilestoneTemplate milestoneTemplate = new JourneyMilestoneTemplate();
        milestoneTemplate.setJourneyProgramTemplate(programTemplate);
        milestoneTemplate.setIteration((short) iteration);
        milestoneTemplate.setStartDate(DateUtils.startOfDay(iterationDates[0]));
        milestoneTemplate.setEndDate(DateUtils.endOfDay(iterationDates[1]));
        milestoneTemplate.setJourneyProgramTemplate(programTemplate);
        milestoneTemplate.setCreatedAt(LocalDateTime.now());
        milestoneTemplate.setUpdatedAt(LocalDateTime.now());

        programTemplate.getJourneyMilestoneTemplates().add(milestoneTemplate);
    }
}
