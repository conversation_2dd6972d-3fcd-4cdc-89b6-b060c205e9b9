package com.vitality.journey.importer.service.csv;

import com.vitality.journey.importer.model.AttendanceMode;
import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.model.csv.DppMemberWeekData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CsvWriter {

    private static final DateTimeFormatter DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");

    public String toCsv(List<DppMemberRecord> records) {
        if (records == null || records.isEmpty()) {
            return "";
        }

        int maxWeeks = records.stream()
                .mapToInt(r -> r.getWeeks().size())
                .max()
                .orElse(0);

        String header = DppCsvHeader.buildHeader(maxWeeks);

        String rows = records.stream()
                .map(memberRecord -> toCsvLine(memberRecord, maxWeeks))
                .collect(Collectors.joining(System.lineSeparator()));

        return header + System.lineSeparator() + rows;
    }

    public String toCsvLine(DppMemberRecord memberRecord) {
        return toCsvLine(memberRecord, memberRecord.getWeeks().size());
    }

    public String toCsvLine(DppMemberRecord memberRecord, int totalWeeks) {
        return toCsvLine(toFields(memberRecord, totalWeeks));
    }

    private List<String> toFields(DppMemberRecord memberRecord, int totalWeeks) {
        List<String> fields = new ArrayList<>();
        fields.add(memberRecord.getUniqueId());
        fields.add(memberRecord.getCdcIdentifier());
        fields.add(memberRecord.getFirstName());
        fields.add(memberRecord.getLastName());
        fields.add(memberRecord.getClassName());
        fields.add(Optional.ofNullable(memberRecord.getClassDate()).map(DATE_FMT::format).orElse(""));
        fields.add(Optional.ofNullable(memberRecord.getProgramStartDate()).map(DATE_FMT::format).orElse(""));
        fields.add(Optional.ofNullable(memberRecord.getLastSessionDate()).map(DATE_FMT::format).orElse(""));
        fields.add(String.valueOf(memberRecord.getStartingWeight()));
        fields.add(String.valueOf(memberRecord.getTargetWeight()));

        for (int i = 0; i < totalWeeks; i++) {
            if (i < memberRecord.getWeeks().size()) {
                DppMemberWeekData weekData = memberRecord.getWeeks().get(i);
                if (weekData.getAttendanceMode() == AttendanceMode.NOT_ATTENDED) {
                    fields.add(weekData.getAttendanceMode().getId());
                } else {
                    String sessionValue = weekData.getAttendanceMode().getId() + " - " + DATE_FMT.format(weekData.getSessionDate());
                    fields.add(sessionValue);
                }

                fields.add(Optional.ofNullable(weekData.getWeight()).map(String::valueOf).orElse(""));
                fields.add(Optional.ofNullable(weekData.getActivityMinutes()).map(String::valueOf).orElse(""));
            } else {
                fields.add("");
                fields.add("");
                fields.add("");
            }
        }
        return fields;
    }

    private String toCsvLine(List<String> fields) {
        return fields.stream().map(this::escapeCsv).collect(Collectors.joining(","));
    }

    private String escapeCsv(String value) {
        if (value == null) return "";
        boolean needQuotes = value.contains(",") || value.contains("\n") || value.contains("\r") || value.contains("\"");
        String v = value.replace("\"", "\"\"");
        return needQuotes ? "\"" + v + "\"" : v;
    }
}