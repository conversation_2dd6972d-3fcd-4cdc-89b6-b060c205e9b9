package com.vitality.journey.importer.service.member;

import com.vitality.journey.importer.model.MemberRecord;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import za.co.discovery.health.entity.api.EntityApi;
import za.co.discovery.health.entity.domain.VMemberEmployerView;

import java.util.Comparator;
import java.util.List;
import java.util.OptionalLong;
import java.util.function.Predicate;

@Component
@RequiredArgsConstructor
public class RemoteEntityNoResolver implements EntityNoResolver {
    private static final Comparator<VMemberEmployerView> PARTIC_STAT_COMPARATOR = Comparator.comparingInt(e -> {
        String participationStatus = e.getParticStat();
        if (StringUtils.isBlank(participationStatus)) return Integer.MAX_VALUE; // nulls go last
        return switch (participationStatus) {
            case "P", "PP" -> 1;
            case "S", "SP" -> 2;
            case "C", "CH" -> 3;
            default -> 4;
        };
    });

    private final EntityApi entityApi;

    @Override
    public OptionalLong resolveEntityNo(MemberRecord member) {
        String employeeNo = member.getUniqueId();
        List<VMemberEmployerView> entities = entityApi.getMemberEmployerViewByEmployeeNoAndEmpEntNo(employeeNo, member.getEmployerId());

        if (entities == null || entities.isEmpty()) {
            return OptionalLong.empty();
        }

        VMemberEmployerView entity = findByUniqueId(entities, member.getUniqueId());

        if (entity != null && entity.getMemberEnityNo() != null) {
            return OptionalLong.of(entity.getMemberEnityNo());
        }

        entity = findByName(entities, member);
        if (entity != null && entity.getMemberEnityNo() != null) {
            return OptionalLong.of(entity.getMemberEnityNo());
        }

        return OptionalLong.empty();
    }

    private VMemberEmployerView findByUniqueId(List<VMemberEmployerView> entities, String uniqueId) {
        return entities.stream()
            .sorted(PARTIC_STAT_COMPARATOR)
            .filter(e -> uniqueId.equals(e.getDepExtCardNo()))
            .findFirst().orElse(null);
    }

    private VMemberEmployerView findByName(List<VMemberEmployerView> entities, MemberRecord member) {
        Predicate<VMemberEmployerView> compareNamePredicate = memberEmployerView ->
            StringUtils.equalsIgnoreCase(
                StringUtils.deleteWhitespace(memberEmployerView.getFirstName()),
                StringUtils.deleteWhitespace(member.getFirstName())
            ) && StringUtils.equalsIgnoreCase(
                StringUtils.deleteWhitespace(memberEmployerView.getLastName()),
                StringUtils.deleteWhitespace(member.getLastName())
            );

        return entities.stream()
            .sorted(PARTIC_STAT_COMPARATOR)
            .filter(compareNamePredicate)
            .findFirst().orElse(null);
    }
}
