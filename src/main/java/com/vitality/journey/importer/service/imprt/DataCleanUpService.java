package com.vitality.journey.importer.service.imprt;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramTemplate;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import com.vitality.journey.importer.database.databaseMapping.StagingToEntityCrossmap;
import com.vitality.journey.importer.util.EntityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class DataCleanUpService {

    private final JdbcTemplate jdbcTemplate;

    private static final List<Class<?>> ENTITIES_TO_CLEAN_UP = List.of(
        StagingToEntityCrossmap.class,
        StagingRecordError.class,
        StagingRecord.class,
        JourneyMilestoneMemberData.class,
        JourneyProgramMemberData.class,
        JourneyMilestoneTemplate.class,
        JourneyProgramTemplate.class,
        Member.class
    );

    private final List<String> tablesToCleanUp;

    public DataCleanUpService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.tablesToCleanUp = ENTITIES_TO_CLEAN_UP.stream()
            .map(EntityUtils::getTableName).toList();
    }

    @Transactional
    public void deleteAllData() {
        log.info("Deleting all data from tables: {}", tablesToCleanUp);
        for (String table : tablesToCleanUp) {
            truncateOrDelete(table);
        }
    }

    private void truncateOrDelete(String table) {
        try {
            jdbcTemplate.execute("TRUNCATE TABLE " + table);
        } catch (DataAccessException e) {
            // Fallback if TRUNCATE not allowed due to FKs/privileges/dialect
            jdbcTemplate.update("DELETE FROM " + table);
        }
    }
}
