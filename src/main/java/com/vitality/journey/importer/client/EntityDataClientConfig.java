package com.vitality.journey.importer.client;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClient;
import za.co.discovery.health.entity.ApiClient;
import za.co.discovery.health.entity.api.EntityApi;

@Configuration
@RequiredArgsConstructor
public class EntityDataClientConfig {

    private final EntityDataClientProperties properties;
    private final HttpClientFactory httpClientFactory;

    @PostConstruct
    void validate() {
        if (properties.getUrl() == null || properties.getUrl().isBlank()) {
            throw new IllegalStateException("entity-service.url must be configured");
        }
    }

    @Bean
    public EntityApi entityController<PERSON>pi(RestClient.Builder builder) {
        CloseableHttpClient httpClient = httpClientFactory.createHttpClient(properties);
        RestClient restClient = builder.requestFactory(new HttpComponentsClientHttpRequestFactory(httpClient)).build();
        ApiClient apiClient = new ApiClient(restClient);
        apiClient.setBasePath(properties.getUrl());
        return new EntityApi(apiClient);
    }
}
