package com.vitality.journey.importer.client;

import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
public class HttpClientFactory {

    private static final Duration EVICT_CONNECTION_TIMEOUT = Duration.ofSeconds(30);

    public CloseableHttpClient createHttpClient(ClientProperties properties) {
        ConnectionConfig connectionConfig = ConnectionConfig.custom()
            .setConnectTimeout(Timeout.of(properties.getConnectTimeout()))
            .setSocketTimeout(Timeout.of(properties.getReadTimeout()))
            .build();

        var connManager = PoolingHttpClientConnectionManagerBuilder.create()
            .setDefaultConnectionConfig(connectionConfig)
            .build();
        connManager.setMaxTotal(properties.getMaxTotal());
        connManager.setDefaultMaxPerRoute(properties.getMaxPerRoute());

        var retryStrategy = new DefaultHttpRequestRetryStrategy(properties.getRetryAttempts(), TimeValue.ofMicroseconds(500));

        return HttpClients.custom()
            .setConnectionManager(connManager)
            .setRetryStrategy(retryStrategy)
            .evictExpiredConnections()
            .evictIdleConnections(TimeValue.of(EVICT_CONNECTION_TIMEOUT))
            .build();
    }
}
