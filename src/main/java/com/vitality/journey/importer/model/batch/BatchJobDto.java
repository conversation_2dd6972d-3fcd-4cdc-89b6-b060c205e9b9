package com.vitality.journey.importer.model.batch;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Schema(description = "Batch job execution details with steps and parameters")
public class BatchJobDto {
    @Schema(description = "Unique job execution identifier", example = "123")
    private Long jobExecutionId;
    
    @Schema(description = "Name of the batch job", example = "importDppCsvJob")
    private String jobName;
    
    @Schema(description = "Current job status", example = "COMPLETED", allowableValues = {"STARTING", "STARTED", "STOPPING", "STOPPED", "FAILED", "COMPLETED", "ABANDONED"})
    private String status;
    
    @Schema(description = "Job start timestamp")
    private LocalDateTime startTime;
    
    @Schema(description = "Job end timestamp")
    private LocalDateTime endTime;
    
    @Schema(description = "Job exit code", example = "COMPLETED")
    private String exitCode;
    
    @Schema(description = "Job execution parameters")
    private Map<String, String> parameters;
    
    @Schema(description = "List of job steps with execution details")
    private List<BatchStepDto> steps;
}