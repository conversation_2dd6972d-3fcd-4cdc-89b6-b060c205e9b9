package com.vitality.journey.importer.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDate;
import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record MemberMilestoneDto(int iteration, LocalDateTime startDate, LocalDateTime endDate,
                                 Double weight, Integer activityMinutes, String attendanceMode,
                                 LocalDate sessionDate) {
}
