package com.vitality.journey.importer.model.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Batch step execution details with processing statistics")
public class BatchStepDto {
    @Schema(description = "Unique step execution identifier", example = "456")
    private Long stepExecutionId;

    @Schema(description = "Name of the batch step", example = "stagingStep")
    private String stepName;

    @Schema(description = "Current step status", example = "COMPLETED")
    private String status;

    @Schema(description = "Step start timestamp")
    private LocalDateTime startTime;

    @Schema(description = "Step end timestamp")
    private LocalDateTime endTime;

    @Schema(description = "Step exit code", example = "COMPLETED")
    private String exitCode;

    @Schema(description = "Step exit message")
    private String exitMessage;

    @Schema(description = "Number of items read", example = "100")
    private Long readCount;

    @Schema(description = "Number of items written", example = "95")
    private Long writeCount;

    @Schema(description = "Number of items skipped due to errors", example = "5")
    private Long skipCount;
}
