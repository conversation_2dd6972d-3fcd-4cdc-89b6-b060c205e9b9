package com.vitality.journey.importer.model.validation;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "Validation error response containing details about validation failures")
public record ValidationErrorResponse(
        @Schema(description = "Error timestamp")
        LocalDateTime timestamp,
        
        @Schema(description = "HTTP status code", example = "400")
        int status,
        
        @Schema(description = "Error type", example = "Validation Failed")
        String error,
        
        @Schema(description = "List of validation error messages")
        List<String> messages,
        
        @Schema(description = "Request path", example = "/api/import/upload/dpp-csv")
        String path
) {
}