package com.vitality.journey.importer.model.imprt;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.batch.core.JobExecution;

@Schema(description = "Result of import operation with job execution details")
public record ExecutionResult(
    @Schema(description = "Spring Batch job execution ID", example = "123")
    long executionId,

    @Schema(description = "Import operation status", example = "STARTED")
    String status
) {
    public ExecutionResult(JobExecution jobExecution) {
        this(jobExecution.getJobId(), jobExecution.getStatus().name());
    }
}
