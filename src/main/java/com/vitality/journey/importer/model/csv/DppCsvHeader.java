package com.vitality.journey.importer.model.csv;

import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class DppCsvHeader {
    public static final String UNIQUE_ID = "Unique ID";
    public static final String CDC_IDENTIFIER = "CDC Identifier";
    public static final String FIRST_NAME = "First Name";
    public static final String LAST_NAME = "Last Name";
    public static final String CLASS = "Class";
    public static final String CLASS_DATE = "Class Date";
    public static final String PROGRAM_START_DATE = "Program Start Date";
    public static final String LAST_SESSION_DATE = "Date Attended Last Session";
    public static final String STARTING_WEIGHT = "Starting weight";
    public static final String TARGET_WEIGHT = "Target weight set";
    public static final String WEEK_SESSION = "Week %d Session";
    public static final String WEEK_WEIGHT = "Week %d Weight";
    public static final String WEEK_ACTIVITY = "Week %d Activity";

    public static String buildHeader(int weeks) {
        List<String> columns = new ArrayList<>(List.of(
                DppCsvHeader.UNIQUE_ID,
                DppCsvHeader.CDC_IDENTIFIER,
                DppCsvHeader.FIRST_NAME,
                DppCsvHeader.LAST_NAME,
                DppCsvHeader.CLASS,
                DppCsvHeader.CLASS_DATE,
                DppCsvHeader.PROGRAM_START_DATE,
                DppCsvHeader.LAST_SESSION_DATE,
                DppCsvHeader.STARTING_WEIGHT,
                DppCsvHeader.TARGET_WEIGHT
        ));

        for (int i = 1; i <= weeks; i++) {
            columns.add(String.format(DppCsvHeader.WEEK_SESSION, i));
            columns.add(String.format(DppCsvHeader.WEEK_WEIGHT, i));
            columns.add(String.format(DppCsvHeader.WEEK_ACTIVITY, i));
        }

        return String.join(",", columns);
    }
}
