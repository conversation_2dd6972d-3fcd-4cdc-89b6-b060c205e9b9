package com.vitality.journey.importer.model.csv;

import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a single DPP CSV row as a strongly-typed object for flexible testing and conversions.
 */
@Data
public class DppMemberRecord {
    private String uniqueId;
    private String cdcIdentifier; // optional, may be empty
    private String firstName;
    private String lastName;
    private String className;
    private LocalDate classDate;
    private LocalDate programStartDate;
    private LocalDate lastSessionDate; // may be null
    private int startingWeight;
    private int targetWeight;
    private List<DppMemberWeekData> weeks = new ArrayList<>();
}
