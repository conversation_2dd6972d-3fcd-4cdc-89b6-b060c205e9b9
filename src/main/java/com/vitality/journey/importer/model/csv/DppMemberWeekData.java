package com.vitality.journey.importer.model.csv;

import com.vitality.journey.importer.model.AttendanceMode;
import lombok.Data;

import java.time.LocalDate;

/**
 * Week-level data for a DPP CSV record.
 */
@Data
public class DppMemberWeekData {
    private AttendanceMode attendanceMode; // NOT_ATTENDED implies null sessionDate
    private LocalDate sessionDate; // nullable
    private Integer weight; // nullable
    private Integer activityMinutes; // nullable
}
