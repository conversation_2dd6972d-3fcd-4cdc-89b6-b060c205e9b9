package com.vitality.journey.importer.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDate;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record ProgramMemberData(MemberRecord member, String programName, Float startingWeight, Float targetWeight,
                                LocalDate programStartDate, LocalDate memberStartDate,
                                LocalDate memberLastAttendedDate, List<MemberMilestoneData> iterations) {
    @JsonIgnore
    public int getNumberOfIterations() {
        return iterations()
                .stream().mapToInt(MemberMilestoneData::getIteration)
                .max().orElse(0);
    }
}
