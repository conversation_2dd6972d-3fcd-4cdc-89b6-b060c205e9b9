package com.vitality.journey.importer.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum EntryType {
    WEIGHT("Weight"), ACTIVITY("Activity"), SESSION("Session");

    private final String name;

    public static EntryType fromName(String name) {
        for (EntryType type : values()) {
            if (type.name.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
}
