package com.vitality.journey.importer.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AttendanceMode {
    LIVE("L"), RECORDING("A"), NOT_ATTENDED("X");

    private final String id;

    public static AttendanceMode fromId(String id) {
        for (AttendanceMode mode : values()) {
            if (mode.id.equals(id)) {
                return mode;
            }
        }
        return null;
    }
}

