package com.vitality.journey.importer.model.dto;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.vitality.journey.importer.model.MemberRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonPropertyOrder({"id", "entityNo", "uniqueId", "employerId", "cdcId", "firstName", "lastName", "createdAt", "updatedAt"})
public class MemberDto extends MemberRecord {
    private Long id;
    private Long entityNo;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
