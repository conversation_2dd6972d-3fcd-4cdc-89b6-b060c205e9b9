package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.batch.BatchJobDto;
import com.vitality.journey.importer.service.batch.BatchJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/batch/jobs")
@RequiredArgsConstructor
@Tag(name = "Batch Jobs", description = "Spring Batch job monitoring and status operations")
public class BatchJobController {

    private final BatchJobService batchJobService;

    @Operation(summary = "Get all batch jobs", description = "Retrieves all Spring Batch job executions")
    @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully")
    @GetMapping
    public ResponseEntity<List<BatchJobDto>> getAllJobs() {
        return ResponseEntity.ok(batchJobService.getAllJobs());
    }

    @Operation(summary = "Get batch job by ID", description = "Retrieves a specific batch job with its steps")
    @ApiResponse(responseCode = "200", description = "Job retrieved successfully")
    @ApiResponse(responseCode = "404", description = "Job not found")
    @GetMapping("/{jobExecutionId}")
    public ResponseEntity<BatchJobDto> getJobById(
            @Parameter(description = "Job execution ID", example = "123", required = true)
            @PathVariable Long jobExecutionId) {
        return batchJobService.getJobById(jobExecutionId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
}