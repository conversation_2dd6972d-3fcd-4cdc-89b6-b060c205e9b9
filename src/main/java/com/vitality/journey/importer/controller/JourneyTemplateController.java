package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.JourneyType;
import com.vitality.journey.importer.model.dto.MemberJourneyTemplateDto;
import com.vitality.journey.importer.model.dto.MemberJourneyTemplateResponse;
import com.vitality.journey.importer.service.journey.JourneyMemberService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/api/journey-templates")
@RequiredArgsConstructor
public class JourneyTemplateController {

    private final JourneyMemberService journeyMemberService;

    @Operation(
        summary = "Get journey template for member",
        description = "Returns the journey template for the specified member and employer. This is the data that will be used to enroll the member in the journey."
    )
    @GetMapping("/")
    public ResponseEntity<MemberJourneyTemplateResponse> getJourneyTemplate(
        @RequestParam(defaultValue = "DPP") JourneyType journeyType,
        @RequestParam String memberUniqueId,
        @RequestParam long employerId) {
        return toResponse(journeyMemberService.getJourneyTemplateDto(memberUniqueId, employerId, journeyType.name()));
    }

    @Operation(
        summary = "Get journey template for member by entity no",
        description = "Returns the journey template for the specified member and employer. This is the data that will be used to enroll the member in the journey."
    )
    @GetMapping("/{entityNo}")
    public ResponseEntity<MemberJourneyTemplateResponse> getJourneyTemplate(
        @RequestParam(defaultValue = "DPP") JourneyType journeyType,
        @PathVariable long entityNo) {
        return toResponse(journeyMemberService.getJourneyTemplateDto(entityNo, journeyType.name()));
    }

    private static ResponseEntity<MemberJourneyTemplateResponse> toResponse(Optional<MemberJourneyTemplateDto> journeyTemplate) {
        return journeyTemplate
            .map(MemberJourneyTemplateResponse::new)
            .map(ResponseEntity::ok)
            .orElseGet(() -> ResponseEntity.notFound().build());
    }
}
