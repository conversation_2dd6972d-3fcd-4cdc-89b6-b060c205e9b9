package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.imprt.ExecutionResult;
import com.vitality.journey.importer.model.validation.ValidationErrorResponse;
import com.vitality.journey.importer.service.batch.BatchJobService;
import com.vitality.journey.importer.service.imprt.DataCleanUpService;
import com.vitality.journey.importer.service.imprt.ImportOrchestrator;
import com.vitality.journey.importer.service.validation.FileValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;

@Slf4j
@RestController
@RequestMapping("/api/import")
@RequiredArgsConstructor
@Tag(name = "Import", description = "Journey data import operations")
public class ImportController {

    private final ImportOrchestrator importOrchestrator;
    private final DataCleanUpService dataCleanUpService;
    private final FileValidationService fileValidationService;
    private final BatchJobService batchJobService;

    @Operation(
        summary = "Import DPP CSV file",
        description = "Uploads and imports a Diabetes Prevention Program CSV file for the specified employer. The file must be in CSV format. Returns the import job details if successful. As result it will create a personal journey template data for each member from the file."
    )
    @PostMapping(value = "/upload-dpp-csv-full", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> importDppCsv(
        @Parameter(description = "Employer identifier", required = true)
        @RequestParam("employerId") long employerId,
        @Parameter(description = "CSV file containing DPP journey data", required = true)
        @RequestParam("file") MultipartFile file) {

        var validationResult = fileValidationService.validateFile(file);
        if (!validationResult.valid()) {
            var errorResponse = new ValidationErrorResponse(
                LocalDateTime.now(),
                400,
                "Validation Failed",
                validationResult.errors(),
                "/api/import/upload/dpp-csv"
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try (var inputStream = file.getInputStream()) {
            ExecutionResult executionResult = importOrchestrator.importDppCsv(
                file.getOriginalFilename(), inputStream, employerId
            );

            return batchJobService.getJobById(executionResult.executionId())
                .map(ResponseEntity::ok)
                .orElseThrow();
        } catch (IOException e) {
            log.error("Failed to handle uploaded file", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(
        summary = "Delete all staging and normalized data",
        description = "WARNING: This operation will irreversibly delete all staging and normalized data. Use with caution."
    )
    @DeleteMapping("/delete-all-data")
    public ResponseEntity<Void> deleteAllData() {
        dataCleanUpService.deleteAllData();
        return ResponseEntity.ok().build();
    }
}
