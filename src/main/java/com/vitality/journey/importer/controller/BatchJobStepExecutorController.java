package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.batch.BatchJobDto;
import com.vitality.journey.importer.model.imprt.ExecutionResult;
import com.vitality.journey.importer.service.batch.BatchJobService;
import com.vitality.journey.importer.service.imprt.ImportOrchestrator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/batch/jobs/step/")
@RequiredArgsConstructor
@Tag(name = "Batch Job Step Executor", description = "Controller to execute individual steps of batch jobs")
public class BatchJobStepExecutorController {

    private final ImportOrchestrator importOrchestrator;
    private final BatchJobService batchJobService;

    @Operation(
        summary = "Normalize imported DPP data",
        description = "Normalizes all imported DPP data. If entityNo is specified, only that member will be normalized."
    )
    @GetMapping("/normalize")
    public ResponseEntity<BatchJobDto> normalizeStagingRecords(
        @RequestParam(value = "importId", required = false) Long importId,
        @Parameter(description = "Employer identifier", required = true)
        @RequestParam("employerId") long employerId,
        @RequestParam(value = "stagingRecordId", required = false) Long stagingRecordId
    ) {
        ExecutionResult executionResult = importOrchestrator.normalize(importId, employerId, stagingRecordId);

        return batchJobService.getJobById(executionResult.executionId())
            .map(ResponseEntity::ok)
            .orElseThrow();
    }

    @Operation(
        summary = "Enrich imported DPP data",
        description = "Enriches all imported DPP data with entity information. If entityNo is specified, only that member will be enriched."
    )
    @GetMapping("/enrich")
    public ResponseEntity<BatchJobDto> enrichImportedData(
        @RequestParam(value = "importId", required = false) Long importId,
        @RequestParam(value = "stagingRecordId", required = false) Long stagingRecordId
    ) {
        ExecutionResult executionResult = importOrchestrator.enrich(importId, stagingRecordId);

        return batchJobService.getJobById(executionResult.executionId())
            .map(ResponseEntity::ok)
            .orElseThrow();
    }

    @Operation(
        summary = "Enroll imported members",
        description = "Enrolls all imported members in the journey. If entityNo is specified, only that member will be enrolled."
    )
    @GetMapping("/enroll")
    public ResponseEntity<BatchJobDto> enrollImportedMembers(
        @RequestParam(value = "importId", required = false) Long importId,
        @RequestParam(value = "entityNo", required = false) Long entityNo
    ) {
        ExecutionResult executionResult = importOrchestrator.enroll(importId, entityNo);

        return batchJobService.getJobById(executionResult.executionId())
            .map(ResponseEntity::ok)
            .orElseThrow();
    }
}
