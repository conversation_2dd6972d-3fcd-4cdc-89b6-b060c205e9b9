package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.dto.MemberDto;
import com.vitality.journey.importer.service.member.MemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@RestController
@RequestMapping("/api/members")
@RequiredArgsConstructor
@Tag(name = "Members", description = "Members API")
public class MembersController {
    private final MemberService memberService;

    @Operation(
        summary = "Find members",
        description = "Returns all members. If employerId is specified, only members for that employer will be returned. If entityNo is specified, only that member will be returned."
    )
    @GetMapping("/")
    public ResponseEntity<PagedModel<MemberDto>> getAll(@RequestParam(value = "employerId", required = false)
                                                        Long employerId,
                                                        @RequestParam(value = "entityNo", required = false)
                                                        Long entityNo,
                                                        Pageable pageable) {
        if (entityNo != null) {
            return memberService.getByEntityNo(entityNo)
                .map(member -> new PageImpl<>(Collections.singletonList(member)))
                .map(PagedModel::new)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
        } else {
            return ResponseEntity.ok(new PagedModel<>(memberService.getAll(employerId, pageable)));
        }
    }
}
