package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.dto.StagingRecordDto;
import com.vitality.journey.importer.model.dto.StagingRecordErrorDto;
import com.vitality.journey.importer.service.imprt.staging.StagingDataQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/api/staging", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Staging Data", description = "Operations for querying staging data")
public class StagingDataController {

    private final StagingDataQueryService stagingDataQueryService;

    @Operation(
        summary = "Get staging records for import job",
        description = "Returns the staging records for the specified import job."
    )
    @GetMapping("/records")
    public PagedModel<StagingRecordDto> getStagingRecords(@RequestParam(value = "importJobId", required = false)
                                                          Long importJobId,
                                                          Pageable pageable) {
        return stagingDataQueryService.getStagingRecords(importJobId, pageable);
    }

    @GetMapping("/records/{id}")
    public ResponseEntity<StagingRecordDto> getStagingRecord(@PathVariable("id") Long id) {
        return stagingDataQueryService.getStagingRecord(id)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    @Operation(
        summary = "Get staging record errors for import job",
        description = "Returns the staging record errors for the specified import job."
    )
    @GetMapping("/errors")
    public PagedModel<StagingRecordErrorDto> getStagingRecordErrors(@RequestParam(value = "importJobId", required = false)
                                                                    Long importJobId, Pageable pageable) {
        return stagingDataQueryService.getStagingRecordErrors(importJobId, pageable);
    }
}
