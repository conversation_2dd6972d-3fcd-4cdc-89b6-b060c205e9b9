package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.model.csv.MemberInfo;
import com.vitality.journey.importer.service.csv.CsvWriter;
import com.vitality.journey.importer.service.generator.DppDataGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.context.annotation.Profile;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Profile("data-generator")
@RestController
@RequestMapping(path = "/api/import/")
@RequiredArgsConstructor
@Tag(name = "Data Generator", description = "Controller to generate test data for import")
public class ImportDataGeneratorController {

    private final DppDataGenerator dppDataGenerator;
    private final CsvWriter csvWriter;

    private static final DateTimeFormatter FILE_TS = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss");

    @Operation(
            summary = "Generate mock DPP CSV file",
            description = "Generates a mock DPP CSV file with the specified number of users and weeks. The file can be downloaded and used for testing purposes."
    )
    @GetMapping(value = "/generate/dpp-csv", produces = "text/csv")
    public ResponseEntity<byte[]> generateDppCsv(
            @RequestParam(name = "users", defaultValue = "10") int numberOfUsers,
            @RequestParam(name = "weeks", defaultValue = "26") int numberOfWeeks,
            @RequestParam(name = "className", required = false) String className,
            @RequestParam(name = "classDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate classDate) {

        if (numberOfUsers <= 0 || numberOfWeeks <= 0) {
            return ResponseEntity.badRequest().build();
        }

        List<DppMemberRecord> records = dppDataGenerator.generate(numberOfUsers, numberOfWeeks, className, classDate);
        String csv = csvWriter.toCsv(records);
        byte[] bytes = csv.getBytes(StandardCharsets.UTF_8);

        String filename = String.format("mock-dpp-data-%s.csv", LocalDateTime.now().format(FILE_TS));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(bytes.length)
                .body(bytes);
    }

    @Operation(
            summary = "Generate mock DPP CSV file for users in CSV file",
            description = "Generates a mock DPP CSV file for the users specified in the uploaded CSV file. Intended to generated test data for real Vitality members. The file can be downloaded and used for testing purposes."
    )
    @PostMapping(value = "/generate/dpp-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = "text/csv")
    public ResponseEntity<byte[]> generateDppCsvForUsers(
            @RequestParam("file") MultipartFile file,
            @RequestParam("employerNo") String employerNo,
            @RequestParam(name = "weeks", defaultValue = "26") int numberOfWeeks,
            @RequestParam(name = "className", required = false) String className,
            @RequestParam(name = "classDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate classDate) throws IOException {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("Please upload a CSV file.".getBytes(StandardCharsets.UTF_8));
        }
        if (!StringUtils.hasText(employerNo)) {
            return ResponseEntity.badRequest().body("employerNo cannot be empty.".getBytes(StandardCharsets.UTF_8));
        }
        if (numberOfWeeks <= 0) {
            return ResponseEntity.badRequest().body("numberOfWeeks must be > 0".getBytes(StandardCharsets.UTF_8));
        }

        List<MemberInfo> members = new ArrayList<>();
        try (Reader reader = new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.builder().setHeader().setSkipHeaderRecord(true).build())) {

            for (CSVRecord csvRecord : csvParser) {
                members.add(new MemberInfo(
                        csvRecord.get("MEMBER_ID"),
                        csvRecord.get("FIRST_NAME"),
                        csvRecord.get("LAST_NAME")
                ));
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(("Error parsing CSV: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }

        List<DppMemberRecord> records = dppDataGenerator.generate(members, numberOfWeeks, className, classDate);
        String csv = csvWriter.toCsv(records);
        byte[] bytes = csv.getBytes(StandardCharsets.UTF_8);

        String filename = String.format("mock-dpp-data-%s-%s.csv", employerNo, LocalDateTime.now().format(FILE_TS));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(bytes.length)
                .body(bytes);
    }
}
