package com.vitality.journey.importer.batch;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@RequiredArgsConstructor
public class BatchThreadPoolConfig {

    private final BatchJobProperties batchJobProperties;

    @Bean
    public TaskExecutor batchTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(batchJobProperties.getThreadPoolSize());
        executor.setMaxPoolSize(batchJobProperties.getThreadPoolSize());
        executor.setThreadNamePrefix("batch-");
        executor.initialize();
        return executor;
    }
}
