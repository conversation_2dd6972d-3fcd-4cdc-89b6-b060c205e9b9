package com.vitality.journey.importer.batch;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "batch")
public class BatchJobProperties {

    private int threadPoolSize = 1;
    private DppConfig dpp = new DppConfig();

    @Data
    public static class DppConfig {
        private StepConfig staging = new StepConfig(100, false);
        private StepConfig normalize = new StepConfig(100, false);
        private StepConfig enrich = new StepConfig(50, false);
        private StepConfig enroll = new StepConfig(10, false);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StepConfig {
        private int chunkSize;
        private boolean concurrent;
    }
}
