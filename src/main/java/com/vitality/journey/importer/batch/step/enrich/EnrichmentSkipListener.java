package com.vitality.journey.importer.batch.step.enrich;

import com.vitality.journey.importer.batch.step.StagingMemberSkipListener;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.database.repository.StagingToEntityCrossmapRepositoryExtension;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import org.springframework.stereotype.Component;

@Component
public class EnrichmentSkipListener extends StagingMemberSkipListener {
    public EnrichmentSkipListener(StagingToEntityCrossmapRepositoryExtension crossmapRepository,
                                  StagingRecordRepositoryExtension stagingRecordRepository,
                                  StagingRecordUpdater stagingRecordUpdater) {
        super(crossmapRepository, stagingRecordRepository, stagingRecordUpdater);
    }
}
