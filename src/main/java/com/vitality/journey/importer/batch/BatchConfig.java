package com.vitality.journey.importer.batch;

import com.vitality.journey.importer.batch.job.ImportJob;
import com.vitality.journey.importer.batch.job.JobCompletionListener;
import com.vitality.journey.importer.batch.step.enrich.EnrichStepFactory;
import com.vitality.journey.importer.batch.step.enroll.EnrollStepFactory;
import com.vitality.journey.importer.batch.step.normalize.NormalizeStepFactory;
import com.vitality.journey.importer.batch.step.stage.StagingStepFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BatchConfig {

    private final StagingStepFactory stagingStepFactory;
    private final NormalizeStepFactory normalizeStepFactory;
    private final EnrichStepFactory enrichStepFactory;
    private final EnrollStepFactory enrollStepFactory;
    private final JobCompletionListener jobCompletionListener;

    /**
     * DPP CSV Import Job is responsible for importing DPP CSV data.
     * It will run the following steps in order:
     *
     * 1. Staging Data Cleanup Step
     * 2. Staging Step
     * 3. Normalize Step
     * 4. Enrich Step
     *
     * Enrollment step is not included in this job to separate processes of staging data and actual enrollment into journey.
     * Enrollment step can be executed separately after the import job is completed.
     */
    @Bean
    public Job dppImportJob(JobRepository jobRepository,
                            Step stagingDataCleanupStep,
                            Step stagingStep,
                            Step normalizeStep,
                            Step enrichStep) {
        return new JobBuilder(ImportJob.DPP_CSV_IMPORT.getJobName(), jobRepository)
            .incrementer(new RunIdIncrementer())
            .start(stagingDataCleanupStep)
            .next(stagingStep)
            .next(normalizeStep)
            .next(enrichStep)
            .listener(jobCompletionListener)
            .build();
    }

    /**
     * DPP Enrollment Job is responsible for enrolling members into DPP journey.
     * It can be run for the whole import data set or for a specific member.
     */
    @Bean
    public Job dppEnrollJob(JobRepository jobRepository, Step enrollStep) {
        return new JobBuilder(ImportJob.DPP_ENROLL.getJobName(), jobRepository)
            .incrementer(new RunIdIncrementer())
            .start(enrollStep)
            .listener(jobCompletionListener)
            .build();
    }

    /**
     * DPP Enrichment Job is responsible for enriching staging data with entity information.
     * It can be run for the whole import data set or for a specific member.
     * It is separated from the import job to allow for re-enrichment in case entity resolution fails.
     */
    @Bean
    public Job dppEnrichJob(JobRepository jobRepository, Step enrichStep) {
        return new JobBuilder(ImportJob.DPP_ENRICH.getJobName(), jobRepository)
            .incrementer(new RunIdIncrementer())
            .start(enrichStep)
            .listener(jobCompletionListener)
            .build();
    }

    /**
     * DPP Normalization Job is responsible for normalizing staging data.
     * It can be run for the whole import data set or for a specific member.
     * It is separated from the import job to allow for re-normalization in case of data quality issues.
     */
    @Bean
    public Job dppNormalizeJob(JobRepository jobRepository, Step normalizeStep) {
        return new JobBuilder(ImportJob.DPP_NORMALIZE.getJobName(), jobRepository)
            .incrementer(new RunIdIncrementer())
            .start(normalizeStep)
            .listener(jobCompletionListener)
            .build();
    }

    @Bean
    public Step stagingDataCleanupStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return stagingStepFactory.createStagingDataCleanupStep(jobRepository, transactionManager);
    }

    @Bean
    public Step stagingStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return stagingStepFactory.createStagingStep(jobRepository, transactionManager);
    }

    @Bean
    public Step normalizeStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return normalizeStepFactory.createStep(jobRepository, transactionManager);
    }

    @Bean
    public Step enrichStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return enrichStepFactory.createStep(jobRepository, transactionManager);
    }

    @Bean
    public Step enrollStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return enrollStepFactory.createStep(jobRepository, transactionManager);
    }
}
