package com.vitality.journey.importer.batch.step.stage;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.mapper.StagingRecordMapper;
import com.vitality.journey.importer.service.csv.ParsedCsvRow;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class StagingItemProcessor implements ItemProcessor<ParsedCsvRow, StagingRecord> {

    private final StagingRecordMapper stagingRecordMapper;

    @Override
    public StagingRecord process(@NonNull ParsedCsvRow csvRow) {
        return stagingRecordMapper.toEntity(csvRow);
    }
}
