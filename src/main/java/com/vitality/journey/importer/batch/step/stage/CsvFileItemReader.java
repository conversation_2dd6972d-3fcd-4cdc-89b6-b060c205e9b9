package com.vitality.journey.importer.batch.step.stage;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.vitality.journey.importer.batch.job.JobParametersFactory;
import com.vitality.journey.importer.service.csv.ParsedCsvRow;
import com.vitality.journey.importer.util.CsvUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;
import java.util.Arrays;

@Slf4j
@Component
public class CsvFileItemReader extends FlatFileItemReader<ParsedCsvRow> implements StepExecutionListener {

    private final ParsedCsvRowLineMapper parsedCsvRowLineMapper;

    public CsvFileItemReader() {
        this.parsedCsvRowLineMapper = new ParsedCsvRowLineMapper();
        // Set LineMapper during construction to satisfy FlatFileItemReader requirements
        setLineMapper(parsedCsvRowLineMapper);
    }

    @Override
    public void beforeStep(StepExecution stepExecution) {
        String csvFilePath = stepExecution.getJobExecution()
                .getJobParameters().getString(JobParametersFactory.PARAM_FILE_PATH);

        if (csvFilePath == null) {
            throw new IllegalStateException("CSV file path not found in job parameters");
        }

        try {
            String[] headers = CsvUtils.parseCsvHeader(Paths.get(csvFilePath));
            parsedCsvRowLineMapper.setHeaders(headers);

            setLineMapper(parsedCsvRowLineMapper);
            setResource(new FileSystemResource(csvFilePath));
            setLinesToSkip(1); // Skip header line

            log.info("Initialized CSV reader for file: {} with headers: {}", csvFilePath, Arrays.toString(headers));
        } catch (Exception e) {
            throw new StagingStepException("Failed to initialize CSV reader for file: " + csvFilePath, e);
        }
    }

    public static class ParsedCsvRowLineMapper extends DefaultLineMapper<ParsedCsvRow> {
        private final DelimitedLineTokenizer tokenizer;


        public ParsedCsvRowLineMapper() {
            tokenizer = new DelimitedLineTokenizer();
            tokenizer.setDelimiter(",");
            tokenizer.setQuoteCharacter('"');
            // tokenizer.setStrict(false); // Allow variable token counts

            setFieldSetMapper(ParsedCsvRowLineMapper::parseCsvRow);
            setLineTokenizer(tokenizer);
        }

        @NonNull
        @Override
        public ParsedCsvRow mapLine(@NonNull String line, int lineNumber) {
            try {
                return super.mapLine(line, lineNumber);
            } catch (Exception e) {
                log.warn("Failed to parse line {}: {}", lineNumber, e.getMessage());
                return new ParsedCsvRow(null, line, e);
            }
        }

        private static ParsedCsvRow parseCsvRow(FieldSet fieldSet) {
            ObjectNode jsonNode = JsonNodeFactory.instance.objectNode();
            String[] names = fieldSet.getNames();
            String[] values = fieldSet.getValues();

            for (int i = 0; i < names.length; i++) {
                String value = values[i];
                if (value != null && !value.trim().isEmpty()) {
                    jsonNode.put(names[i], value);
                }
            }

            return new ParsedCsvRow(jsonNode, String.join(",", values), null);
        }

        public void setHeaders(String[] headers) {
            tokenizer.setNames(headers);
        }
    }
}
