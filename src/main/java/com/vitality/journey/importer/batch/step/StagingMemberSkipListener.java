package com.vitality.journey.importer.batch.step;

import com.vitality.journey.importer.batch.BatchJobExecutionUtils;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.database.repository.StagingToEntityCrossmapRepositoryExtension;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.SkipListener;

@Slf4j
@RequiredArgsConstructor
public class StagingMemberSkipListener implements SkipListener<Member, Member> {
    private final StagingToEntityCrossmapRepositoryExtension crossmapRepository;
    private final StagingRecordRepositoryExtension stagingRecordRepository;
    private final StagingRecordUpdater stagingRecordUpdater;

    @Override
    public void onSkipInProcess(Member item, @NonNull Throwable t) {
        log.error("Skipped member {} during enrichment processing", item.getUniqueId(), t);
        saveError(item, t);
    }

    @Override
    public void onSkipInWrite(Member item, @NonNull Throwable t) {
        log.error("Skipped member {} during enrichment write", item.getUniqueId(), t);
        saveError(item, t);
    }

    private void saveError(Member member, Throwable t) {
        Long jobExecutionId = BatchJobExecutionUtils.getJobExecutionId();
        Long stepId = BatchJobExecutionUtils.getStepExecutionId();

        crossmapRepository.findByEntityReference(jobExecutionId, member.getId(), "MEMBER")
            .flatMap(crossmap -> stagingRecordRepository.findById(crossmap.getId().getStagingRecordId()))
            .ifPresentOrElse(stagingRecord -> stagingRecordUpdater.saveError(stagingRecord, stepId, t),
                () -> log.warn("Staging record not found for member {} in job {}", member.getUniqueId(), jobExecutionId));
    }
}
