package com.vitality.journey.importer.batch.step;

import com.vitality.journey.importer.batch.job.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.imprt.EntityCrossmapService;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;

import java.util.List;
import java.util.Objects;
import java.util.OptionalLong;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractMemberItemWriter implements ItemWriter<Member> {
    private final MemberRepositoryExtension memberRepository;
    private final StagingRecordUpdater stagingRecordUpdater;
    private final EntityCrossmapService entityCrossmapService;

    protected void update(Member member, RecordStatus status) {
        memberRepository.save(member);

        getStagingRecordId(member).ifPresentOrElse(
            stagingRecordId -> stagingRecordUpdater.updateStatus(stagingRecordId, status),
            () -> log.warn("Staging record not found for member {}", member.getUniqueId())
        );
    }

    protected OptionalLong getStagingRecordId(Member member) {
        StepContext context = Objects.requireNonNull(StepSynchronizationManager.getContext());
        StepExecution stepExecution = context.getStepExecution();
        JobParameters params = stepExecution.getJobParameters();
        Long jobExecutionId = params.getLong(JobParametersFactory.PARAM_JOB_EXECUTION_ID);

        if (jobExecutionId == null) {
            return entityCrossmapService.getStagingRecordId(member);
        } else {
            return entityCrossmapService.getStagingRecordId(jobExecutionId, member);
        }
    }

    protected List<Long> getIds(Chunk<? extends Member> chunk) {
        return chunk.getItems().stream().map(Member::getId).toList();
    }

}
