package com.vitality.journey.importer.batch.step.stage;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.repository.StagingRecordRepository;
import com.vitality.journey.importer.service.imprt.EntityCrossmapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class StagingItemWriter implements ItemWriter<StagingRecord> {

    private final StagingRecordRepository stagingRecordRepository;
    private final EntityCrossmapService entityCrossmapService;
    private StepExecution currentStepExecution;

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        this.currentStepExecution = stepExecution;
    }

    @Override
    public void write(Chunk<? extends StagingRecord> chunk) {
        for (StagingRecord stagingRecord : chunk) {
            StagingRecord saved = stagingRecordRepository.save(stagingRecord);
            entityCrossmapService.saveCrossmapEntry(currentStepExecution.getJobExecution().getJobId(), saved.getId());

            log.debug("Saved staging record {} with status {}", saved.getId(), saved.getStatus());
        }

        if (log.isInfoEnabled()) {
            List<Long> stagingRecordIds = chunk.getItems().stream().map(StagingRecord::getId).toList();
            log.info("Saved a chunk of {} staging records - {}", chunk.size(), stagingRecordIds);
        }
    }
}
