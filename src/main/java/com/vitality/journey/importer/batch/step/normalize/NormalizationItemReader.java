package com.vitality.journey.importer.batch.step.normalize;

import com.vitality.journey.importer.batch.BatchJobExecutionUtils;
import com.vitality.journey.importer.batch.job.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class NormalizationItemReader extends RepositoryItemReader<StagingRecord> {
    private static final int PAGE_SIZE = 100;

    public NormalizationItemReader(StagingRecordRepositoryExtension stagingRecordRepository) {
        setRepository(stagingRecordRepository);
        setMethodName(StagingRecordRepositoryExtension.FIND_BY_JOB_ID_AND_STATUS);
        setSort(Map.of("id", Sort.Direction.ASC));
        setPageSize(PAGE_SIZE);
    }

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        JobParameters params = stepExecution.getJobParameters();
        Long stagingRecordId = params.getLong(JobParametersFactory.PARAM_STAGING_RECORD_ID);

        if (stagingRecordId != null) {
            setMethodName(StagingRecordRepositoryExtension.FIND_BY_ID_PAGED);
            setArguments(List.of(stagingRecordId));
        } else {
            Long jobExecutionId = BatchJobExecutionUtils.getJobExecutionId();
            setMethodName(StagingRecordRepositoryExtension.FIND_BY_JOB_ID_AND_STATUS);
            setArguments(List.of(jobExecutionId, RecordStatus.LOADED.name()));
        }
    }
}
