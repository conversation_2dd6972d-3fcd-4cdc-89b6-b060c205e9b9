package com.vitality.journey.importer.batch.step.enroll;

import com.vitality.journey.importer.batch.step.AbstractMemberItemWriter;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.imprt.EntityCrossmapService;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.Chunk;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EnrollmentItemWriter extends AbstractMemberItemWriter {

    public EnrollmentItemWriter(MemberRepositoryExtension memberRepository,
                                StagingRecordUpdater stagingRecordUpdater,
                                EntityCrossmapService entityCrossmapService) {
        super(memberRepository, stagingRecordUpdater, entityCrossmapService);
    }

    @Override
    public void write(Chunk<? extends Member> chunk) {
        for (Member member : chunk.getItems()) {
            update(member, RecordStatus.ENROLLED);
            log.debug("Enrolled member {} with entityNo {}", member.getUniqueId(), member.getEntityNo());
        }

        if (log.isInfoEnabled()) {
            log.info("Saved a chunk of {} enrolled member records - {}", chunk.size(), getIds(chunk));
        }
    }
}
