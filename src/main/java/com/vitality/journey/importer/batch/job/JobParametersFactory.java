package com.vitality.journey.importer.batch.job;

import com.vitality.journey.importer.model.ProcessingMode;
import lombok.NoArgsConstructor;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;

import java.nio.file.Path;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class JobParametersFactory {
    public static final String PARAM_ORIGINAL_FILE_NAME = "originalFileName";
    public static final String PARAM_FILE_PATH = "filePath";
    public static final String PARAM_EMPLOYER_ID = "employerId";
    public static final String PARAM_ENTITY_NO = "entityNo";
    public static final String PARAM_STAGING_RECORD_ID = "stagingRecordId";
    public static final String PARAM_PROCESSING_MODE = "processingMode";
    public static final String PARAM_JOB_EXECUTION_ID = "jobExecutionId";
    public static final String PARAM_LAUNCH_TIMESTAMP = "launchTimestamp";

    public static JobParameters getImportJobParameters(String originalFileName, Path filePath, Long employerId) {
        return new JobParametersBuilder()
            .addString(PARAM_ORIGINAL_FILE_NAME, originalFileName)
            .addString(PARAM_FILE_PATH, filePath.toString())
            .addLong(PARAM_EMPLOYER_ID, employerId)
            .addLong(PARAM_LAUNCH_TIMESTAMP, System.currentTimeMillis())
            .addString(PARAM_PROCESSING_MODE, ProcessingMode.FULL.name())
            .toJobParameters();
    }

    public static JobParameters getImportJobParameters(Path filePath) {
        return new JobParametersBuilder()
            .addString(PARAM_FILE_PATH, filePath.toString())
            .addLong(PARAM_LAUNCH_TIMESTAMP, System.currentTimeMillis())
            .addString(PARAM_PROCESSING_MODE, ProcessingMode.FULL.name())
            .toJobParameters();
    }

    public static JobParameters getEnrollJobParameters(Long jobExecutionId, Long entityNo) {
        return createParamsForJobAndEntityNo(jobExecutionId, entityNo);
    }

    public static JobParameters getEnrichJobParameters(Long jobExecutionId, Long stagingRecordId) {
        JobParametersBuilder builder = createParamsBuilder(jobExecutionId);

        if (stagingRecordId != null) {
            builder.addLong(PARAM_STAGING_RECORD_ID, stagingRecordId);
        }
        return builder.toJobParameters();
    }

    public static JobParameters getNormalizeJobParameters(Long jobExecutionId, long employerId, Long stagingRecordId) {
        JobParametersBuilder builder = createParamsBuilder(jobExecutionId)
            .addLong(PARAM_EMPLOYER_ID, employerId);

        if (stagingRecordId != null) {
            builder.addLong(PARAM_STAGING_RECORD_ID, stagingRecordId);
        }
        return builder.toJobParameters();
    }

    private static JobParameters createParamsForJobAndEntityNo(Long jobExecutionId, Long entityNo) {
        JobParametersBuilder builder = createParamsBuilder(jobExecutionId);
        if (entityNo != null) {
            builder.addLong(PARAM_ENTITY_NO, entityNo);
        }
        return builder.toJobParameters();
    }

    private static JobParametersBuilder createParamsBuilder(Long jobExecutionId) {
        JobParametersBuilder builder = new JobParametersBuilder()
            .addLong(PARAM_LAUNCH_TIMESTAMP, System.currentTimeMillis())
            .addString(PARAM_PROCESSING_MODE, ProcessingMode.FULL.name());

        if (jobExecutionId != null) {
            builder.addLong(PARAM_JOB_EXECUTION_ID, jobExecutionId);
        }
        return builder;
    }
}
