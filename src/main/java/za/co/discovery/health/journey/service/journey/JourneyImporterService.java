package za.co.discovery.health.journey.service.journey;

import com.vitality.journey.importer.domain.MemberDto;
import com.vitality.journey.importer.domain.MemberJourneyTemplateDto;
import com.vitality.journey.importer.domain.MemberMilestoneDto;
import com.vitality.journey.importer.domain.MemberProgramDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import za.co.discovery.health.journey.constant.JourneyAttr;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivityTxn;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneRepository;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.NextMilestoneNeededEvent;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.ActivityStatus;
import za.co.discovery.health.journey.service.CdcIdentifierService;
import za.co.discovery.health.journey.service.dataattr.DataAttributesProcessor;
import za.co.discovery.health.journey.service.journey.processor.JourneyCategoryService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Objects.requireNonNull;
import static java.util.Objects.requireNonNullElse;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("PMD.ExcessiveImports")
public class JourneyImporterService {

    private static final String PA_MNEMONIC = "SWDA";
    private static final String WEIGHT_IN_MNEMONIC = "MNWI";
    private static final String INITIAL_WEIGHT_MNEMONIC = "INIT_WEIGHT";

    private static final String SESSION_MODE_LIVE = "LIVE";
    private static final String SESSION_MODE_RECORDING = "RECORDING";

    private final JourneyCategoryService categoryService;
    private final JourneyEnrollmentService enrollmentService;
    private final JourneyProgramService programService;
    private final JourneyEventPublisher eventPublisher;
    private final CdcIdentifierService cdcIdService;
    private final JourneyEnrollmentMilestoneService milestoneService;
    private final ExtendedJourneyEnrollmentMilestoneRepository milestoneRepository;
    private final DataAttributesProcessor dataAttributesProcessor;

    @Transactional
    public long importJourney(final MemberJourneyTemplateDto journeyTemplate) {
        final MemberDto member = requireNonNull(journeyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());

        log.info("Importing journey '{}' for member: {}", journeyTemplate.getJourneyName(), entityNo);

        final long start = System.currentTimeMillis();

        final JourneyEnrollment enrollment = createJourneyEnrollment(journeyTemplate);

        saveJourneyActivities(enrollment, journeyTemplate);

        dataAttributesProcessor.process(journeyTemplate);

        log.info("Journey imported for member: {} in {}ms", entityNo, System.currentTimeMillis() - start);

        return enrollment.getJourneyEnrollmentId();
    }

    private JourneyEnrollment createJourneyEnrollment(final MemberJourneyTemplateDto journeyTemplate) {
        final MemberDto member = requireNonNull(journeyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());
        final JourneyCategory journey = findJourneyCategory(journeyTemplate);
        final MemberProgramDto memberProgram = requireNonNull(journeyTemplate.getProgram());
        final JourneyProgram firstProgram = findFirstProgram(journey, memberProgram.getProgramStartDate(), entityNo);

        final int deletedJourneys = enrollmentService.deleteUserEnrollments(entityNo, journey.getJourneyCategoryId());

        if (deletedJourneys > 0) {
            log.info(
                "Deleted {} enrollments for journey: {} and member: {}",
                deletedJourneys,
                journey.getJourneyCategoryId(),
                entityNo);
        }

        final Map<String, String> attributes = buildAttributes(journeyTemplate.getAttributes(), member);

        final JourneyEnrollment enrollment = enrollmentService.createEnrollment(
            entityNo, firstProgram, journey, memberProgram.getProgramStartDate(), attributes);

        log.info("Created enrollment {} for journey category: {}, member: {}",
            enrollment.getJourneyEnrollmentId(), enrollment.getJourneyCategory().getJourneyCategoryId(), entityNo);

        createMilestones(enrollment, memberProgram);

        return enrollment;
    }

    @SuppressWarnings("PMD.AvoidInstantiatingObjectsInLoops")
    private void createMilestones(final JourneyEnrollment enrollment, final MemberProgramDto memberProgram) {
        final List<MemberMilestoneDto> milestones =
            requireNonNullElse(memberProgram.getMilestones(), Collections.emptyList());
        milestones.stream()
            .filter(Objects::nonNull)
            .filter(milestoneDto -> Objects.nonNull(milestoneDto.getIteration()))
            .sorted(Comparator.comparing(MemberMilestoneDto::getIteration))
            .forEach(milestoneDto -> {
                final NextMilestoneNeededEvent event = new NextMilestoneNeededEvent(
                    enrollment, requireNonNull(milestoneDto.getIteration()), milestoneDto.getStartDate());

                eventPublisher.publish(event);
            });

        if (!milestones.isEmpty()) {
            log.info(
                "Created {} milestones for enrollment: {}, user: {}",
                milestones.size(),
                enrollment.getJourneyEnrollmentId(),
                enrollment.getEntityId());
        }
    }

    private JourneyProgram findFirstProgram(final JourneyCategory journeyCategory,
                                            final LocalDateTime startDate,
                                            final long entityNo) {
        final JourneyProgram firstProgram = programService.getFirstProgram(journeyCategory, startDate);
        if (firstProgram == null) {
            throw new IllegalArgumentException(
                "First program not found for journey: " + journeyCategory.getName() + ", member: " + entityNo);
        }
        return firstProgram;
    }

    private JourneyCategory findJourneyCategory(final MemberJourneyTemplateDto memberJourneyTemplate) {
        final MemberDto member = requireNonNull(memberJourneyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());

        final JourneyCategory journeyCategory = categoryService.findByNameAndType(
            memberJourneyTemplate.getJourneyName(), memberJourneyTemplate.getJourneyType());

        if (journeyCategory == null) {
            throw new IllegalArgumentException("Journey category not found for journey: "
                + memberJourneyTemplate.getJourneyName()
                + ", member: "
                + entityNo);
        }
        return journeyCategory;
    }

    private Map<String, String> buildAttributes(final Map<String, String> providedAttributes, final MemberDto member) {
        final Map<String, String> attributes = new HashMap<>();
        if (providedAttributes != null) {
            attributes.putAll(providedAttributes);

            String cdcId = member.getCdcId();
            if (!StringUtils.hasText(cdcId)) {
                cdcId = cdcIdService.getCdcIdentifier(member.getEntityNo());
            }

            attributes.put(JourneyAttr.CDC_IDENTIFIER.getName(), cdcId);
        }
        return attributes;
    }

    private void saveDataAttributes(final MemberJourneyTemplateDto journeyTemplate) {

    }

    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    private void saveJourneyActivities(final JourneyEnrollment enrollment,
                                       final MemberJourneyTemplateDto journeyTemplate) {
        final MemberProgramDto programDto = requireNonNull(journeyTemplate.getProgram());
        final List<MemberMilestoneDto> milestoneDtoList = requireNonNull(programDto.getMilestones());

        final var milestonesByIteration = getMilestonesGroupedByIteration(enrollment);

        for (final MemberMilestoneDto milestoneDto : milestoneDtoList) {
            final JourneyEnrollmentMilestone milestone = milestonesByIteration.get(milestoneDto.getIteration());
            if (milestone.getMilestoneIteration() == 1) {
                saveInitialWeightIn(milestone, journeyTemplate);
            }
            saveMilestoneProgress(milestoneDto, milestone);

            log.info(
                "Milestone {} progress saved for user {} with status {}",
                milestone.getMilestoneIteration(),
                enrollment.getEntityId(),
                milestone.getMilestoneStatus());
        }

        final MemberDto member = requireNonNull(journeyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());
        log.info("Journey activities are saved for member: {}", entityNo);
    }

    private static Map<Integer, JourneyEnrollmentMilestone> getMilestonesGroupedByIteration(JourneyEnrollment enrollment) {
        return enrollment.getJourneyEnrollmentMilestones().stream()
            .collect(Collectors.toMap(
                journeyEnrollmentMilestone -> (int) journeyEnrollmentMilestone.getMilestoneIteration(),
                Function.identity()));
    }

    private void saveMilestoneProgress(
        final MemberMilestoneDto milestoneDto, final JourneyEnrollmentMilestone milestone) {
        saveWeightIn(milestone, milestoneDto);
        savePhysicalActivityMinutes(milestone, milestoneDto);
        saveSessionAttendance(milestone, milestoneDto);

        milestoneRepository.save(milestone);

        final boolean milestoneComplete =
            milestoneService.isMilestoneComplete(milestone.getJourneyEnrollmentMilestoneId());
        if (milestoneComplete) {
            milestoneService.markAsCompleted(milestone, milestoneDto.getStartDate());
        } else if (milestone.getMilestoneTo().isBefore(LocalDateTime.now())) {
            milestoneService.markAsNotAchieved(milestone);
        }
    }

    private void saveInitialWeightIn(
        final JourneyEnrollmentMilestone milestone, final MemberJourneyTemplateDto journeyTemplate) {
        final MemberProgramDto program = requireNonNull(journeyTemplate.getProgram());
        final Float startingWeight = program.getStartingWeight();

        if (startingWeight != null) {
            getActivityByMnemonic(milestone, INITIAL_WEIGHT_MNEMONIC).ifPresent(activity -> {
                final LocalDateTime completionDate = program.getProgramStartDate();
                final String value = String.valueOf(startingWeight.intValue());
                completeActivity(activity, completionDate, value);
            });
        }
    }

    private void saveWeightIn(final JourneyEnrollmentMilestone milestone, final MemberMilestoneDto milestoneDto) {
        final long entityId = milestone.getJourneyEnrollment().getEntityId();

        final Double weight = milestoneDto.getWeight();
        if (weight != null) {
            getActivityByMnemonic(milestone, WEIGHT_IN_MNEMONIC)
                .ifPresentOrElse(
                    activity -> {
                        final LocalDateTime completionDate = milestoneDto.getStartDate();
                        final String value = String.valueOf(weight.intValue());
                        completeActivity(activity, completionDate, value);
                    },
                    () -> log.warn(
                        WEIGHT_IN_MNEMONIC
                            + " activity not found for user {} milestone {}, cannot set weight",
                        entityId,
                        milestone.getMilestoneIteration()));
        }
    }

    private void savePhysicalActivityMinutes(
        final JourneyEnrollmentMilestone milestone, final MemberMilestoneDto milestoneDto) {
        final long entityId = milestone.getJourneyEnrollment().getEntityId();

        final Integer activityMinutes = milestoneDto.getActivityMinutes();
        if (activityMinutes != null) {
            getActivityByMnemonic(milestone, PA_MNEMONIC)
                .ifPresentOrElse(
                    activity -> {
                        final LocalDateTime completionDate = milestoneDto.getStartDate();
                        final String value = String.valueOf(activityMinutes);
                        completeActivity(activity, completionDate, value);
                    },
                    () -> log.warn(
                        PA_MNEMONIC
                            + " activity not found for user {} milestone {}, cannot set physical activity",
                        entityId,
                        milestone.getMilestoneIteration()));
        }
    }

    private void saveSessionAttendance(
        final JourneyEnrollmentMilestone milestone, final MemberMilestoneDto milestoneDto) {
        final long entityId = milestone.getJourneyEnrollment().getEntityId();

        getActivityByType(milestone, ActivityDetailsType.APPOINTMENT)
            .ifPresentOrElse(
                activity -> {
                    final String attendanceMode = requireNonNull(milestoneDto.getAttendanceMode());
                    if (SESSION_MODE_LIVE.equals(attendanceMode)
                        || SESSION_MODE_RECORDING.equals(attendanceMode)) {
                        final LocalDate sessionDate = requireNonNull(milestoneDto.getSessionDate());
                        final LocalDateTime completionDate = sessionDate.atTime(12, 0, 0);

                        completeActivity(
                            activity, completionDate, null, SESSION_MODE_RECORDING.equals(attendanceMode));
                    }
                },
                () -> log.warn(
                    "{} activity not found for user {} milestone {}, cannot set session attendance",
                    ActivityDetailsType.APPOINTMENT,
                    entityId,
                    milestone.getMilestoneIteration()));
    }

    private static void completeActivity(
        final JourneyEnrollmentMilestoneActivity activity, final LocalDateTime completionDate, final String value) {
        completeActivity(activity, completionDate, value, false);
    }

    private static void completeActivity(
        final JourneyEnrollmentMilestoneActivity activity,
        final LocalDateTime completionDate,
        final String value,
        final boolean isLateCompleted) {
        final String status =
            isLateCompleted ? ActivityStatus.LATE_COMPLETED.getValue() : ActivityStatus.COMPLETED.getValue();
        activity.setActivityStatus(status);
        activity.setCompletedAt(completionDate);
        activity.setActivityCompletionCount(1);

        final var transaction = new JourneyEnrollmentMilestoneActivityTxn(
            activity, completionDate, activity.getJourneyEnrollmentMilestoneActivityId(), value);
        activity.getJourneyEnrollmentMilestoneActivityTxns().add(transaction);
    }

    private Optional<JourneyEnrollmentMilestoneActivity> getActivityByMnemonic(
        final JourneyEnrollmentMilestone milestone, final String mnemonic) {
        return milestone.getJourneyEnrollmentMilestoneActivities().stream()
            .filter(activity -> mnemonic.equals(activity.getActivityMnemonicId()))
            .findFirst();
    }

    private Optional<JourneyEnrollmentMilestoneActivity> getActivityByType(
        final JourneyEnrollmentMilestone milestone, final ActivityDetailsType activityType) {
        return milestone.getJourneyEnrollmentMilestoneActivities().stream()
            .filter(activity -> activityType.name().equals(activity.getActivityType()))
            .findFirst();
    }
}
