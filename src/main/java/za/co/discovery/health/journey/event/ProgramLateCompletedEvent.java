package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;

@Getter
public class ProgramLateCompletedEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;
    private final LocalDateTime completionTime;

    public ProgramLateCompletedEvent(final JourneyEnrollment enrollment, final LocalDateTime completionTime) {
        super(enrollment.getEntityId(), completionTime);
        this.enrollment = enrollment;
        this.completionTime = completionTime;
    }
}
