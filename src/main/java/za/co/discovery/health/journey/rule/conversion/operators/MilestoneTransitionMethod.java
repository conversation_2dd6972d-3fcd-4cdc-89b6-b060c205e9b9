package za.co.discovery.health.journey.rule.conversion.operators;

import lombok.RequiredArgsConstructor;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.resolver.rule.model.request.MilestoneTransitionRuleRequest;

import java.util.List;

@RequiredArgsConstructor
public class MilestoneTransitionMethod {
    private final List<MilestoneTransitionRuleRequest.MilestoneActivityDetails> ruleRequest;

    public boolean hasCompletedAllMandatoryActivitiesAtLeastOnce(
            final List<MilestoneTransitionRuleRequest.MilestoneActivityDetails> activityDetails) {

        return activityDetails.stream()
                .filter(it -> it.getFlexibility().equals(Flexibility.MANDATORY))
                .allMatch(it -> it.getCompletionCount() > 0);
    }

    public boolean hasCompletedAllMandatoryActivitiesAtLeastOnce() {
        return hasCompletedAllMandatoryActivitiesAtLeastOnce(ruleRequest);
    }

    public boolean hasMetAllConditions(
            final List<MilestoneTransitionRuleRequest.MilestoneActivityDetails> activityDetails) {

        return activityDetails.stream()
                .filter(it -> it.getFlexibility().equals(Flexibility.MANDATORY))
                .allMatch(it -> it.getCompletionCount().equals(it.getRequiredCount()));
    }

    public boolean hasMetAllConditions() {
        return hasMetAllConditions(ruleRequest);
    }
}
