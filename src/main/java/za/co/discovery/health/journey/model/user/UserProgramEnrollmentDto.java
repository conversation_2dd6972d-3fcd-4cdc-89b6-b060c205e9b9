package za.co.discovery.health.journey.model.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import za.co.discovery.health.journey.model.JourneyBehaviourDto;
import za.co.discovery.health.journey.model.enums.EnrollmentStatus;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class UserProgramEnrollmentDto {

    private Long enrollmentId;
    private Long programId;
    private String programName;
    private String programDescription;
    private LocalDateTime enrollmentDate;
    private LocalDateTime terminationDate;
    private EnrollmentStatus status;
    private JourneyBehaviourDto behaviour;
    private List<UserMilestoneEnrollmentDto> milestones;
    private List<UserEnrollmentProgramReward> rewards;
    private Long order;
}
