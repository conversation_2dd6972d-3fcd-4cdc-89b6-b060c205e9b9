package za.co.discovery.health.journey.rule.dmn.model.dmn;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JourneyEligibilityRule {

    private String journeyCategory;
    private String alliance;
    private String customer;
    private String branch;

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        final JourneyEligibilityRule that = (JourneyEligibilityRule) obj;
        return Objects.equals(journeyCategory, that.journeyCategory)
                && Objects.equals(alliance, that.alliance)
                && Objects.equals(customer, that.customer)
                && Objects.equals(branch, that.branch);
    }

    @Override
    public int hashCode() {
        return Objects.hash(journeyCategory, alliance, customer, branch);
    }
}
