package za.co.discovery.health.journey.config.converter;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public final class MultipartFileHttpMessageConverter extends AbstractHttpMessageConverter<File> {

    public MultipartFileHttpMessageConverter() {
        super(MediaType.ALL);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected Long getContentLength(final File file, final MediaType contentType) throws IOException {
        return file.length();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected MediaType getDefaultContentType(final File file) throws IOException {
        return MediaType.MULTIPART_FORM_DATA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @SuppressWarnings("PMD.AvoidUncheckedExceptionsInSignatures")
    protected File readInternal(final Class<? extends File> cls, final HttpInputMessage inputMessage)
            throws IOException, HttpMessageNotReadableException {
        final File destination = File.createTempFile("temp", ".tmp");
        FileUtils.copyInputStreamToFile(inputMessage.getBody(), destination);
        return destination;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected boolean supports(final Class<?> cls) {
        return File.class.equals(cls);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @SuppressWarnings("PMD.AvoidUncheckedExceptionsInSignatures")
    protected void writeInternal(final File file, final HttpOutputMessage outputMessage)
            throws IOException, HttpMessageNotWritableException {
        try (FileInputStream fis = FileUtils.openInputStream(file)) {
            IOUtils.copyLarge(fis, outputMessage.getBody());
        }
    }
}
