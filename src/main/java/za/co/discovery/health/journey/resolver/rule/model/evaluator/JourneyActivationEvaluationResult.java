package za.co.discovery.health.journey.resolver.rule.model.evaluator;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.output.JourneyActivationDMNRuleOutput;

import java.util.List;
import java.util.stream.Collectors;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class JourneyActivationEvaluationResult extends RuleEvaluationResult {
    private List<CategoryRecommendationResult> results;

    public static JourneyActivationEvaluationResult of(
            final JourneyActivationDMNRuleOutput results, final boolean success) {

        if (success) {
            return JourneyActivationEvaluationResult.builder()
                    .results(results.getOutputs().stream()
                            .map(CategoryRecommendationResult::of)
                            .collect(Collectors.toList()))
                    .success(true)
                    .build();
        } else {
            return JourneyActivationEvaluationResult.builder()
                    .results(List.of())
                    .success(false)
                    .build();
        }
    }

    @Data
    @SuperBuilder(toBuilder = true)
    public static class CategoryRecommendationResult {
        private String category;

        public static CategoryRecommendationResult of(final JourneyActivationDMNRuleOutput.DMNOutput output) {
            return CategoryRecommendationResult.builder()
                    .category(output.getCategory())
                    .build();
        }
    }
}
