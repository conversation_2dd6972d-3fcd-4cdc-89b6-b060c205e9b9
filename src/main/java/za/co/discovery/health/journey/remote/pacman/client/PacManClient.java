package za.co.discovery.health.journey.remote.pacman.client;

import reactor.core.publisher.Mono;
import za.co.discovery.health.pacman.domain.ActivityFilter;
import za.co.discovery.health.pacman.domain.ActivityTransactionResponse;
import za.co.discovery.health.pacman.domain.MemberActivityExclusionFilter;
import za.co.discovery.health.pacman.domain.PageActivity;
import za.co.discovery.health.pacman.domain.PageProgramEnrolmentCohortActivityDTO;
import za.co.discovery.health.pacman.domain.PagingRequest;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

public interface PacManClient {

    Mono<PageProgramEnrolmentCohortActivityDTO> getActivities(
            PagingRequest pagingRequest, String programName, Long entityNo);

    Mono<Map<String, ProgramEnrolmentCohortActivityDTO>> getActivitiesByMnemonic(
            Long entityId, Set<String> mnemonics, Predicate<ProgramEnrolmentCohortActivityDTO> predicate);

    Mono<PageActivity> filterActivity(PagingRequest pagingRequest, ActivityFilter filter);

    Mono<Map<String, ProgramEnrolmentCohortActivityDTO>> getActivitiesByMnemonicWithCustomExclusions(
            Long entityId,
            Set<String> mnemonics,
            Predicate<ProgramEnrolmentCohortActivityDTO> predicate,
            MemberActivityExclusionFilter exclusions);

    Mono<Map<Long, ActivityTransactionResponse>> getTransaction(Long entityId, Set<Long> activityTxnIds);

    Mono<Map<Long, ActivityTransactionResponse>> getTransactionByMnemonic(Long entityId, String mnemonic);
}
