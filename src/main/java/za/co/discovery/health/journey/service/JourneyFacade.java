package za.co.discovery.health.journey.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.model.JourneyDetailsResponse;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.model.enums.UserJourneyState;
import za.co.discovery.health.journey.model.preview.JourneyCategoryPreview;
import za.co.discovery.health.journey.model.preview.JourneyMilestonePreview;
import za.co.discovery.health.journey.model.preview.JourneyProgramPreview;
import za.co.discovery.health.journey.model.preview.JourneyRewardPreview;
import za.co.discovery.health.journey.model.preview.LimitedJourneyCategoryPreview;
import za.co.discovery.health.journey.model.preview.LimitedUserCategoryEnrollmentPreview;
import za.co.discovery.health.journey.model.user.LimitedUserCategoryEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserEnrollmentMilestoneReward;
import za.co.discovery.health.journey.model.user.UserEnrollmentProgramReward;
import za.co.discovery.health.journey.model.user.UserMilestoneEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserProgramEnrollmentDto;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentService;
import za.co.discovery.health.journey.service.journey.JourneyProcessor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("PMD")
@RequiredArgsConstructor
public class JourneyFacade {

    private final JourneyEnrollmentService journeyEnrollmentService;

    private final JourneyProcessor journeyProcessor;

    private final PreviewJourneyCategoryService previewJourneyCategoryService;

    public List<LimitedUserCategoryEnrollmentDto> getActiveLimitedEnrollments(
            final Long entityId, final LocalDateTime currentTime) {
        journeyProcessor.process(entityId, currentTime);

        return journeyEnrollmentService.getActiveLimitedEnrollments(entityId, currentTime).stream()
                .sorted(Comparator.comparing(
                        LimitedUserCategoryEnrollmentDto::getEnrollmentTime,
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    public List<LimitedUserCategoryEnrollmentPreview> getLimitedJourneyCategoryPreview(
            final Long entityId, final LocalDateTime time) {
        log.debug("Getting limited journey category preview for entity {} at time {}", entityId, time);

        journeyProcessor.process(entityId, time);
        // Get current enrollments for the entity
        final List<LimitedUserCategoryEnrollmentDto> categoryEnrollmentDtos =
                journeyEnrollmentService.getLimitedEnrollments(entityId, time);

        // Get available journey categories preview
        final List<LimitedJourneyCategoryPreview> categoriesPreview =
                previewJourneyCategoryService.getLimitedJourneyCategoriesPreview(entityId, time);

        // Map to keep track of categories by ID for efficient lookups
        final Map<Long, LimitedUserCategoryEnrollmentPreview> resultMap = new HashMap<>();

        // Process enrollments first
        if (categoryEnrollmentDtos != null && !categoryEnrollmentDtos.isEmpty()) {
            log.debug("Processing {} enrollments", categoryEnrollmentDtos.size());

            categoryEnrollmentDtos.forEach(enrollment -> {
                final LimitedUserCategoryEnrollmentPreview previewDto = LimitedUserCategoryEnrollmentPreview.builder()
                        .categoryId(enrollment.getCategoryId())
                        .categoryName(enrollment.getCategoryName())
                        .categoryType(enrollment.getCategoryType())
                        .externalReference(enrollment.getExternalReference())
                        .state(enrollment.getState()) // Default to ENROLLED for
                        .startTime(enrollment.getStartTime())
                        .build();

                resultMap.put(enrollment.getCategoryId(), previewDto);
            });
        }

        // Process available categories and merge with existing enrollments or add new ones
        if (categoriesPreview != null && !categoriesPreview.isEmpty()) {
            log.debug("Processing {} available categories", categoriesPreview.size());

            categoriesPreview.forEach(category -> {
                final Long categoryId = category.getId();

                // If category already exists in results (from enrollments), enhance it with
                // additional info
                if (resultMap.containsKey(categoryId)) {
                    final LimitedUserCategoryEnrollmentPreview existingDto = resultMap.get(categoryId);

                    // Update with additional information from category preview
                    existingDto.setJourneyDuration(category.getJourneyDuration());
                    existingDto.setJourneyDurationUnit(category.getJourneyDurationUnit());
                    existingDto.setEnrollmentStartTime(category.getEnrollmentStartTime());
                    existingDto.setEnrollmentEndTime(category.getEnrollmentEndTime());
                    existingDto.setPreconditions(category.getPreconditions());
                    existingDto.setMaxParticipants(category.getMaxParticipants());
                } else if (category.getMaxParticipants() == null
                        || category.getMaxParticipants()
                                > journeyEnrollmentService.countMembersInCategory(categoryId)) {

                    // Create new preview for available category
                    final LimitedUserCategoryEnrollmentPreview previewDto =
                            LimitedUserCategoryEnrollmentPreview.builder()
                                    .categoryId(categoryId)
                                    .categoryType(category.getCategoryType())
                                    .externalReference(category.getExternalReference())
                                    .categoryName(category.getName())
                                    .journeyDuration(category.getJourneyDuration())
                                    .journeyDurationUnit(category.getJourneyDurationUnit())
                                    .enrollmentStartTime(category.getEnrollmentStartTime())
                                    .enrollmentEndTime(category.getEnrollmentEndTime())
                                    .startTime(category.getStartTime())
                                    .preconditions(category.getPreconditions())
                                    .maxParticipants(category.getMaxParticipants())
                                    .build();

                    if (category.getEnrollmentStartTime() != null && category.getEnrollmentEndTime() != null) {

                        if (time.isBefore(category.getEnrollmentStartTime())) {
                            previewDto.setState(UserJourneyState.UPCOMING);
                        } else if (time.isAfter(category.getEnrollmentStartTime())
                                && time.isBefore(category.getEnrollmentEndTime())) {
                            previewDto.setState(UserJourneyState.CAN_ENROLL);
                        } else if (time.isAfter(category.getEnrollmentEndTime())) {
                            previewDto.setState(UserJourneyState.EXPIRED);
                        }
                    }

                    resultMap.put(categoryId, previewDto);
                }
            });
        }

        return resultMap.values().stream()
                .sorted(Comparator.comparing(
                        LimitedUserCategoryEnrollmentPreview::getEnrollmentStartTime,
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    public Optional<JourneyDetailsResponse> getJourneyDetails(
            final Long entityId, final Long categoryId, final LocalDateTime time) {
        log.debug("Getting journey details for entity {} and category {} at time {}", entityId, categoryId, time);
        journeyProcessor.process(entityId, time);

        final Optional<UserCategoryEnrollmentDto> enrollment =
                journeyEnrollmentService.getCategoryEnrollments(categoryId, entityId, time);
        final Optional<JourneyCategoryPreview> categoryPreview =
                previewJourneyCategoryService.getJourneyCategoryPreview(entityId, categoryId, time);

        if (enrollment.isEmpty() && categoryPreview.isEmpty()) {
            return Optional.empty();
        }

        final var responseBuilder = JourneyDetailsResponse.builder().categoryId(categoryId);

        enrollment.ifPresent(e -> responseBuilder
                .categoryName(e.getCategoryName())
                .categoryType(e.getCategoryType())
                .externalReference(e.getExternalReference())
                .startTime(e.getStartTime())
                .state(e.getState()));

        if (categoryPreview.isPresent()) {
            final JourneyCategoryPreview preview = categoryPreview.get();
            final LocalDateTime startTime = preview.getStartTime();
            final long journeyDuration = preview.getJourneyDuration();
            final ChronoUnit journeyDurationUnit = preview.getJourneyDurationUnit();

            if (enrollment.isEmpty() || responseBuilder.build().getCategoryName() == null) {
                responseBuilder.categoryName(preview.getName());
                responseBuilder.categoryType(preview.getCategoryType());
                responseBuilder.externalReference(preview.getExternalReference());
            }

            responseBuilder
                    .journeyDuration(journeyDuration)
                    .journeyDurationUnit(journeyDurationUnit)
                    .enrollmentStartTime(preview.getEnrollmentStartTime())
                    .enrollmentEndTime(preview.getEnrollmentEndTime())
                    .maxParticipants(preview.getMaxParticipants())
                    .preconditions(preview.getPreconditions());

            if (enrollment.isEmpty()) {
                handlePreviewState(responseBuilder, preview, time);
                responseBuilder.startTime(startTime);
            } else {
                responseBuilder.monitoringPeriodEndTime(enrollment.get().getMonitoringPeriodEndTime());
            }

            final List<UserProgramEnrollmentDto> mergedPrograms = mergeJourneyPrograms(
                    enrollment
                            .map(UserCategoryEnrollmentDto::getJourneyPrograms)
                            .orElse(List.of()),
                    preview.getPrograms(),
                    startTime,
                    journeyDurationUnit);
            responseBuilder.journeyPrograms(mergedPrograms);
        }

        return Optional.of(responseBuilder.build());
    }

    private void handlePreviewState(
            final JourneyDetailsResponse.JourneyDetailsResponseBuilder builder,
            final JourneyCategoryPreview preview,
            final LocalDateTime time) {
        if (preview.getEnrollmentStartTime() != null && preview.getEnrollmentEndTime() != null) {
            if (time.isBefore(preview.getEnrollmentStartTime())) {
                builder.state(UserJourneyState.UPCOMING);
            } else if (time.isAfter(preview.getEnrollmentStartTime())
                    && time.isBefore(preview.getEnrollmentEndTime())) {
                builder.state(UserJourneyState.CAN_ENROLL);
            } else if (time.isAfter(preview.getEnrollmentEndTime())) {
                builder.state(UserJourneyState.EXPIRED);
            }
        }
    }

    private List<UserProgramEnrollmentDto> mergeJourneyPrograms(
            final List<UserProgramEnrollmentDto> enrollmentPrograms,
            final List<JourneyProgramPreview> previewPrograms,
            final LocalDateTime startTime,
            final ChronoUnit journeyDurationUnit) {

        final Map<Long, UserProgramEnrollmentDto> enrollmentMap =
                enrollmentPrograms.stream().collect(Collectors.toMap(UserProgramEnrollmentDto::getProgramId, p -> p));

        return previewPrograms.stream()
                .map(preview -> {
                    final UserProgramEnrollmentDto enrollment = enrollmentMap.get(preview.getProgramId());
                    if (enrollment != null) {
                        return mergeProgramWithPreview(enrollment, preview, startTime, journeyDurationUnit);
                    }
                    return convertPreviewProgramToEnrollment(preview, startTime, journeyDurationUnit);
                })
                .collect(Collectors.toList());
    }

    private UserProgramEnrollmentDto mergeProgramWithPreview(
            final UserProgramEnrollmentDto enrollment,
            final JourneyProgramPreview preview,
            final LocalDateTime startTime,
            final ChronoUnit journeyDurationUnit) {

        return enrollment.toBuilder()
                .programName(preview.getProgramName())
                .behaviour(preview.getBehaviour())
                .order(preview.getOrder())
                .milestones(mergeMilestones(
                        enrollment.getMilestones(), preview.getMilestones(), startTime, journeyDurationUnit))
                .rewards(mergeProgramRewards(enrollment.getRewards(), preview.getRewards()))
                .build();
    }

    private List<UserMilestoneEnrollmentDto> mergeMilestones(
            final List<UserMilestoneEnrollmentDto> enrollmentMilestones,
            final List<JourneyMilestonePreview> previewMilestones,
            final LocalDateTime startTime,
            final ChronoUnit journeyDurationUnit) {

        final Map<Long, UserMilestoneEnrollmentDto> enrollmentMap = enrollmentMilestones.stream()
                .collect(Collectors.toMap(UserMilestoneEnrollmentDto::getIteration, m -> m));

        return previewMilestones.stream()
                .map(preview -> {
                    final UserMilestoneEnrollmentDto enrollment = enrollmentMap.get(preview.getIteration());
                    if (enrollment != null) {
                        return mergeMilestoneWithPreview(enrollment, preview);
                    }
                    return convertPreviewMilestoneToEnrollment(
                            preview, Objects.requireNonNullElse(startTime, LocalDateTime.now()), journeyDurationUnit);
                })
                .collect(Collectors.toList());
    }

    private UserMilestoneEnrollmentDto mergeMilestoneWithPreview(
            final UserMilestoneEnrollmentDto milestone, final JourneyMilestonePreview preview) {

        return milestone.toBuilder()
                .activities(mergeActivities(milestone.getActivities(), preview.getActivities()))
                .rewards(mergeMilestoneRewards(milestone, preview.getRewards()))
                .milestoneFrom(milestone.getMilestoneFrom())
                .milestoneTo(milestone.getMilestoneTo())
                .status(milestone.getStatus())
                .build();
    }

    private LocalDateTime calculateMilestoneStart(
            final LocalDateTime startTime, final ChronoUnit unit, final Long iteration) {
        return startTime.plus(iteration - 1, unit);
    }

    private LocalDateTime calculateMilestoneEnd(
            final LocalDateTime startTime, final Long duration, final ChronoUnit unit, final Long iteration) {
        return startTime.plus(iteration * duration, unit);
    }

    private UserProgramEnrollmentDto convertPreviewProgramToEnrollment(
            final JourneyProgramPreview preview, final LocalDateTime startTime, final ChronoUnit journeyDurationUnit) {

        return UserProgramEnrollmentDto.builder()
                .programId(preview.getProgramId())
                .programName(preview.getProgramName())
                .behaviour(preview.getBehaviour())
                .order(preview.getOrder())
                .milestones(preview.getMilestones().stream()
                        .map(m -> convertPreviewMilestoneToEnrollment(m, startTime, journeyDurationUnit))
                        .collect(Collectors.toList()))
                .rewards(preview.getRewards().stream()
                        .map(this::convertPreviewRewardToProgramReward)
                        .collect(Collectors.toList()))
                .build();
    }

    private UserMilestoneEnrollmentDto convertPreviewMilestoneToEnrollment(
            final JourneyMilestonePreview preview,
            final LocalDateTime startTime,
            final ChronoUnit journeyDurationUnit) {

        final LocalDateTime milestoneFrom =
                calculateMilestoneStart(startTime, journeyDurationUnit, preview.getIteration());
        return UserMilestoneEnrollmentDto.builder()
                .iteration(preview.getIteration())
                .milestoneFrom(milestoneFrom)
                .milestoneTo(milestoneFrom.plus(1, journeyDurationUnit))
                .status(MilestoneStatus.LOCKED)
                .activities(preview.getActivities().stream()
                        .map(this::convertPreviewActivityToEnrollment)
                        .collect(Collectors.toList()))
                .rewards(preview.getRewards().stream()
                        .map(this::convertPreviewRewardToMilestoneReward)
                        .collect(Collectors.toList()))
                .build();
    }

    /**
     * Merges activity details from enrollment and preview.
     */
    private List<UserMilestoneEnrollmentDto.ActivityDetails> mergeActivities(
            final List<UserMilestoneEnrollmentDto.ActivityDetails> enrollmentActivities,
            final List<JourneyMilestonePreview.ActivityDetails> previewActivities) {

        if (enrollmentActivities == null || enrollmentActivities.isEmpty()) {
            // Convert preview activities to enrollment format if enrollment activities are empty
            return previewActivities.stream()
                    .map(this::convertPreviewActivityToEnrollment)
                    .collect(Collectors.toList());
        }

        if (previewActivities == null || previewActivities.isEmpty()) {
            return enrollmentActivities;
        }

        // Create a map of preview activities by mnemonic for easy lookup
        final Map<String, JourneyMilestonePreview.ActivityDetails> previewActivityMap = previewActivities.stream()
                .collect(Collectors.toMap(JourneyMilestonePreview.ActivityDetails::getMnemonic, a -> a));

        // Merge each enrollment activity with its corresponding preview activity
        return enrollmentActivities.stream()
                .map(enrollmentActivity -> {
                    final JourneyMilestonePreview.ActivityDetails previewActivity =
                            previewActivityMap.get(enrollmentActivity.getMnemonic());
                    if (previewActivity != null) {
                        return mergeActivityWithPreview(enrollmentActivity, previewActivity);
                    }
                    return enrollmentActivity;
                })
                .collect(Collectors.toList());
    }

    /**
     * Merges a specific activity enrollment with its preview data.
     */
    private UserMilestoneEnrollmentDto.ActivityDetails mergeActivityWithPreview(
            final UserMilestoneEnrollmentDto.ActivityDetails enrollmentActivity,
            final JourneyMilestonePreview.ActivityDetails previewActivity) {

        // Create a builder based on the enrollment activity
        final UserMilestoneEnrollmentDto.ActivityDetails.ActivityDetailsBuilder builder =
                enrollmentActivity.toBuilder();

        // Set activity amount from preview if needed
        if (enrollmentActivity.getActivityAmount() == 0 && previewActivity.getActivityAmount() > 0) {
            builder.activityAmount(previewActivity.getActivityAmount());
        }

        return builder.build();
    }

    /**
     * Merges program rewards from enrollment and preview.
     */
    private List<UserEnrollmentProgramReward> mergeProgramRewards(
            final List<UserEnrollmentProgramReward> enrollmentRewards,
            final List<JourneyRewardPreview> previewRewards) {

        if (enrollmentRewards == null || enrollmentRewards.isEmpty()) {
            // Convert preview rewards to enrollment format if enrollment rewards are empty
            return previewRewards.stream()
                    .map(this::convertPreviewRewardToProgramReward)
                    .collect(Collectors.toList());
        }

        if (previewRewards == null || previewRewards.isEmpty()) {
            return enrollmentRewards;
        }

        return enrollmentRewards;
    }

    /**
     * Merges milestone rewards from enrollment and preview.
     */
    private List<UserEnrollmentMilestoneReward> mergeMilestoneRewards(
            final UserMilestoneEnrollmentDto milestone, final List<JourneyRewardPreview> previewRewards) {

        if (milestone == null) {
            // Convert preview rewards to enrollment format if enrollment rewards are empty
            return previewRewards.stream()
                    .map(this::convertPreviewRewardToMilestoneReward)
                    .collect(Collectors.toList());
        }

        if (MilestoneStatus.completedMilestoneStatuses().contains(milestone.getStatus())) {
            return milestone.getRewards();
        }

        if (previewRewards == null || previewRewards.isEmpty()) {
            return milestone.getRewards();
        }

        return milestone.getRewards();
    }

    /**
     * Converts a preview activity to an enrollment activity.
     */
    private UserMilestoneEnrollmentDto.ActivityDetails convertPreviewActivityToEnrollment(
            final JourneyMilestonePreview.ActivityDetails preview) {
        return UserMilestoneEnrollmentDto.ActivityDetails.builder()
                .mnemonic(preview.getMnemonic())
                .type(preview.getType())
                .name(preview.getName())
                .icon(preview.getIcon())
                .activityCompletionCount(0) // Default to 0 for new enrollments
                .status("LOCKED") // Default status for new enrollments
                .activityAmount(preview.getActivityAmount())
                .isMandatory(preview.isMandatory())
                .preconditionResults(preview.getPreconditionResults())
                .build();
    }

    /**
     * Converts a preview reward to a program reward.
     */
    private UserEnrollmentProgramReward convertPreviewRewardToProgramReward(final JourneyRewardPreview preview) {
        return UserEnrollmentProgramReward.builder()
                .rewardType(preview.getRewardType())
                .rewardValue(preview.getRewardValue())
                .build();
    }

    /**
     * Converts a preview reward to a milestone reward.
     */
    private UserEnrollmentMilestoneReward convertPreviewRewardToMilestoneReward(final JourneyRewardPreview preview) {
        return UserEnrollmentMilestoneReward.builder()
                .rewardType(preview.getRewardType())
                .rewardValue(preview.getRewardValue())
                .build();
    }
}
