package za.co.discovery.health.journey.database.repository;

import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCustomerDefinition;

import java.util.Optional;

@Repository
public interface ExtendedJourneyCustomerDefinitionRepository extends JourneyCustomerDefinitionRepository {
    Optional<JourneyCustomerDefinition> findCustomerDefinitionByAllianceAndGroupIdAndBranch(
            String alliance, String employer, String branch);
}
