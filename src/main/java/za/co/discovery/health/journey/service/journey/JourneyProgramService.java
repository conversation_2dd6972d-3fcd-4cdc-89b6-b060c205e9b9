package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategorization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategorizationRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramRepository;
import za.co.discovery.health.journey.resolver.rule.impl.RuleCalculatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.request.ActivityRecommendationRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityRuleResult;
import za.co.discovery.health.journey.util.BusinessRuleViolation;
import za.co.discovery.health.journey.util.ValidationUtils;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class JourneyProgramService {
    private final ExtendedJourneyProgramRepository repository;
    private final ExtendedJourneyCategorizationRepository categorizationRepository;
    private final RuleCalculatorResolver resolver;
    private final JourneyOrderService orderService;

    @Transactional(readOnly = true)
    public JourneyProgram getFirstProgram(final JourneyCategory category, final LocalDateTime time) {
        ValidationUtils.requireNonNull(category, "Journey category");
        ValidationUtils.validateTimestamp(time, "Time");

        final List<JourneyProgram> programs = getProgramsByCategory(category.getJourneyCategoryId(), time);
        ValidationUtils.validateNotEmpty(programs, "Programs for category " + category.getName());

        final Map<Long, Long> orderMap = getOrderMapForPrograms(category.getJourneyCategoryId(), programs, time);

        return programs.stream()
                .min(Comparator.comparing(
                        program -> orderMap.getOrDefault(program.getJourneyProgramId(), Long.MAX_VALUE)))
                .orElseThrow(() -> BusinessRuleViolation.noProgramsForCategory(category.getJourneyCategoryId()));
    }

    @Transactional(readOnly = true)
    public List<JourneyProgram> getProgramsByCategory(final Long journeyCategoryId, final LocalDateTime time) {
        ValidationUtils.validateCategoryId(journeyCategoryId);
        ValidationUtils.validateTimestamp(time, "Time");

        final List<JourneyProgram> programs =
                categorizationRepository.findByJourneyCategory(journeyCategoryId, time).stream()
                        .map(JourneyCategorization::getJourneyProgram)
                        .collect(Collectors.toList());

        log.debug("Found {} programs for category {}", programs.size(), journeyCategoryId);
        return programs;
    }

    @Transactional(readOnly = true)
    public Optional<JourneyProgram> getNextProgram(
            final Long currentProgramId, final Long journeyCategoryId, final LocalDateTime now) {

        ValidationUtils.validateProgramId(currentProgramId);
        ValidationUtils.validateCategoryId(journeyCategoryId);
        ValidationUtils.validateTimestamp(now, "Current time");

        final List<JourneyProgram> programs = getProgramsByCategory(journeyCategoryId, now);
        if (programs.isEmpty()) {
            log.warn("No programs found for category {}", journeyCategoryId);
            return Optional.empty();
        }

        final Map<Long, Long> orderMap = getOrderMapForPrograms(journeyCategoryId, programs, now);
        final Long currentProgramOrder = orderMap.get(currentProgramId);

        if (currentProgramOrder == null) {
            log.warn("Current program {} not found in category {}", currentProgramId, journeyCategoryId);
            return Optional.empty();
        }

        return findNextProgramInOrder(programs, orderMap, currentProgramId, currentProgramOrder);
    }

    private Optional<JourneyProgram> findNextProgramInOrder(
            final List<JourneyProgram> programs,
            final Map<Long, Long> orderMap,
            final Long currentProgramId,
            final Long currentProgramOrder) {

        return programs.stream()
                .filter(program -> !Objects.equals(program.getJourneyProgramId(), currentProgramId))
                .filter(program ->
                        orderMap.getOrDefault(program.getJourneyProgramId(), Long.MAX_VALUE) > currentProgramOrder)
                .min(Comparator.comparing(program -> orderMap.get(program.getJourneyProgramId())));
    }

    private Map<Long, Long> getOrderMapForPrograms(
            final Long categoryId, final List<JourneyProgram> programs, final LocalDateTime time) {

        final List<Long> programIds =
                programs.stream().map(JourneyProgram::getJourneyProgramId).collect(Collectors.toList());

        return orderService.getOrderByCategoryAndPrograms(categoryId, programIds, time);
    }

    @Transactional(readOnly = true)
    public Map<Long, List<GetActivityRuleResult.RecommendedActivities>> getAllMilestoneActivities(
            final Long journeyProgramId, final Long entityId) {

        ValidationUtils.validateProgramId(journeyProgramId);
        ValidationUtils.validateUserId(entityId);

        final JourneyProgram journeyProgram = findProgramById(journeyProgramId);
        final JourneyProgramBehaviour behaviour = journeyProgram.getJourneyProgramBehaviour();
        final long programDuration = behaviour.getProgramDuration();

        log.debug(
                "Getting all milestone activities for program {} with duration {}", journeyProgramId, programDuration);

        return LongStream.rangeClosed(1, programDuration)
                .boxed()
                .collect(Collectors.toMap(iteration -> iteration, iteration -> getMilestoneActivities(
                                journeyProgramId, entityId, iteration)
                        .getActivities()));
    }

    @Transactional(readOnly = true)
    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    public GetActivityRuleResult getMilestoneActivities(
            final Long journeyProgramId, final Long entityId, final long iteration) {

        ValidationUtils.validateProgramId(journeyProgramId);
        ValidationUtils.validateUserId(entityId);

        if (iteration < 1) {
            throw BusinessRuleViolation.invalidIteration(iteration);
        }

        final JourneyProgram journeyProgram = findProgramById(journeyProgramId);
        final JourneyProgramBehaviour behaviour = journeyProgram.getJourneyProgramBehaviour();

        final GetActivityRuleResult result = evaluateActivityRecommendationRule(entityId, iteration, behaviour);

        if (!result.isSuccess()) {
            throw BusinessRuleViolation.ruleEvaluationFailed(
                    "Activity Recommendation Rule ",
                    "Failed to calculate activities for milestone iteration " + iteration);
        }

        log.debug(
                "Found {} activities for program {} iteration {}",
                result.getActivities().size(),
                journeyProgramId,
                iteration);

        return result;
    }

    private JourneyProgram findProgramById(final Long journeyProgramId) {
        return repository
                .findById(journeyProgramId)
                .orElseThrow(() -> BusinessRuleViolation.programNotFound(journeyProgramId));
    }

    private GetActivityRuleResult evaluateActivityRecommendationRule(
            final Long entityId, final long iteration, final JourneyProgramBehaviour behaviour) {

        final ActivityRecommendationRuleRequest request = ActivityRecommendationRuleRequest.builder()
                .iteration(iteration)
                .ruleName(behaviour
                        .getJourneyRulesByProgramActivityRecommendationRulesId()
                        .getRuleSetName())
                .processorType(RuleProcessorType.valueOf(behaviour
                        .getJourneyRulesByProgramActivityRecommendationRulesId()
                        .getRuleSetType()))
                .entityId(entityId)
                .build();

        return (GetActivityRuleResult) resolver.getEvaluator(RuleType.ACTIVITY_RECOMMENDATION_RULE)
                .calculate(RuleType.ACTIVITY_RECOMMENDATION_RULE, request);
    }
}
