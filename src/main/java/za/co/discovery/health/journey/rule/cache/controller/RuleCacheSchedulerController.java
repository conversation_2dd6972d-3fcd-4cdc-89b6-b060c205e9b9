package za.co.discovery.health.journey.rule.cache.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/bo/cache/rule")
public class RuleCacheSchedulerController {
    private final RuleCacheService cacheService;

    @PostMapping("refresh")
    public void refreshCache() {
        cacheService.init();
    }
}
