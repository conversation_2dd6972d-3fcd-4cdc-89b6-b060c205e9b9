package za.co.discovery.health.journey.config.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReasonCode {
    GENERAL_ERROR(10_000, "general error"),

    ENTITY_ALREADY_CHANGED(100, "Entity already changed"),

    VALIDATION_ERROR(10_001, "validation error"),

    ACCESS_DENIED(10_002, "access denied"),

    CONFLICT(10_003, "Conflicting data"),

    ASSOCIATED_ENTITIES(10_004, "Associated entities should be deleted first"),

    ENTITY_ALREADY_PRESENT(10_005, "Entity already present"),

    ERROR_PROCESSING_EVENT(10_006, "Error processing event"),

    EVALUATION_ERROR(10_007, "Error evaluating Rule"),

    BUSINESS_RULE_VIOLATION(10_008, "Business rule violation"),

    PROCESSING_ERROR(10_009, "Service Unavailable");

    private int code;

    private String description;
}
