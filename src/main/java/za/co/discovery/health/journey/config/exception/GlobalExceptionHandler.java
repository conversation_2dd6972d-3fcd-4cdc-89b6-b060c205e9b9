package za.co.discovery.health.journey.config.exception;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse<?>> handleException(final Exception ex) {
        log.error("Handling unknown exception: ", ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ErrorResponse.builder()
                        .reasonCode(ReasonCode.GENERAL_ERROR)
                        .description(ReasonCode.GENERAL_ERROR.getDescription())
                        .internalCode(ReasonCode.GENERAL_ERROR.getCode())
                        .message(ex.getMessage())
                        .build());
    }

    @ExceptionHandler(ObjectOptimisticLockingFailureException.class)
    public ResponseEntity<ErrorResponse<?>> handleOptimisticException(final Exception ex) {
        log.error("Handling unknown exception: ", ex);
        return ResponseEntity.status(HttpStatus.I_AM_A_TEAPOT)
                .body(ErrorResponse.builder()
                        .reasonCode(ReasonCode.ENTITY_ALREADY_CHANGED)
                        .description(ReasonCode.ENTITY_ALREADY_CHANGED.getDescription())
                        .internalCode(ReasonCode.ENTITY_ALREADY_CHANGED.getCode())
                        .message(ex.getMessage())
                        .build());
    }

    @ExceptionHandler(EntityNotFoundCoreException.class)
    public ResponseEntity<ErrorResponse<?>> handleEntityNotFoundCoreException(final CoreException ex) {
        log.error("Handling known core exception: ", ex);
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ErrorResponse.builder()
                        .reasonCode(ex.getReasonCode())
                        .internalCode(ex.getReasonCode().getCode())
                        .description(ex.getReasonCode().getDescription())
                        .message(ex.getMessage())
                        .build());
    }

    @ExceptionHandler(CoreException.class)
    public ResponseEntity<ErrorResponse<?>> handleCoreException(final CoreException ex) {
        log.error("Handling known core exception: ", ex);
        return ResponseEntity.status(HttpStatus.I_AM_A_TEAPOT)
                .body(ErrorResponse.builder()
                        .reasonCode(ex.getReasonCode())
                        .internalCode(ex.getReasonCode().getCode())
                        .description(ex.getReasonCode().getDescription())
                        .message(ex.getMessage())
                        .build());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<?> onMethodArgumentNotValidException(final MethodArgumentNotValidException ex) {
        log.error("Got not valid argument exception.", ex);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ErrorResponse.builder()
                        .reasonCode(ReasonCode.VALIDATION_ERROR)
                        .description(ReasonCode.VALIDATION_ERROR.getDescription())
                        .internalCode(ReasonCode.VALIDATION_ERROR.getCode())
                        .message(ex.getBindingResult().getFieldErrors().stream()
                                .map(e -> "field [" + e.getField() + "] " + e.getDefaultMessage())
                                .collect(Collectors.joining("\n")))
                        .build());
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponse<?>> handleAccessDeniedException(final AccessDeniedException ex) {
        log.error("Access denied exception: ", ex);
        return ResponseEntity.status(HttpStatus.I_AM_A_TEAPOT)
                .body(ErrorResponse.builder()
                        .reasonCode(ReasonCode.ACCESS_DENIED)
                        .description(ReasonCode.ACCESS_DENIED.getDescription())
                        .internalCode(ReasonCode.ACCESS_DENIED.getCode())
                        .message("Access is denied")
                        .build());
    }
}
