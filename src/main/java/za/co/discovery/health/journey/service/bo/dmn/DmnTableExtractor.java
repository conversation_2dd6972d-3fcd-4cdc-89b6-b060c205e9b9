package za.co.discovery.health.journey.service.bo.dmn;

import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Input;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.Output;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.model.bo.dmn.DmnTableRow;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@SuppressWarnings("PMD")
@RequiredArgsConstructor
public class DmnTableExtractor {

    private final RuleCacheService cacheService;

    public List<DmnTableRow> extractTableRows(final String ruleName, final String decisionKey) {
        try {
            final RuleCache ruleCache = cacheService.getRuleCache(ruleName);
            final InputStream inputStream =
                    new ByteArrayInputStream(ruleCache.getRuleContent().getBytes(StandardCharsets.UTF_8));
            final DmnModelInstance modelInstance = Dmn.readModelFromStream(inputStream);

            // Find the decision by key
            final Decision decision = modelInstance.getModelElementsByType(Decision.class).stream()
                    .filter(d -> decisionKey.equals(d.getId()))
                    .findFirst()
                    .orElseThrow(
                            () -> new CoreException(ReasonCode.VALIDATION_ERROR, "Decision not found: " + decisionKey));

            final DecisionTable decisionTable = decision.getChildElementsByType(DecisionTable.class).stream()
                    .findFirst()
                    .orElseThrow(() -> new CoreException(
                            ReasonCode.VALIDATION_ERROR, "No decision table found in decision: " + decisionKey));

            return extractRowsFromDecisionTable(decisionTable);

        } catch (Exception e) {
            throw new CoreException(
                    ReasonCode.VALIDATION_ERROR, "Error extracting DMN table rows: " + e.getMessage(), e);
        }
    }

    private List<DmnTableRow> extractRowsFromDecisionTable(final DecisionTable decisionTable) {
        final List<DmnTableRow> rows = new ArrayList<>();

        // Get column headers
        final List<String> inputHeaders =
                decisionTable.getInputs().stream().map(Input::getId).collect(Collectors.toList());

        final List<String> outputHeaders =
                decisionTable.getOutputs().stream().map(Output::getId).collect(Collectors.toList());

        // Extract each rule as a row
        for (final Rule rule : decisionTable.getRules()) {
            final DmnTableRow row = new DmnTableRow();
            row.setRuleId(rule.getId());

            final Map<String, String> inputValues = new HashMap<>();
            final Map<String, String> outputValues = new HashMap<>();

            // Extract input entries
            final List<InputEntry> inputEntries = new ArrayList<>(rule.getInputEntries());
            for (int i = 0; i < inputEntries.size() && i < inputHeaders.size(); i++) {
                final String header = inputHeaders.get(i);
                final String value = inputEntries.get(i).getText().getRawTextContent();
                inputValues.put(header, value);
            }

            // Extract output entries
            final List<OutputEntry> outputEntries = new ArrayList<>(rule.getOutputEntries());
            for (int i = 0; i < outputEntries.size() && i < outputHeaders.size(); i++) {
                final String header = outputHeaders.get(i);
                final String value =
                        outputEntries.get(i).getText().getRawTextContent().replace("\"", "");
                outputValues.put(header, value);
            }

            row.setInputs(inputValues);
            row.setOutputs(outputValues);
            rows.add(row);
        }

        return rows;
    }
}
