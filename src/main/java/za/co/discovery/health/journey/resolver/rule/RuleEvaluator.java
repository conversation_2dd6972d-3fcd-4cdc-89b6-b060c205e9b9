package za.co.discovery.health.journey.resolver.rule;

import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.RuleEvaluationResult;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.util.model.Audience;

import java.util.Map;

public interface RuleEvaluator {
    // In RuleEvaluator.java (or each implementing class)
    RuleType getHandledRuleType();

    RuleProcessorType getHandledProcessorType();

    RuleEvaluationResult evaluate(Long entityId, RuleCache cache, Map<String, Object> vars);

    default RuleEvaluationResult evaluate(
            final Audience audience, final RuleCache cache, final Map<String, Object> vars) {
        return evaluate(audience.getEntityId(), cache, vars);
    }
}
