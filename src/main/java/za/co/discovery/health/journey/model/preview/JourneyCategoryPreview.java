package za.co.discovery.health.journey.model.preview;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class JourneyCategoryPreview extends LimitedJourneyCategoryPreview {
    private List<JourneyProgramPreview> programs;

    public JourneyCategoryPreview(final LimitedJourneyCategoryPreviewBuilder<?, ?> builder) {
        super(builder);
    }
}
