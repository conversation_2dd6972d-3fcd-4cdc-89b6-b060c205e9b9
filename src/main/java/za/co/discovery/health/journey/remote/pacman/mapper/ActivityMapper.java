package za.co.discovery.health.journey.remote.pacman.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import za.co.discovery.health.journey.config.MapStructConfig;
import za.co.discovery.health.journey.remote.pacman.model.ProgramEnrolmentCohortActivityDTOPageable;
import za.co.discovery.health.journey.remote.pacman.model.ProgramEnrolmentCohortActivityDTOResponse;
import za.co.discovery.health.pacman.domain.PageProgramEnrolmentCohortActivityDTO;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

@Mapper(config = MapStructConfig.class)
public interface ActivityMapper {

    ProgramEnrolmentCohortActivityDTOPageable map(PageProgramEnrolmentCohortActivityDTO from);

    @Mapping(source = "effForm", target = "effFrom")
    ProgramEnrolmentCohortActivityDTOResponse map(ProgramEnrolmentCohortActivityDTO from);
}
