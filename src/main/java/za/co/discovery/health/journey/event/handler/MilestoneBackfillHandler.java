package za.co.discovery.health.journey.event.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.MilestoneExpiredEvent;
import za.co.discovery.health.journey.event.NextMilestoneNeededEvent;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentMilestoneService;
import za.co.discovery.health.journey.service.journey.JourneyProgramService;
import za.co.discovery.health.journey.strategy.JourneyTypeStrategy;
import za.co.discovery.health.journey.strategy.JourneyTypeStrategyRegistry;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class MilestoneBackfillHandler {
    private final JourneyEnrollmentMilestoneService milestoneService;
    private final JourneyProgramService programService;
    private final JourneyEventPublisher eventPublisher;
    private final JourneyTypeStrategyRegistry strategyRegistry;

    @EventListener
    @Transactional
    public void handleMilestoneExpired(final MilestoneExpiredEvent event) {
        log.info(
                "Handling milestone expired event for enrollment: {}",
                event.getEnrollment().getJourneyEnrollmentId());
        final JourneyEnrollmentMilestone expiredMilestone = event.getMilestone();
        final JourneyEnrollment enrollment = event.getEnrollment();

        // Mark current as not achieved
        if (milestoneService.isSkipMilestone(expiredMilestone)) {
            milestoneService.markAsSkipped(expiredMilestone);
        } else {
            milestoneService.markAsNotAchieved(expiredMilestone);
        }

        // Get strategy to check if backfill is enabled
        final JourneyTypeStrategy strategy = strategyRegistry.getStrategy(enrollment.getJourneyCategory());

        if (!strategy.getMilestoneManager().shouldBackfillMissedMilestones()) {
            // Just create next milestone
            eventPublisher.publish(new NextMilestoneNeededEvent(
                    enrollment, expiredMilestone.getMilestoneIteration() + 1, event.getRequestedAt()));
            return;
        }

        // Calculate and create all missed milestones
        final List<MissedPeriod> missedPeriods =
                calculateMissedPeriods(expiredMilestone, enrollment, event.getRequestedAt());

        long lastIteration = expiredMilestone.getMilestoneIteration();

        for (final MissedPeriod period : missedPeriods) {
            createNotAchievedMilestone(enrollment, period);
            lastIteration = period.iteration;
        }

        // Create current active milestone
        final LocalDateTime currentStart = missedPeriods.isEmpty()
                ? expiredMilestone.getMilestoneTo()
                : missedPeriods.get(missedPeriods.size() - 1).endDate;

        eventPublisher.publish(new NextMilestoneNeededEvent(enrollment, lastIteration + 1, currentStart));
    }

    private List<MissedPeriod> calculateMissedPeriods(
            final JourneyEnrollmentMilestone lastMilestone,
            final JourneyEnrollment enrollment,
            final LocalDateTime now) {

        final List<MissedPeriod> missed = new ArrayList<>();
        final ChronoUnit unit = milestoneService.getMilestoneUnit(enrollment);
        final long programDuration =
                enrollment.getJourneyProgram().getJourneyProgramBehaviour().getProgramDuration();

        LocalDateTime periodStart = lastMilestone.getMilestoneTo();
        long iteration = lastMilestone.getMilestoneIteration() + 1;

        while (periodStart != null && periodStart.isBefore(now) && programDuration > iteration) {
            final LocalDateTime periodEnd = periodStart.plus(1, unit);

            if (periodEnd.isBefore(now)) {
                missed.add(new MissedPeriod(iteration, periodStart, periodEnd));
                periodStart = periodEnd;
                iteration++;
            } else {
                break;
            }
        }

        return missed;
    }

    private void createNotAchievedMilestone(final JourneyEnrollment enrollment, final MissedPeriod period) {
        final var activities = programService.getMilestoneActivities(
                enrollment.getJourneyProgram().getJourneyProgramId(), enrollment.getEntityId(), period.iteration);

        if (activities.isSkipPeriod()) {
            milestoneService.createSkipMilestone(
                    enrollment, period.startDate, period.iteration, MilestoneStatus.SKIPPED.getValue());
        } else if (!(activities.getActivities().isEmpty())) {
            milestoneService.createMilestone(
                    enrollment,
                    activities.getActivities(),
                    period.startDate,
                    period.endDate,
                    period.iteration,
                    MilestoneStatus.NOT_ACHIEVED.getValue());
        }
    }

    private static class MissedPeriod {
        final long iteration;
        final LocalDateTime startDate;
        final LocalDateTime endDate;

        MissedPeriod(final long iteration, final LocalDateTime startDate, final LocalDateTime endDate) {
            this.iteration = iteration;
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }
}
