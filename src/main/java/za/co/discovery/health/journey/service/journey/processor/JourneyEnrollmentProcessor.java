package za.co.discovery.health.journey.service.journey.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.event.EnrollmentCreatedEvent;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.MilestoneCompletedEvent;
import za.co.discovery.health.journey.event.MilestoneExpiredEvent;
import za.co.discovery.health.journey.event.NextMilestoneNeededEvent;
import za.co.discovery.health.journey.event.ProgramCompletedEvent;
import za.co.discovery.health.journey.model.enums.EnrollmentStatus;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.resolver.rule.model.result.JourneyActivationRuleResult;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentMilestoneService;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentService;
import za.co.discovery.health.journey.service.journey.JourneyProgramService;
import za.co.discovery.health.journey.service.journey.JourneyService;
import za.co.discovery.health.journey.strategy.JourneyTypeStrategy;
import za.co.discovery.health.journey.strategy.JourneyTypeStrategyRegistry;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("PMD.ExcessiveImports")
public class JourneyEnrollmentProcessor {
    private final JourneyTypeStrategyRegistry strategyRegistry;
    private final JourneyCategoryService categoryService;
    private final JourneyEnrollmentService enrollmentService;
    private final JourneyEnrollmentMilestoneService milestoneService;
    private final JourneyProgramService programService;
    private final JourneyService journeyService;
    private final JourneyEventPublisher eventPublisher;

    @Transactional
    public void process(
            final Long userId,
            final JourneyActivationRuleResult.CategoryRecommendation category,
            final LocalDateTime now) {
        validateInputs(userId, category, now);

        final JourneyCategory targetCategory = categoryService.findByCode(category.getCategory());
        final List<JourneyEnrollment> enrollments =
                enrollmentService.findEnrollments(userId, targetCategory.getJourneyCategoryId());

        final JourneyTypeStrategy strategy = strategyRegistry.getStrategy(targetCategory);

        if (enrollments.isEmpty()) {
            processNewUserEnrollment(userId, targetCategory, strategy, now);
        } else {
            processExistingUserEnrollment(enrollments, strategy, now);
        }
    }

    private void processNewUserEnrollment(
            final Long userId,
            final JourneyCategory category,
            final JourneyTypeStrategy strategy,
            final LocalDateTime now) {
        final CategoryConfigurationRuleResult.CategoryConfiguration config =
                journeyService.getCategoryConfiguration(userId, category);

        if (!strategy.getEnrollmentValidator().canEnroll(userId, category, config, now)) {
            log.info("Journey is invalid for user {} in category {}", userId, category.getCategoryCode());
            return;
        }

        final LocalDateTime journeyStart = strategy.getEnrollmentValidator().calculateJourneyStartTime(config, now);

        // Create enrollment
        final JourneyProgram firstProgram = programService.getFirstProgram(category, now);
        final JourneyEnrollment enrollment = enrollmentService.createEnrollment(userId, firstProgram, category, now);

        // Publish event - listeners will handle milestone creation
        eventPublisher.publish(new EnrollmentCreatedEvent(enrollment, journeyStart));
    }

    private void processExistingUserEnrollment(
            final List<JourneyEnrollment> enrollments, final JourneyTypeStrategy strategy, final LocalDateTime now) {
        final JourneyEnrollment latestEnrollment = findLatestEnrollment(enrollments);

        if (isEnrollmentCompleted(latestEnrollment)) {
            eventPublisher.publish(new ProgramCompletedEvent(latestEnrollment, now));
            return;
        }

        processActiveMilestone(latestEnrollment, strategy, now);
    }

    @SuppressWarnings("PMD.ConfusingTernary")
    private void processActiveMilestone(
            final JourneyEnrollment enrollment, final JourneyTypeStrategy strategy, final LocalDateTime now) {
        final Optional<JourneyEnrollmentMilestone> latestMilestone = findLatestMilestone(enrollment);

        if (latestMilestone.isEmpty()) {
            // Need to create initial milestone
            final CategoryConfigurationRuleResult.CategoryConfiguration config =
                    journeyService.getCategoryConfiguration(enrollment.getEntityId(), enrollment.getJourneyCategory());
            final LocalDateTime journeyStart = strategy.getEnrollmentValidator().calculateJourneyStartTime(config, now);

            eventPublisher.publish(new NextMilestoneNeededEvent(enrollment, 1L, journeyStart));
            return;
        }

        final JourneyEnrollmentMilestone milestone = latestMilestone.get();

        if (!isMilestoneActive(milestone)) {
            // Non-active milestone needs next milestone
            final LocalDateTime nextStart = milestone.getMilestoneTo() != null ? milestone.getMilestoneTo() : now;
            eventPublisher.publish(
                    new NextMilestoneNeededEvent(enrollment, milestone.getMilestoneIteration() + 1, nextStart));
            return;
        }

        if (strategy.getMilestoneManager().isMilestoneExpired(milestone, now)) {
            eventPublisher.publish(new MilestoneExpiredEvent(milestone, enrollment, now));
            return;
        }

        if (milestoneService.isMilestoneComplete(milestone.getJourneyEnrollmentMilestoneId())) {
            eventPublisher.publish(new MilestoneCompletedEvent(milestone, enrollment, now));
        }
    }

    // Helper methods remain the same
    private JourneyEnrollment findLatestEnrollment(final List<JourneyEnrollment> enrollments) {
        return enrollments.stream()
                .max(Comparator.comparing(JourneyEnrollment::getEnrollmentDate))
                .orElseThrow(() -> new CoreException(ReasonCode.VALIDATION_ERROR, "No enrollments found"));
    }

    private Optional<JourneyEnrollmentMilestone> findLatestMilestone(final JourneyEnrollment enrollment) {
        return enrollment.getJourneyEnrollmentMilestones().stream()
                .max(Comparator.comparing(JourneyEnrollmentMilestone::getMilestoneTo));
    }

    private boolean isEnrollmentCompleted(final JourneyEnrollment enrollment) {
        return EnrollmentStatus.COMPLETED.getValue().equals(enrollment.getStatus());
    }

    private boolean isMilestoneActive(final JourneyEnrollmentMilestone milestone) {
        return MilestoneStatus.ACTIVE.getValue().equals(milestone.getMilestoneStatus())
                || MilestoneStatus.SKIP.getValue().equals(milestone.getMilestoneStatus());
    }

    private void validateInputs(
            final Long userId,
            final JourneyActivationRuleResult.CategoryRecommendation category,
            final LocalDateTime now) {
        Assert.notNull(userId, "User ID cannot be null");
        Assert.notNull(category, "Category recommendation cannot be null");
        Assert.notNull(now, "Current time cannot be null");
    }
}
