package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import za.co.discovery.health.group.coaching.domain.AppointmentResponseDto;
import za.co.discovery.health.journey.config.client.AppointmentApiClient;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.constant.JourneyCommunicationConstant;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategorization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.database.projection.RewardProjection;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategoryRepository;
import za.co.discovery.health.journey.model.bo.ActivityDetails;
import za.co.discovery.health.journey.model.bo.dmn.ActivityRecommendationDmnRuleDto;
import za.co.discovery.health.journey.model.communication.JourneyCategoryCommunicationDataPoint;
import za.co.discovery.health.journey.model.communication.JourneyProgramCommunicationDataPoint;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.service.bo.BoJourneyService;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyProgramRewardFacade;
import za.co.discovery.health.journey.util.JourneyConfigExtractionUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("PMD.ExcessiveImports")
public class JourneyCommunicationService {
    private final ExtendedJourneyCategoryRepository journeyCategoryRepository;
    private final JourneyService journeyService;
    private final BoJourneyService boJourneyService;
    private final AppointmentApiClient appointmentApiClient;
    private final JourneyProgramRewardFacade rewardFacade;

    @Transactional(readOnly = true)
    public Mono<JourneyCategoryCommunicationDataPoint> createCommunicationDataPointsReactive(
            final Long entityId, final Long journeyCategoryId) {

        final JourneyCategory category = findJourneyCategoryById(journeyCategoryId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("JourneyCategory with ID = %d could not be found", journeyCategoryId)));

        final CategoryConfigurationRuleResult.CategoryConfiguration configuration =
                getCategoryConfiguration(entityId, category);

        final LocalDateTime journeyStartTime = configuration.getJourneyStartTime();

        final Map<JourneyCommunicationConstant, String> categoryAttributes =
                prepareCategoryAttributes(category, journeyStartTime);

        final JourneyCategoryCommunicationDataPoint dataPoint = JourneyCategoryCommunicationDataPoint.builder()
                .categoryId(category.getJourneyCategoryId())
                .journeyCategoryType(category.getJourneyCategoryType().getName())
                .attributes(categoryAttributes)
                .build();

        final List<JourneyProgram> activePrograms = getActivePrograms(category);

        return mapProgramIdsToAppointmentDaysReactive(entityId, activePrograms)
                .flatMap(programIdDayMap -> Flux.fromIterable(activePrograms)
                        .flatMap(program -> Mono.fromCallable(() ->
                                        prepareProgramAttributes(entityId, program, journeyStartTime, programIdDayMap))
                                .subscribeOn(Schedulers.boundedElastic())
                                .map(programAttributes -> JourneyProgramCommunicationDataPoint.builder()
                                        .journeyProgramId(program.getJourneyProgramId())
                                        .attributes(programAttributes)
                                        .build()))
                        .collectList()
                        .map(programDataPoints -> {
                            dataPoint.getProgramCommunicationDataPoints().addAll(programDataPoints);
                            return dataPoint;
                        }));
    }

    private Optional<JourneyCategory> findJourneyCategoryById(final Long categoryId) {
        return journeyCategoryRepository.findJourneyCategoryById(categoryId);
    }

    private CategoryConfigurationRuleResult.CategoryConfiguration getCategoryConfiguration(
            final Long entityId, final JourneyCategory journeyCategory) {
        return journeyService.getCategoryConfiguration(entityId, journeyCategory);
    }

    private Optional<ActivityDetails> getArbitraryAppointment(
            final Long entityId, final JourneyProgram journeyProgram) {
        return boJourneyService
                .getProgramActivities(
                        journeyProgram, entityId, ActivityRecommendationDmnRuleDto.ActivityType.APPOINTMENT)
                .stream()
                .findAny();
    }

    private String getDayOfWeek(final OffsetDateTime offsetDateTime) {
        if (offsetDateTime == null) {
            throw new IllegalArgumentException("OffsetDateTime cannot be null");
        }
        return offsetDateTime.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.ENGLISH);
    }

    public Mono<Map<String, AppointmentResponseDto>> batchFetchAppointments(
            final List<ActivityDetails> activityDetails) {
        final Set<Long> appointmentIds = activityDetails.stream()
                .filter(activityDetail -> ActivityRecommendationDmnRuleDto.ActivityType.APPOINTMENT.equals(
                        activityDetail.getActivityType()))
                .filter(activity -> activity.getMnemonic() != null)
                .map(activity -> {
                    try {
                        return Long.parseLong(activity.getMnemonic());
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (appointmentIds.isEmpty()) {
            return Mono.just(Collections.emptyMap());
        }

        return appointmentApiClient
                .getAppointmentDetails(appointmentIds)
                .collectMap(appointment -> String.valueOf(appointment.getId()), appointment -> appointment);
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    private String getMustAttendSessionNumber(final JourneyProgram program) {
        try {
            final JourneyRules completionRules =
                    program.getJourneyProgramBehaviour().getJourneyRulesByProgramCompletionRulesId();

            if (completionRules == null || completionRules.getRuleSet() == null) {
                log.warn("No completion rules found for program {}", program.getJourneyProgramId());
                return "0";
            }

            final String ruleExpression = new String(completionRules.getRuleSet(), StandardCharsets.UTF_8);
            return JourneyConfigExtractionUtils.extractAppointmentCountFromRule(ruleExpression);
        } catch (Exception e) {
            log.warn("Failed to get min number of sessions to attend for program {}", program.getJourneyProgramId(), e);
            return "0";
        }
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    private String getTotalNumberOfSessions(final Long entityId, final JourneyProgram program) {
        try {
            final List<ActivityDetails> appointments = boJourneyService.getProgramActivities(
                    program, entityId, ActivityRecommendationDmnRuleDto.ActivityType.APPOINTMENT);
            return String.valueOf(appointments.size());
        } catch (Exception e) {
            log.warn("Failed to get total number of sessions for program {}", program.getJourneyProgramId(), e);
            return "0";
        }
    }

    private Map<Long, ActivityDetails> getProgramIdAppointmentMap(
            final Long entityId, final List<JourneyProgram> activePrograms) {

        return activePrograms.stream()
                .collect(Collectors.toMap(JourneyProgram::getJourneyProgramId, program -> getArbitraryAppointment(
                                entityId, program)
                        .orElseThrow(() -> new IllegalArgumentException(
                                "No appointments found for journey program ID = " + program.getJourneyProgramId()))));
    }

    private List<JourneyProgram> getActivePrograms(final JourneyCategory category) {
        final String activeStatus = "ACTIVE";

        return category.getJourneyCategorizations().stream()
                .filter(categorization -> activeStatus.equals(categorization.getStatus()))
                .map(JourneyCategorization::getJourneyProgram)
                .collect(Collectors.toList());
    }

    public Mono<Map<Long, String>> mapProgramIdsToAppointmentDaysReactive(
            final Long entityId, final List<JourneyProgram> activePrograms) {

        final SecurityContext context = SecurityContextHolder.getContext();

        return Mono.fromCallable(() -> getProgramIdAppointmentMap(entityId, activePrograms))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(programIdAppointmentMap -> {
                    SecurityContextHolder.setContext(context);

                    final List<ActivityDetails> appointments = new ArrayList<>(programIdAppointmentMap.values());
                    return batchFetchAppointments(appointments)
                            .map(mnemonicAppointmentMap -> programIdAppointmentMap.entrySet().stream()
                                    .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                                        if (mnemonicAppointmentMap != null) {
                                            final OffsetDateTime startTime = mnemonicAppointmentMap
                                                    .get(e.getValue().getMnemonic())
                                                    .getStartTime();
                                            return getDayOfWeek(startTime);
                                        }
                                        throw new IllegalArgumentException(String.format(
                                                "Could not extract appointment for mnemonic %s",
                                                e.getValue().getMnemonic()));
                                    })));
                });
    }

    private Map<JourneyCommunicationConstant, String> prepareCategoryAttributes(
            final JourneyCategory category, final LocalDateTime journeyStartTime) {
        final Map<JourneyCommunicationConstant, String> categoryAttributes = new HashMap<>();
        categoryAttributes.put(JourneyCommunicationConstant.TOPIC_NAME, category.getName());
        categoryAttributes.put(JourneyCommunicationConstant.START_DATE, journeyStartTime.toString());
        categoryAttributes.put(
                JourneyCommunicationConstant.JOURNEY_EXTERNAL_REFERENCE_ID, category.getExternalReference());
        return categoryAttributes;
    }

    private Map<JourneyCommunicationConstant, String> prepareProgramAttributes(
            final Long entityId,
            final JourneyProgram program,
            final LocalDateTime journeyStartTime,
            final Map<Long, String> programIdAppointmentDayMap) {

        final Optional<RewardProjection> reward =
                rewardFacade.getProgramRewards(entityId, program.getJourneyProgramId(), journeyStartTime).stream()
                        .findFirst();

        final String rewardType = reward.map(RewardProjection::getRewardType)
                .orElseThrow(() -> new CoreException(
                        ReasonCode.GENERAL_ERROR,
                        String.format(
                                "No rewards have been found for journey program ID = %d",
                                program.getJourneyProgramId())));

        final String rewardValue = reward.map(RewardProjection::getRewardValue)
                .orElseThrow(() -> new CoreException(
                        ReasonCode.GENERAL_ERROR,
                        String.format(
                                "No rewards have been found for journey program ID = %d",
                                program.getJourneyProgramId())));

        final Map<JourneyCommunicationConstant, String> programAttributes = new HashMap<>();
        programAttributes.put(
                JourneyCommunicationConstant.END_DATE,
                JourneyConfigExtractionUtils.getEndDateForProgram(program, journeyStartTime)
                        .toString());
        programAttributes.put(
                JourneyCommunicationConstant.DAY_OF_WEEK,
                programIdAppointmentDayMap.get(program.getJourneyProgramId()));
        programAttributes.put(JourneyCommunicationConstant.ATTEND_SESSION, getMustAttendSessionNumber(program));
        programAttributes.put(JourneyCommunicationConstant.MAX_SESSION, getTotalNumberOfSessions(entityId, program));
        programAttributes.put(JourneyCommunicationConstant.REWARD_TYPE, rewardType);
        programAttributes.put(JourneyCommunicationConstant.REWARD_VALUE, rewardValue);
        programAttributes.put(JourneyCommunicationConstant.PROGRAM_SENTENCE_DESCRIPTION, program.getDescription());
        return programAttributes;
    }
}
