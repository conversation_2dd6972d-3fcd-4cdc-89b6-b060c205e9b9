package za.co.discovery.health.journey.model.bo.dmn;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRecommendationDmnRuleDto {
    // Rule identification
    private String ruleId;
    private String description;

    // Input fields
    private String alliance;
    private String branch;
    private String group;
    private Integer milestone;

    // Output fields
    private String activityName;
    private String activityIcon;
    private CompletionFlexibility completionFlexibility;
    private String activity;
    private Integer frequency;
    private ActivityType activityType;

    // Enums for type safety
    public enum CompletionFlexibility {
        MANDATORY("Mandatory"),
        OPTIONAL("Optional"),
        FLEXIBLE("Flexible");

        private final String value;

        CompletionFlexibility(final String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @JsonCreator
        public static CompletionFlexibility fromValue(final String value) {
            for (final CompletionFlexibility flexibility : CompletionFlexibility.values()) {
                if (flexibility.value.equalsIgnoreCase(value)) {
                    return flexibility;
                }
            }
            return MANDATORY; // Default fallback
        }
    }

    public enum ActivityType {
        APPOINTMENT("APPOINTMENT"),
        ACTIVITY("ACTIVITY");

        private final String value;

        ActivityType(final String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @JsonCreator
        public static ActivityType fromValue(final String value) {
            for (final ActivityType type : ActivityType.values()) {
                if (type.value.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            return ACTIVITY; // Default fallback
        }

        public static ActivityType fromActivityType(final ActivityDetailsType activityType) {
            if (activityType == null) {
                return ActivityType.ACTIVITY;
            }
            switch (activityType) {
                case APPOINTMENT:
                    return ActivityType.APPOINTMENT;
                case ACTIVITY:
                    return ActivityType.ACTIVITY;
                default:
                    return ActivityType.ACTIVITY; // Default fallback
            }
        }
    }

    public boolean isMandatory() {
        return CompletionFlexibility.MANDATORY.equals(completionFlexibility);
    }
}
