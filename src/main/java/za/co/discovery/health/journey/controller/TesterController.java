package za.co.discovery.health.journey.controller;

import lombok.RequiredArgsConstructor;
import org.camunda.bpm.dmn.engine.DmnDecision;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import za.co.discovery.health.journey.model.CompleteActivityRequest;
import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
import za.co.discovery.health.journey.resolver.rule.RuleEvaluator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.RuleEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.result.JourneyActivationRuleResult;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.service.journey.JourneyActivityService;
import za.co.discovery.health.journey.service.journey.JourneyProcessor;
import za.co.discovery.health.journey.service.journey.JourneyService;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@SuppressWarnings("PMD.ExcessiveImports")
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TesterController {
    private final RuleEvaluatorResolver service;
    private final JourneyService journeyService;
    private final JourneyProcessor journeyProcessor;
    private final DmnEngine dmnEngine;
    private final JourneyActivityService activityService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public RuleEvaluationResult evaluate(
            @RequestPart final MultipartFile multipartFile,
            @RequestParam final String decisionKey,
            @RequestParam final Map<String, Object> vars,
            @RequestParam(required = false) final Long entityId)
            throws IOException {
        try (InputStream inputStream = new ByteArrayInputStream(multipartFile.getBytes())) {

            final DmnDecision decision = dmnEngine.parseDecision(decisionKey, inputStream);
            final RuleEvaluator evaluator =
                    service.getEvaluator(RuleType.ACTIVITY_RECOMMENDATION_RULE, RuleProcessorType.DMN);

            return evaluator.evaluate(entityId, new RuleCache(RuleProcessorType.DMN, "", decision), vars);
        }
    }

    @GetMapping(value = "/v2/journey")
    public List<JourneyActivationRuleResult.CategoryRecommendation> v2Journey(@RequestParam final Long entityId) {

        return journeyService.getRecommendedCategories(entityId);
    }

    @GetMapping(value = "/enrollments", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<UserCategoryEnrollmentDto> enrollmentDtos(
            @RequestParam final Long entityId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {

        return journeyProcessor.getUserEnrollments(
                entityId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
    }

    @PostMapping(value = "/complete-activities", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void activities(@RequestBody final CompleteActivityRequest request) throws IOException {

        activityService.processActivityCompletion(
                Objects.requireNonNullElse(request.getEntityId(), 1L),
                request.getMnemonic(),
                request.getActivityTnxId(),
                request.getCompletedAt(),
                request.getValue(),
                request.getActivityDetailsType());
    }
}
