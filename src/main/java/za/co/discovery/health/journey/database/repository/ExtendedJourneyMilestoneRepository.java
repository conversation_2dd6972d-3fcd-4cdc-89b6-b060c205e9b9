package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyMilestone;

import java.util.Optional;

@Repository
public interface ExtendedJourneyMilestoneRepository
        extends JourneyMilestoneRepository, JpaRepository<JourneyMilestone, Long> {
    @Query("SELECT mil FROM JourneyMilestone mil " + "WHERE mil.description = ?1 ")
    Optional<JourneyMilestone> findByName(String type);
}
