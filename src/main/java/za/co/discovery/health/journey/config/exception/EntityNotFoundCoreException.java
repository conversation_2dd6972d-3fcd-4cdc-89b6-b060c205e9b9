package za.co.discovery.health.journey.config.exception;

import lombok.Getter;

@Getter
public class EntityNotFoundCoreException extends CoreException {
    private static final Long serialVersionUID = 10010101203L;

    public EntityNotFoundCoreException(final ReasonCode reasonCode) {
        super(reasonCode);
    }

    public EntityNotFoundCoreException(final ReasonCode reasonCode, final String message) {
        super(reasonCode, message);
    }

    public EntityNotFoundCoreException(final ReasonCode reasonCode, final Exception ex) {
        super(reasonCode, ex);
    }

    public EntityNotFoundCoreException(final ReasonCode reasonCode, final String message, final Exception ex) {
        super(reasonCode, message, ex);
    }
}
