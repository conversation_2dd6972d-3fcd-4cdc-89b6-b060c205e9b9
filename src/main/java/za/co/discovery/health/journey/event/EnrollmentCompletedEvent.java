package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;

@Getter
public class EnrollmentCompletedEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;

    public EnrollmentCompletedEvent(final JourneyEnrollment enrollment) {
        super(enrollment.getEntityId(), LocalDateTime.now());
        this.enrollment = enrollment;
    }
}
