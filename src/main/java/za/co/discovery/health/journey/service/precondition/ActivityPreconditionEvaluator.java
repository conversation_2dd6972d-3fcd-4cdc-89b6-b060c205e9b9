package za.co.discovery.health.journey.service.precondition;

import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;

import java.time.LocalDateTime;
import java.util.List;

public interface ActivityPreconditionEvaluator {
    List<PreconditionEvaluationResult> evaluateAllPreconditions(
            Long entityId,
            Long iteration,
            String activityMnemonic,
            ActivityDetailsType activityType,
            LocalDateTime time,
            JourneyProgramBehaviour behaviour);
}
