package za.co.discovery.health.journey.config.converter;

import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
public class StringToZonedDateTimeConverter implements Converter<String, ZonedDateTime> {

    @Override
    public ZonedDateTime convert(final String source) {
        if (source.isBlank()) {
            return null;
        }
        return ZonedDateTime.parse(source, DateTimeFormatter.ISO_ZONED_DATE_TIME);
    }
}
