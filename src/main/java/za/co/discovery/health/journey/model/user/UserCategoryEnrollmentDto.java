package za.co.discovery.health.journey.model.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class UserCategoryEnrollmentDto extends LimitedUserCategoryEnrollmentDto {
    private List<UserProgramEnrollmentDto> journeyPrograms; // programs which user will so through ordered by order
}
