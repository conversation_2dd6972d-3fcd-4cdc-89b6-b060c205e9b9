package za.co.discovery.health.journey.database.repository;

import org.hibernate.cfg.AvailableSettings;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentProgramAward;

import javax.persistence.LockModeType;
import javax.persistence.QueryHint;

import java.util.List;

@Repository
public interface ExtendedJourneyEnrollmentProgramAwardRepository extends JourneyEnrollmentProgramAwardRepository {
    @Query("SELECT a FROM JourneyEnrollmentProgramAward a WHERE a.awardStatus = :status")
    @QueryHints(@QueryHint(name = AvailableSettings.JPA_LOCK_TIMEOUT, value = "-2")) // SKIP_LOCKED
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<JourneyEnrollmentProgramAward> findProgramAwardsByAwardStatus(String status);
}
