package za.co.discovery.health.journey.model.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.enums.UserJourneyState;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class LimitedUserCategoryEnrollmentDto {
    private Long categoryId;

    private UserJourneyState state;

    private String categoryName;

    private Long journeyDuration;

    private ChronoUnit journeyDurationUnit;

    private String categoryType;

    private String externalReference;

    private LocalDateTime startTime;

    private LocalDateTime enrollmentTime;

    private CurrentUserMilestoneEnrollmentDto currentMilestone;

    private LocalDateTime monitoringPeriodEndTime;
}
