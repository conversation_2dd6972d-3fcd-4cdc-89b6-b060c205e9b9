package za.co.discovery.health.journey.event.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.event.ActivityCompletedEvent;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.MilestoneCompletedEvent;
import za.co.discovery.health.journey.event.MilestoneLateCompletedEvent;
import za.co.discovery.health.journey.model.enums.ActivityStatus;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentMilestoneService;

@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityCompletedHandler {

    private final JourneyEventPublisher eventPublisher;

    private final JourneyEnrollmentMilestoneService journeyEnrollmentMilestoneService;

    @EventListener
    @Transactional
    public void handleActivityCompleted(final ActivityCompletedEvent event) {
        log.info(
                "Handling activity completed event for enrollment: {}",
                event.getEnrollment().getJourneyEnrollmentId());

        final boolean milestoneComplete = journeyEnrollmentMilestoneService.isMilestoneComplete(
                event.getMilestone().getJourneyEnrollmentMilestoneId());

        if (milestoneComplete) {
            final ActivityStatus status = event.getStatus();
            switch (status) {
                case COMPLETED:
                    eventPublisher.publish(new MilestoneCompletedEvent(
                            event.getMilestone(), event.getEnrollment(), event.getCompletedAt()));
                    break;
                case LATE_COMPLETED:
                    eventPublisher.publish(new MilestoneLateCompletedEvent(
                            event.getMilestone(), event.getEnrollment(), event.getCompletedAt()));
                    break;
                default:
                    break;
            }
        }
    }
}
