package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;

@Getter
public class ProgramCompletedEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;
    private final LocalDateTime completionTime;

    public ProgramCompletedEvent(final JourneyEnrollment enrollment, final LocalDateTime completionTime) {
        super(enrollment.getEntityId(), completionTime);
        this.enrollment = enrollment;
        this.completionTime = completionTime;
    }
}
