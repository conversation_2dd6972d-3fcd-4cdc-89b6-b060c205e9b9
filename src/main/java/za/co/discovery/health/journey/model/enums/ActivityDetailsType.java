package za.co.discovery.health.journey.model.enums;

import org.springframework.util.StringUtils;

public enum ActivityDetailsType {
    JOURNEY_DRIVEN_ACTIVITY,
    ACTIVITY,
    APPOINTMENT,
    VIDEO_LINK,
    SKIP;

    public static ActivityDetailsType fromValue(final String type) {
        if (!StringUtils.hasText(type)) {
            return null;
        }
        for (final ActivityDetailsType activityDetailsType : ActivityDetailsType.values()) {
            if (activityDetailsType.name().equalsIgnoreCase(type)) {
                return activityDetailsType;
            }
        }
        throw new IllegalArgumentException("Unknown type: " + type);
    }
}
