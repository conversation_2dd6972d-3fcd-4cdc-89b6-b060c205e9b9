package za.co.discovery.health.journey.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.List;
import java.util.stream.Collectors;

@Configuration
@RequiredArgsConstructor
public class OpenApiConfig {

    @Value("${openapi.servers:}")
    private List<String> serverUrls;

    @Bean
    @Profile("!local")
    public OpenAPI customOpenAPI() {
        return getOpenAPIServer();
    }

    @Bean
    @Profile("local")
    public OpenAPI localOpenAPI() {
        return getOpenAPIServer()
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes(
                                "Bearer Authentication",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .bearerFormat("JWT")
                                        .scheme("bearer")));
    }

    private OpenAPI getOpenAPIServer() {
        final List<Server> serverList = serverUrls.stream()
                .map(url -> {
                    final Server server = new Server();
                    server.setUrl(url);
                    return server;
                })
                .collect(Collectors.toList());
        return new OpenAPI().servers(serverList);
    }
}
