package za.co.discovery.health.journey.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.config.KafkaTopics;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentRepository;
import za.co.discovery.health.journey.messaging.model.AttendeeType;
import za.co.discovery.health.journey.messaging.model.NotificationTemplate;
import za.co.discovery.health.journey.util.Constants;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class MidPointAssessmentReminderScheduler {

    private final ExtendedJourneyEnrollmentRepository journeyEnrollmentRepository;
    private final KafkaTemplate<String, NotificationTemplate> notificationTemplateKafkaTemplate;

    private static final Long MIDPOINT_WEEK = 6L;

    @Scheduled(cron = "${scheduler.mid-point-reminder-cron}", zone = "America/Chicago")
    @SchedulerLock(name = "hs-personal-journey:mid-point-reminder", lockAtLeastFor = "PT1M", lockAtMostFor = "PT5M")
    public void checkMidPointAssessmentReminders() {
        final LocalDateTime now = LocalDateTime.now();
        log.info("Starting week 6 mid-point assessment reminder check at {}", now);

        final List<JourneyEnrollment> groupCoachingEnrollments =
                new ArrayList<>(journeyEnrollmentRepository.findActiveGroupCoachingEnrollments(
                        now, Constants.COACHING_JOURNEY_CATEGORY_TYPE_NAME));

        for (final JourneyEnrollment enrollment : groupCoachingEnrollments) {
            checkWeek6MidPointAssessment(enrollment, now);
        }
    }

    private void checkWeek6MidPointAssessment(final JourneyEnrollment enrollment, final LocalDateTime currentTime) {
        final LocalDateTime journeyStart = getJourneyStartTime(enrollment);
        if (journeyStart == null) {
            log.warn("No journey start time found for enrollment {}", enrollment.getJourneyEnrollmentId());
            return;
        }

        final ChronoUnit durationUnit = ChronoUnit.valueOf(enrollment
                .getJourneyProgram()
                .getJourneyProgramBehaviour()
                .getJourneyMilestone()
                .getDescription()
                .toUpperCase(Locale.US));

        final LocalDateTime week6End = journeyStart.plus(MIDPOINT_WEEK, durationUnit);

        // Check if week 6 ended within last 24 hours
        final LocalDateTime yesterday = currentTime.minusDays(1);
        if (!(week6End.isAfter(yesterday) && week6End.isBefore(currentTime))) {
            return;
        }

        // Find week 6 milestone and check NSBM completion
        final Optional<JourneyEnrollmentMilestone> week6Milestone = enrollment.getJourneyEnrollmentMilestones().stream()
                .filter(milestone -> milestone.getMilestoneIteration() == MIDPOINT_WEEK)
                .findFirst();

        if (week6Milestone.isEmpty()) {
            return;
        }

        final boolean nsbmCompleted = week6Milestone.get().getJourneyEnrollmentMilestoneActivities().stream()
                .filter(activity -> Constants.MIDPOINT_ASSESSMENT_MNEMONIC.equals(activity.getActivityMnemonicId()))
                .anyMatch(activity -> "COMPLETED".equals(activity.getActivityStatus()));

        if (!nsbmCompleted) {
            log.info(
                    "Sending week 6 mid-point assessment reminder for enrollment {}",
                    enrollment.getJourneyEnrollmentId());
            sendWeek6MidPointReminder(enrollment);
        }
    }

    private void sendWeek6MidPointReminder(final JourneyEnrollment enrollment) {
        final Map<String, String> attributes = new HashMap<>();
        attributes.put("CategoryName", enrollment.getJourneyCategory().getName());
        attributes.put("CoachNames", "");
        attributes.put("ActivityName", Constants.MIDPOINT_ASSESSMENT_ACTIVITY_NAME);

        notificationTemplateKafkaTemplate.send(
                KafkaTopics.NOTIFICATION_MANAGER_SEND,
                NotificationTemplate.builder()
                        .attendeeType(AttendeeType.PATIENT)
                        .type(Constants.MIDPOINT_ASSESSMENT_REMINDER)
                        .destinationEntityId(enrollment.getEntityId())
                        .attributes(attributes)
                        .build());
    }

    private LocalDateTime getJourneyStartTime(final JourneyEnrollment enrollment) {
        return enrollment.getJourneyEnrollmentMilestones().stream()
                .min(Comparator.comparing(JourneyEnrollmentMilestone::getMilestoneIteration))
                .map(JourneyEnrollmentMilestone::getMilestoneFrom)
                .orElse(null);
    }
}
