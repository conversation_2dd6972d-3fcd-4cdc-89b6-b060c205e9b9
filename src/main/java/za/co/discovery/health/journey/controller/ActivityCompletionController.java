package za.co.discovery.health.journey.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.facade.ActivityCompletionFacade;
import za.co.discovery.health.journey.messaging.model.ActivityCompletedEvent;
import za.co.discovery.health.journey.messaging.model.MemberAppointmentReconciliationEvent;
import za.co.discovery.health.journey.messaging.model.PatientAppointmentEvent;
import za.co.discovery.health.journey.model.request.CompleteMilestoneActivity;

import javax.validation.Valid;

@Slf4j
@Validated
@RestController
@RequestMapping("/api/activities")
@RequiredArgsConstructor
@Tag(name = "Activity Completion", description = "API to manage activity and appointment completion")
public class ActivityCompletionController {

    private final ActivityCompletionFacade activityCompletionFacade;

    @PostMapping("/complete")
    @Operation(
            summary = "Complete an activity",
            description = "Completes an activity based on the provided event details")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Activity completed successfully"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<Void> completeActivity(@Valid @RequestBody final ActivityCompletedEvent event) {
        log.info("Received request to complete activity with ID: {}", event.getActivityTransactionId());
        activityCompletionFacade.completeActivity(event);
        log.info("Successfully completed activity with ID: {}", event.getActivityTransactionId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/appointments/complete")
    @Operation(
            summary = "Complete a patient appointment",
            description = "Completes a patient appointment based on the provided event details")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Appointment completed successfully"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<Void> completeAppointment(@Valid @RequestBody final PatientAppointmentEvent event) {
        log.info("Received request to complete appointment with ID: {}", event.getAppointmentId());
        activityCompletionFacade.completeAppointment(event);
        log.info("Successfully completed appointment with ID: {}", event.getAppointmentId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/appointments/reconcile")
    @Operation(
            summary = "Reconcile a patient appointment",
            description = "Reconcile a patient appointment based on the provided event details")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Appointment reconciled successfully"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<Void> reconcileAppointment(
            @Valid @RequestBody final MemberAppointmentReconciliationEvent event) {
        log.info("Received request to reconcile appointment with ID: {}", event.getAppointmentId());
        activityCompletionFacade.reconcileAppointment(event);
        log.info("Successfully reconciled appointment with ID: {}", event.getAppointmentId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/milestone-activity")
    @Operation(
            summary = "Complete a milestone activity",
            description = "Completes a milestone activity based on the provided event details")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Milestone activity completed successfully"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<Void> completeMilestoneActivity(@Valid @RequestBody final CompleteMilestoneActivity event) {
        activityCompletionFacade.completeMilestoneActivity(event);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/milestone-activity/expired")
    @Operation(
            summary = "Complete expired milestone activity",
            description = "Completes expired a milestone activity based on the provided event details")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Milestone activity completed successfully"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<Void> completeExpiredMilestoneActivity(
            @Valid @RequestBody final CompleteMilestoneActivity event) {
        activityCompletionFacade.completeExpiredActivity(event);
        return ResponseEntity.ok().build();
    }
}
