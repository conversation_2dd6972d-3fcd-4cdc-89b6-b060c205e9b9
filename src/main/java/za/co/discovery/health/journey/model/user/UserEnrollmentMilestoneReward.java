package za.co.discovery.health.journey.model.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class UserEnrollmentMilestoneReward {

    private String rewardType;
    private String rewardValue;
    private LocalDateTime awardedAt;
    private String status;
}
