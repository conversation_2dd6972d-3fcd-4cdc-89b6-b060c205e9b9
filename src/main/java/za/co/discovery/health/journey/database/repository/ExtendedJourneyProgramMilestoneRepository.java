package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramMilestone;

import java.util.Optional;

@Repository
public interface ExtendedJourneyProgramMilestoneRepository extends JourneyProgramMilestoneRepository {

    @Query("SELECT jpm FROM JourneyProgramMilestone jpm "
            + "WHERE jpm.journeyMilestone = ?1 "
            + "AND jpm.milestoneRangeFrom = ?2 "
            + "AND jpm.milestoneRangeTo = ?3")
    Optional<JourneyProgramMilestone> findByJourneyProgramVersionAndMilestoneRange(
            JourneyMilestone milestone, long milestoneFrom, long milestoneTo);
}
