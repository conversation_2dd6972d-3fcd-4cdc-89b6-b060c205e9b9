package za.co.discovery.health.journey.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyRewardAwardingFacade;

@Slf4j
@Component
@RequiredArgsConstructor
public class JourneyRewardAwardingScheduler {
    private final JourneyRewardAwardingFacade journeyRewardAwardingFacade;

    @Scheduled(cron = "${scheduler.journey-reward.cron:*/30 * * * * *}") // Runs every 30 seconds
    @SchedulerLock(name = "hs-personal-journey:journey-program-reward")
    public void processAwardedProgramRewards() {
        log.info("Started Program Rewards processing ...");
        final StopWatch stopWatch = new StopWatch("Resolve Expired Recommendations [Scheduled]");
        stopWatch.start("Scheduled Expiration Processing");

        journeyRewardAwardingFacade.processAwardedProgramRewards();

        stopWatch.stop();
        log.info("Scheduled processing of Program Rewards completed in {} ms", stopWatch.getTotalTimeMillis());
        log.info(stopWatch.prettyPrint());
    }

    @Scheduled(cron = "${scheduler.journey-reward.cron:*/30 * * * * *}") // Runs every 30 seconds
    @SchedulerLock(name = "hs-personal-journey:journey-milestone-reward")
    public void processAwardedMilestoneRewards() {
        log.info("Started Milestone Rewards processing ...");
        final StopWatch stopWatch = new StopWatch("Resolve Expired Recommendations [Scheduled]");
        stopWatch.start("Scheduled Expiration Processing");

        journeyRewardAwardingFacade.processAwardedMilestoneRewards();

        stopWatch.stop();
        log.info("Scheduled processing of Milestone Rewards completed in {} ms", stopWatch.getTotalTimeMillis());
        log.info(stopWatch.prettyPrint());
    }
}
