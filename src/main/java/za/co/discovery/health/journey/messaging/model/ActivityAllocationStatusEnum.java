package za.co.discovery.health.journey.messaging.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ActivityAllocationStatusEnum {
    ALLOCATED("A", "Allocated"),
    PENDING("P", "Pending"),
    REJECTED("R", "Rejected"),
    REVERSED("Z", "Reversed"),
    ;

    final String status;
    final String apiStatus;

    public static ActivityAllocationStatusEnum fromString(final String status) {
        for (final ActivityAllocationStatusEnum allocationStatusEnum : ActivityAllocationStatusEnum.values()) {
            if (allocationStatusEnum.getStatus().equalsIgnoreCase(status)) {
                return allocationStatusEnum;
            }
        }
        return null;
    }
}
