package za.co.discovery.health.journey.rule.dmn.mapper;

import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.rule.dmn.model.dmn.DmnFileContent;

@Component
@RequiredArgsConstructor
public class FileContentMapper {

    public ResponseEntity<Resource> toResourceEntity(final DmnFileContent fileContent) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileContent.getName() + ".dmn")
                .body(new ByteArrayResource(fileContent.getContent()));
    }

    public DmnFileContent toModel(final JourneyRules rules) {
        return new DmnFileContent(rules.getRuleId(), rules.getRuleSetName(), rules.getRuleSet());
    }
}
