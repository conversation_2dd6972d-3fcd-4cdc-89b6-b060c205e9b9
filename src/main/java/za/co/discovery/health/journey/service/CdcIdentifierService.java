package za.co.discovery.health.journey.service;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Slf4j
@Service
@NoArgsConstructor
public class CdcIdentifierService {

    public String getCdcIdentifier(final Long entityNo) {
        final String cdcId = "POVJ" + RandomStringUtils.randomAlphabetic(21).toUpperCase(Locale.getDefault());
        log.info("Generated CDC identifier: {} for entity: {}", cdcId, entityNo);
        return cdcId;
    }
}
