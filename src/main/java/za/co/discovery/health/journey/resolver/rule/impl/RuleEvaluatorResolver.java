package za.co.discovery.health.journey.resolver.rule.impl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.resolver.rule.RuleEvaluator;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;

import javax.annotation.PostConstruct;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RuleEvaluatorResolver {

    private final List<RuleEvaluator> evaluators;

    // Key = (RuleType, RuleProcessorType)
    // Value = concrete RuleEvaluator
    private Map<EvaluatorKey, RuleEvaluator> evaluatorMap;

    /**
     * Convert the list of evaluators into a map so that lookups can avoid streaming.
     */
    @PostConstruct
    public void initEvaluatorMap() {
        evaluatorMap = evaluators.stream()
                // Collect to a map keyed by (RuleType, RuleProcessorType)
                .collect(Collectors.toMap(
                        // Key mapper: build a new EvaluatorKey from the evaluator's
                        // handled types
                        evaluator ->
                                new EvaluatorKey(evaluator.getHandledRuleType(), evaluator.getHandledProcessorType()),
                        // Value mapper
                        Function.identity(),
                        // Merge function in case two evaluators handle the same key
                        (existing, duplicate) -> {
                            throw new CoreException(
                                    ReasonCode.GENERAL_ERROR,
                                    "Multiple evaluators found for the same RuleType/RuleProcessorType combination: "
                                            + existing.getClass().getName()
                                            + " vs. "
                                            + duplicate.getClass().getName());
                        }));
    }

    /**
     * Looks up the evaluator that can handle the given RuleType and RuleProcessorType.
     *
     * @throws CoreException if no evaluator is found
     */
    public RuleEvaluator getEvaluator(final RuleType type, final RuleProcessorType processorType) {
        final var key = new EvaluatorKey(type, processorType);
        final RuleEvaluator evaluator = evaluatorMap.get(key);

        if (evaluator == null) {
            throw new CoreException(
                    ReasonCode.VALIDATION_ERROR,
                    "No evaluator found for rule type " + type + " and processor type " + processorType);
        }
        return evaluator;
    }

    /**
     * A simple record used to map (RuleType, RuleProcessorType) to a specific RuleEvaluator.
     */
    @Data
    @AllArgsConstructor
    private static class EvaluatorKey {
        private final RuleType ruleType;
        private final RuleProcessorType processorType;
    }
}
