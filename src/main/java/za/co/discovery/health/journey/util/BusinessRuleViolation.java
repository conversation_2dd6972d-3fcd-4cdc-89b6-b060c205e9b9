package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;

@UtilityClass
public final class BusinessRuleViolation {

    public static CoreException invalidIteration(final long iteration) {
        return new CoreException(
                ReasonCode.VALIDATION_ERROR, "Invalid milestone iteration: " + iteration + ". Must be greater than 0.");
    }

    public static CoreException programNotFound(final Long programId) {
        return new CoreException(ReasonCode.VALIDATION_ERROR, "Journey program not found with ID: " + programId);
    }

    public static CoreException noProgramsForCategory(final Long categoryId) {
        return new CoreException(ReasonCode.VALIDATION_ERROR, "No programs found for category ID: " + categoryId);
    }

    public static CoreException ruleEvaluationFailed(final String ruleName, final String reason) {
        return new CoreException(
                ReasonCode.EVALUATION_ERROR, String.format("Rule evaluation failed for '%s': %s", ruleName, reason));
    }
}
