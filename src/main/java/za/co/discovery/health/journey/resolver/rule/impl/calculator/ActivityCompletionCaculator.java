package za.co.discovery.health.journey.resolver.rule.impl.calculator;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ActivityCompletionRuleEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.request.ActivityCompletionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityCompletionRuleResult;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.util.DMNConstants;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ActivityCompletionCaculator implements RuleCalculator {
    private final RuleCacheService cacheService;
    private final RuleEvaluatorResolver evaluator;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.ACTIVITY_COMPLETION_RULE;
    }

    @Override
    public GetActivityCompletionRuleResult calculate(final RuleType type, final RuleRequest ruleRequest) {
        if (!(ruleRequest instanceof ActivityCompletionRuleRequest)) {
            throw new IllegalArgumentException("Expected an ActivityCompletionRuleRequest");
        }

        final ActivityCompletionRuleRequest activityRequest = (ActivityCompletionRuleRequest) ruleRequest;
        final RuleCache ruleCache = cacheService.getRuleCache(ruleRequest.getRuleName());
        final Map<String, Object> vars = Map.of(
                DMNConstants.MILESTONE_VALUE,
                activityRequest.getIteration(),
                DMNConstants.COMPLETION_ACTIVITY_ID,
                activityRequest.getActivityMnemonic());

        final ActivityCompletionRuleEvaluationResult evaluate = (ActivityCompletionRuleEvaluationResult) evaluator
                .getEvaluator(type, ruleCache.getProcessorType())
                .evaluate(ruleRequest.getEntityId(), ruleCache, vars);

        if (evaluate.isSuccess()) {

            return GetActivityCompletionRuleResult.builder()
                    .rules(evaluate.getResults().stream()
                            .map(GetActivityCompletionRuleResult.ActivityCompletionRules::of)
                            .collect(Collectors.toList()))
                    .ruleType(ruleCache.getProcessorType())
                    .success(true)
                    .build();
        } else {
            return GetActivityCompletionRuleResult.builder()
                    .rules(List.of())
                    .ruleType(ruleCache.getProcessorType())
                    .success(false)
                    .build();
        }
    }
}
