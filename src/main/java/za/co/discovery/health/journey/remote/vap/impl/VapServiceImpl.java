package za.co.discovery.health.journey.remote.vap.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.remote.vap.VapService;
import za.co.discovery.health.journey.util.DateUtils;
import za.co.discovery.vap.api.ActivityRegisterControllerApi;
import za.co.discovery.vap.domain.WorkoutActivity;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class VapServiceImpl implements VapService {

    private final ActivityRegisterControllerApi activityRegisterControllerApi;

    @Override
    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    public List<WorkoutActivity> filterActivityValues(
            final Long entityNo, final LocalDate dateFrom, final LocalDate dateTo) {
        return Objects.requireNonNullElse(
                activityRegisterControllerApi.filterActivityValuesUsingGET(
                        "WEIGHT", entityNo, DateUtils.toDate(dateFrom), DateUtils.toDate(dateTo)),
                new ArrayList<>());
    }

    @Override
    public Optional<WorkoutActivity> getActivityValue(final Long entityNo, final LocalDate date) {
        return filterActivityValues(entityNo, date, date).stream().findFirst();
    }
}
