package za.co.discovery.health.journey.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProvider;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProviderBuilder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.reactive.function.client.ServletOAuth2AuthorizedClientExchangeFilterFunction;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import za.co.discovery.health.hs.starter.webclient.WebClientTokenRelayCustomizer;
import za.co.discovery.health.journey.config.converter.FileHttpMessageConverter;
import za.co.discovery.health.journey.config.converter.MultipartFileHttpMessageConverter;

import java.util.List;

@Profile("b2b")
@Configuration
@ComponentScan(
        excludeFilters = {
            @ComponentScan.Filter(
                    type = FilterType.ASSIGNABLE_TYPE,
                    value = {WebClientTokenRelayCustomizer.class})
        })
public class B2BWebConfiguration {

    public static final String REGISTRATION_ID = "b2b-client";

    @Value("${hs.oauth2.url}")
    private String tokenUri;

    @Value("${hs.oauth2.client.id}")
    private String clientId;

    @Value("${hs.oauth2.client.secret}")
    private String clientSecret;

    public B2BWebConfiguration(@Qualifier("b2b-resttemplate") final RestTemplate restTemplate) {
        addConverters(restTemplate);
    }

    @Bean
    public ClientRegistrationRepository clientRegistrationRepository() {
        final ClientRegistration clientRegistration = ClientRegistration.withRegistrationId(REGISTRATION_ID)
                .tokenUri(tokenUri)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                .build();

        return new InMemoryClientRegistrationRepository(clientRegistration);
    }

    @Bean
    public OAuth2AuthorizedClientManager authorizedClientManager(
            final ClientRegistrationRepository clientRegistrationRepository,
            final OAuth2AuthorizedClientService authorizedClientService) {

        final OAuth2AuthorizedClientProvider authorizedClientProvider = OAuth2AuthorizedClientProviderBuilder.builder()
                .clientCredentials()
                .build();

        final AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager =
                new AuthorizedClientServiceOAuth2AuthorizedClientManager(
                        clientRegistrationRepository, authorizedClientService);
        authorizedClientManager.setAuthorizedClientProvider(authorizedClientProvider);

        return authorizedClientManager;
    }

    @Bean("b2b-webclient")
    public WebClient webClient(final OAuth2AuthorizedClientManager authorizedClientManager) {
        final ServletOAuth2AuthorizedClientExchangeFilterFunction oauth2Client =
                new ServletOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager);
        oauth2Client.setDefaultOAuth2AuthorizedClient(true);
        oauth2Client.setDefaultClientRegistrationId(REGISTRATION_ID);

        return WebClient.builder().apply(oauth2Client.oauth2Configuration()).build();
    }

    @Primary
    @Bean("token-relay-webclient")
    public WebClient webClient(final WebClient.Builder builder) {
        return builder.filter(new WebClientTokenRelayCustomizer()).build();
    }

    private void addConverters(final RestTemplate restTemplate) {
        final List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        messageConverters.add(new FileHttpMessageConverter());
        messageConverters.add(new MultipartFileHttpMessageConverter());
    }
}
