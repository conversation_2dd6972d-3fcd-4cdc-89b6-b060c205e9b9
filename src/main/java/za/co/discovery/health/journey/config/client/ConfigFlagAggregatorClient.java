package za.co.discovery.health.journey.config.client;

import com.za.disocvery.health.configflag.ApiClient;
import com.za.disocvery.health.configflag.api.ConfigurationFlagControllerApi;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class ConfigFlagAggregatorClient {

    private final RestTemplate restTemplate;

    private final RestTemplate b2bRestTemplate;

    @Value("${integration.config-flag.url}")
    private String baseUrl;

    public ConfigFlagAggregatorClient(
            final RestTemplate restTemplate, @Qualifier("b2b-resttemplate") final RestTemplate b2bRestTemplate) {
        this.restTemplate = restTemplate;
        this.b2bRestTemplate = b2bRestTemplate;
    }

    @Bean
    @Primary
    public ConfigurationFlagControllerApi configurationFlagControllerApi() {
        final ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(baseUrl);

        return new ConfigurationFlagControllerApi(apiClient);
    }

    @Bean
    public ConfigurationFlagControllerApi b2bconfigurationFlagControllerApi() {
        final ApiClient apiClient = new ApiClient(b2bRestTemplate);
        apiClient.setBasePath(baseUrl);

        return new ConfigurationFlagControllerApi(apiClient);
    }
}
