package za.co.discovery.health.journey.database.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyEnrollmentRepository
        extends JourneyEnrollmentRepository, JpaRepository<JourneyEnrollment, Long> {

    @Query("SELECT je.journeyEnrollmentId "
            + "FROM JourneyEnrollment je "
            + "JOIN je.journeyEnrollmentAttributes attrs "
            + "WHERE attrs.attrName = :attrName "
            + "AND attrs.attrValue = :attrValue "
            + "AND je.journeyEnrollmentId > :afterId "
            + "ORDER BY je.journeyEnrollmentId ASC")
    List<Long> findNextEnrollmentIdsWithAttribute(
            @Param("attrName") String attrName,
            @Param("attrValue") String attrValue,
            @Param("afterId") Long afterId,
            Pageable pageable);

    @Query("SELECT je.journeyEnrollmentId "
        + "FROM JourneyEnrollment je "
        + "JOIN je.journeyEnrollmentAttributes attrs "
        + "WHERE je.entityId = :entityId "
        + "AND attrs.attrName = :attrName "
        + "AND attrs.attrValue = :attrValue "
        + "AND je.journeyEnrollmentId > :afterId "
        + "ORDER BY je.journeyEnrollmentId ASC")
    List<Long> findNextEnrollmentIdsWithAttributeForUser(
        @Param("entityId") Long entityId,
        @Param("attrName") String attrName,
        @Param("attrValue") String attrValue,
        @Param("afterId") Long afterId,
        Pageable pageable);

    @Query("SELECT DISTINCT je.journeyEnrollmentId FROM JourneyEnrollment je "
            + "WHERE je.entityId = :entityId "
            + "AND je.journeyCategory.journeyCategoryId = :categoryId "
            + "ORDER BY je.journeyEnrollmentId")
    List<Long> findEnrollmentIds(@Param("entityId") Long entityId, @Param("categoryId") Long categoryId);

    @Query("SELECT DISTINCT je FROM JourneyEnrollment je "
            + "LEFT JOIN FETCH je.journeyEnrollmentMilestones milestones "
            + "LEFT JOIN FETCH milestones.journeyEnrollmentMilestoneActivities "
            + "LEFT JOIN FETCH milestones.journeyEnrollmentMilestoneAwards "
            + "LEFT JOIN FETCH je.journeyProgram "
            + "LEFT JOIN FETCH je.journeyCategory "
            + "LEFT JOIN FETCH je.journeyEnrollmentProgramAwards "
            + "LEFT JOIN FETCH je.journeyEnrollment "
            + "WHERE je.entityId = :entityId "
            + "  AND je.journeyCategory.journeyCategoryId = :categoryId")
    List<JourneyEnrollment> findEnrollments(@Param("entityId") Long entityId, @Param("categoryId") Long categoryId);

    @Query("SELECT DISTINCT je "
            + "FROM JourneyEnrollment je "
            + "LEFT JOIN FETCH je.journeyEnrollmentMilestones milestones "
            + "LEFT JOIN FETCH milestones.journeyEnrollmentMilestoneActivities "
            + "LEFT JOIN FETCH milestones.journeyEnrollmentMilestoneAwards "
            + "LEFT JOIN FETCH je.journeyProgram "
            + "LEFT JOIN FETCH je.journeyCategory "
            + "LEFT JOIN FETCH je.journeyEnrollmentProgramAwards "
            + "LEFT JOIN FETCH je.journeyEnrollment "
            + "WHERE je.entityId = :entityId ")
    List<JourneyEnrollment> findEnrollments(@Param("entityId") Long entityId);

    @Query("SELECT DISTINCT je "
            + "FROM JourneyEnrollment je "
            + "LEFT JOIN FETCH je.journeyCategory "
            + "WHERE je.entityId = :entityId "
            + "  AND je.enrollmentDate <= :currentTime "
            + "  AND je.status = 'ACTIVE' "
            + "  AND (je.terminationDate IS NULL OR je.terminationDate > :currentTime) ")
    List<JourneyEnrollment> findCurrentEnrollments(
            @Param("entityId") Long entityId, @Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT DISTINCT je "
            + "FROM JourneyEnrollment je "
            + "LEFT JOIN FETCH je.journeyCategory "
            + "WHERE je.entityId = :entityId "
            + "  AND je.enrollmentDate <= :currentTime "
            + "  AND je.status in ('COMPLETED', 'NOT_ACHIEVED') "
            + "  AND (je.monitoringPeriodEndDate IS NOT NULL AND je.monitoringPeriodEndDate > :currentTime) ")
    List<JourneyEnrollment> findEnrollmentsInMonitoringPeriod(
            @Param("entityId") Long entityId, @Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT DISTINCT je "
            + "FROM JourneyEnrollment je "
            + "LEFT JOIN je.journeyCategory "
            + "WHERE je.entityId = :entityId "
            + "  AND je.journeyCategory.journeyCategoryId = :journeyCategoryId "
            + "  AND je.enrollmentDate <= :currentTime "
            + "  AND je.status = 'ACTIVE' "
            + "  AND (je.terminationDate IS NULL OR je.terminationDate > :currentTime) ")
    Optional<JourneyEnrollment> findCurrentEnrollment(Long journeyCategoryId, Long entityId, LocalDateTime currentTime);

    @Query("SELECT COUNT(je) "
            + "FROM JourneyEnrollment je "
            + "WHERE je.journeyCategory.journeyCategoryId = :journeyCategoryId ")
    Long countMembersInCategory(@Param("journeyCategoryId") Long journeyCategoryId);

    @Query("SELECT DISTINCT je "
            + "FROM JourneyEnrollment je "
            + "LEFT JOIN FETCH je.journeyEnrollmentMilestones milestones "
            + "LEFT JOIN FETCH milestones.journeyEnrollmentMilestoneActivities "
            + "LEFT JOIN FETCH je.journeyProgram "
            + "LEFT JOIN FETCH je.journeyCategory "
            + "WHERE je.status = 'ACTIVE' "
            + "AND (je.terminationDate IS NULL OR je.terminationDate > :currentTime) "
            + "AND je.journeyCategory.journeyCategoryType.name = :categoryTypeName")
    List<JourneyEnrollment> findActiveGroupCoachingEnrollments(
            @Param("currentTime") LocalDateTime currentTime, @Param("categoryTypeName") String categoryTypeName);
}
