package za.co.discovery.health.journey.service.journey.reward;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.constant.JourneyRewardAwardStatusEnum;
import za.co.discovery.health.journey.constant.JourneyRewardTypeEnum;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneAward;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentProgramAward;
import za.co.discovery.health.journey.database.projection.RewardProjection;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneAwardRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentProgramAwardRepository;
import za.co.discovery.health.journey.model.enums.EnrollmentStatus;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyProgramRewardFacade;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
@Service
@RequiredArgsConstructor
public class JourneyEnrollmentRewardService {
    private final JourneyProgramRewardFacade rewardService;
    private final ExtendedJourneyEnrollmentProgramAwardRepository programAwardRepository;
    private final ExtendedJourneyEnrollmentMilestoneAwardRepository milestoneAwardRepository;

    public void assignProgramReward(final JourneyEnrollment enrollment, final LocalDateTime now) {
        if (!EnrollmentStatus.COMPLETED.getValue().equals(enrollment.getStatus())) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Only completed enrollments can be rewarded");
        }

        if (!enrollment.getJourneyEnrollmentProgramAwards().isEmpty()) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Enrollment has already been rewarded");
        }

        final List<RewardProjection> rewards = rewardService.getProgramRewards(enrollment, now);

        final List<JourneyEnrollmentProgramAward> rewardEntities = rewards.stream()
                .map(this::map)
                .peek(reward -> reward.setJourneyEnrollment(enrollment))
                .collect(Collectors.toList());

        programAwardRepository.saveAll(rewardEntities);
    }

    public void assignMilestoneReward(final JourneyEnrollmentMilestone milestone, final LocalDateTime now) {
        if (!MilestoneStatus.COMPLETED.getValue().equals(milestone.getMilestoneStatus())) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Only completed milestone can be rewarded");
        }

        if (!milestone.getJourneyEnrollmentMilestoneAwards().isEmpty()) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Milestone has already been rewarded");
        }

        final List<RewardProjection> rewards = rewardService.getMilestoneReward(milestone, now);

        final List<JourneyEnrollmentMilestoneAward> rewardEntities = rewards.stream()
                .map(this::mapMilestone)
                .peek(reward -> reward.setJourneyEnrollmentMilestone(milestone))
                .collect(Collectors.toList());

        milestoneAwardRepository.saveAll(rewardEntities).forEach(award -> milestone
                .getJourneyEnrollmentMilestoneAwards()
                .add(award));
    }

    private JourneyEnrollmentProgramAward map(final RewardProjection reward) {
        final var rewardEntity = new JourneyEnrollmentProgramAward();
        rewardEntity.setRewardValue(reward.getRewardValue());
        rewardEntity.setRewardType(extractRewardTypeValue(reward));
        rewardEntity.setExtRewardRef(reward.getExtRewardRef());
        rewardEntity.setAwardDate(LocalDateTime.now());
        rewardEntity.setAwardStatus(JourneyRewardAwardStatusEnum.PENDING.name());
        return rewardEntity;
    }

    private JourneyEnrollmentMilestoneAward mapMilestone(final RewardProjection reward) {
        final var rewardEntity = new JourneyEnrollmentMilestoneAward();
        rewardEntity.setRewardValue(reward.getRewardValue());
        rewardEntity.setRewardType(extractRewardTypeValue(reward));
        rewardEntity.setExtRewardRef(reward.getExtRewardRef());
        rewardEntity.setAwardDate(LocalDateTime.now());
        rewardEntity.setAwardStatus(JourneyRewardAwardStatusEnum.PENDING.name());
        return rewardEntity;
    }

    private String extractRewardTypeValue(final RewardProjection reward) {
        final JourneyRewardTypeEnum rewardTypeEnum;
        try {
            rewardTypeEnum = JourneyRewardTypeEnum.valueOf(reward.getRewardType());
        } catch (IllegalArgumentException e) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Invalid reward type: '" + reward.getRewardType(), e);
        }
        return rewardTypeEnum.name();
    }
}
