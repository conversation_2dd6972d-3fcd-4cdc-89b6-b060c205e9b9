package za.co.discovery.health.journey.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MilestoneAdditionalActivityDto {
    private Long milestoneActivityId;
    private String activityMnemonicId;
    private ActivityDetailsType activityType;
}
