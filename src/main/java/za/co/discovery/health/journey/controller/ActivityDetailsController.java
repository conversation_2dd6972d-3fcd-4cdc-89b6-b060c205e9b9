package za.co.discovery.health.journey.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.model.request.SearchMilestoneActivityRequest;
import za.co.discovery.health.journey.model.response.MilestoneActivityDto;
import za.co.discovery.health.journey.model.response.MilestoneActivityPreconditionResponse;
import za.co.discovery.health.journey.model.response.MilestoneAdditionalActivityDto;
import za.co.discovery.health.journey.service.journey.JourneyActivityService;

import javax.validation.Valid;
import javax.validation.constraints.Positive;

import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/api/activities")
@RequiredArgsConstructor
@Tag(name = "Activity Completion", description = "API to manage activity and appointment completion")
public class ActivityDetailsController {

    private final JourneyActivityService activityService;

    @GetMapping("/milestone-activity/{milestoneActivityId}")
    @Operation(summary = "Get a milestone activity", description = "Get a milestone activity")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Get a milestone activity"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<MilestoneActivityDto> getMilestoneActivity(
            @Positive @PathVariable final Long milestoneActivityId) {
        return ResponseEntity.ok(activityService.getMilestoneActivity(milestoneActivityId));
    }

    @PostMapping("/milestone-activity/precondition/search")
    @Operation(summary = "Get a activity preconditions", description = "Get a activity preconditions")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Get a activity preconditions"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<List<MilestoneActivityPreconditionResponse>> getMilestoneActivityPreconditions(
            @Valid @RequestBody final SearchMilestoneActivityRequest request) {
        return ResponseEntity.ok(activityService.getMilestoneActivityPreconditions(request));
    }

    @GetMapping("/milestone-activity/{milestoneActivityId}/additional")
    @Operation(summary = "Get a additional milestone activity", description = "Get a additional milestone activity")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Get a additional milestone activity"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public ResponseEntity<MilestoneAdditionalActivityDto> getAdditionalActivityForMilestone(
            @Positive @PathVariable final Long milestoneActivityId) {
        return ResponseEntity.ok(activityService.getAdditionalActivityForMilestone(milestoneActivityId));
    }
}
