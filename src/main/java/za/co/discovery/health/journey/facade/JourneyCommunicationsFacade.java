package za.co.discovery.health.journey.facade;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import za.co.discovery.health.journey.model.communication.JourneyCategoryCommunicationDataPoint;
import za.co.discovery.health.journey.rule.conversion.ConfigFlagJexlRunner;
import za.co.discovery.health.journey.service.journey.JourneyCommunicationService;

@Service
@RequiredArgsConstructor
public class JourneyCommunicationsFacade {
    private final ConfigFlagJexlRunner configFlagJexlRunner;
    private final JourneyCommunicationService journeyCommunicationService;

    public Mono<JourneyCategoryCommunicationDataPoint> getCommunicationDataPoints(
            final Long entityId, final Long categoryId) {
        return Mono.fromSupplier(() -> configFlagJexlRunner.run(entityId, "journey_enabled"))
                .flatMap(enabled -> {
                    if (enabled) {
                        return journeyCommunicationService.createCommunicationDataPointsReactive(entityId, categoryId);
                    } else {
                        return Mono.just(new JourneyCategoryCommunicationDataPoint());
                    }
                });
    }
}
