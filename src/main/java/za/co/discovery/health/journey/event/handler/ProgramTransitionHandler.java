package za.co.discovery.health.journey.event.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.event.EnrollmentCreatedEvent;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.ProgramCompletedEvent;
import za.co.discovery.health.journey.event.ProgramExpiredEvent;
import za.co.discovery.health.journey.event.ProgramLateCompletedEvent;
import za.co.discovery.health.journey.model.enums.EnrollmentStatus;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentService;
import za.co.discovery.health.journey.service.journey.JourneyProgramService;
import za.co.discovery.health.journey.service.journey.JourneyService;

import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ProgramTransitionHandler {

    private final JourneyService journeyService;

    private final JourneyEventPublisher eventPublisher;

    private final JourneyProgramService programService;

    private final JourneyEnrollmentService enrollmentService;

    @EventListener
    @Transactional
    public void handleProgramCompleted(final ProgramCompletedEvent event) {
        log.info(
                "Handling program completed event for enrollment: {}",
                event.getEnrollment().getJourneyEnrollmentId());
        final JourneyEnrollment currentEnrollment = event.getEnrollment();

        final Optional<JourneyProgram> nextProgram = programService.getNextProgram(
                currentEnrollment.getJourneyProgram().getJourneyProgramId(),
                currentEnrollment.getJourneyCategory().getJourneyCategoryId(),
                event.getCompletionTime());

        if (nextProgram.isPresent()) {
            // Create new enrollment
            final JourneyEnrollment newEnrollment = enrollmentService.createEnrollment(
                    currentEnrollment.getEntityId(),
                    nextProgram.get(),
                    currentEnrollment.getJourneyCategory(),
                    event.getCompletionTime());

            // Mark current as completed
            if (EnrollmentStatus.ACTIVE.getValue().equals(currentEnrollment.getStatus())) {
                enrollmentService.markCompleted(
                        currentEnrollment.getJourneyEnrollmentId(), newEnrollment, event.getCompletionTime(), null);
            }

            // Trigger milestone creation for new enrollment
            eventPublisher.publish(new EnrollmentCreatedEvent(newEnrollment, event.getCompletionTime()));
        } else if (EnrollmentStatus.ACTIVE.getValue().equals(currentEnrollment.getStatus())) {

            final CategoryConfigurationRuleResult.CategoryConfiguration categoryConfiguration =
                    journeyService.getCategoryConfiguration(
                            currentEnrollment.getEntityId(), currentEnrollment.getJourneyCategory());

            // No next program - mark journey as complete
            enrollmentService.markCompleted(
                    currentEnrollment.getJourneyEnrollmentId(),
                    event.getCompletionTime(),
                    categoryConfiguration.getMonitoringPeriodDuration());
        }
    }

    @EventListener
    @Transactional
    public void handleProgramLateCompleted(final ProgramLateCompletedEvent event) {
        log.info(
                "Handling program late completed event for enrollment: {}",
                event.getEnrollment().getJourneyEnrollmentId());
        final JourneyEnrollment currentEnrollment = event.getEnrollment();

        final Optional<JourneyProgram> nextProgram = programService.getNextProgram(
                currentEnrollment.getJourneyProgram().getJourneyProgramId(),
                currentEnrollment.getJourneyCategory().getJourneyCategoryId(),
                event.getCompletionTime());

        if (nextProgram.isPresent()) {
            // Create new enrollment
            final JourneyEnrollment newEnrollment = enrollmentService.createEnrollment(
                    currentEnrollment.getEntityId(),
                    nextProgram.get(),
                    currentEnrollment.getJourneyCategory(),
                    event.getCompletionTime());

            // Mark current as completed
            if (EnrollmentStatus.ACTIVE.getValue().equals(currentEnrollment.getStatus())) {
                enrollmentService.markLateCompleted(
                        currentEnrollment.getJourneyEnrollmentId(), newEnrollment, event.getCompletionTime(), null);
            }

            // Trigger milestone creation for new enrollment
            eventPublisher.publish(new EnrollmentCreatedEvent(newEnrollment, event.getCompletionTime()));
        } else if (EnrollmentStatus.ACTIVE.getValue().equals(currentEnrollment.getStatus())) {

            final CategoryConfigurationRuleResult.CategoryConfiguration categoryConfiguration =
                    journeyService.getCategoryConfiguration(
                            currentEnrollment.getEntityId(), currentEnrollment.getJourneyCategory());

            // No next program - mark journey as complete
            enrollmentService.markLateCompleted(
                    currentEnrollment.getJourneyEnrollmentId(),
                    event.getCompletionTime(),
                    categoryConfiguration.getMonitoringPeriodDuration());
        }
    }

    @EventListener
    @Transactional
    public void handleProgramExpired(final ProgramExpiredEvent event) {
        log.info(
                "Handling program expired event for enrollment: {}",
                event.getEnrollment().getJourneyEnrollmentId());
        final JourneyEnrollment currentEnrollment = event.getEnrollment();

        if (EnrollmentStatus.ACTIVE.getValue().equals(currentEnrollment.getStatus())) {

            final CategoryConfigurationRuleResult.CategoryConfiguration categoryConfiguration =
                    journeyService.getCategoryConfiguration(
                            currentEnrollment.getEntityId(), currentEnrollment.getJourneyCategory());

            enrollmentService.markNotAchieved(
                    currentEnrollment.getJourneyEnrollmentId(),
                    event.getOccurredAt(),
                    categoryConfiguration.getMonitoringPeriodDuration());
        }
    }
}
