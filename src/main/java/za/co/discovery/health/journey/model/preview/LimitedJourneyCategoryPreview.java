package za.co.discovery.health.journey.model.preview;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class LimitedJourneyCategoryPreview {
    private Long id;
    private String name;
    private String categoryType;
    private String externalReference;
    private long journeyDuration;
    private ChronoUnit journeyDurationUnit;
    private LocalDateTime enrollmentStartTime;
    private LocalDateTime enrollmentEndTime;
    private LocalDateTime startTime;
    private List<String> preconditions;
    private Long maxParticipants;
}
