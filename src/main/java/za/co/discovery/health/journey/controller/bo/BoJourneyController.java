package za.co.discovery.health.journey.controller.bo;

import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.hs.resourceserver.service.LoggedInUserService;
import za.co.discovery.health.journey.model.bo.JourneyActivityResponse;
import za.co.discovery.health.journey.model.bo.JourneyCategoryResponse;
import za.co.discovery.health.journey.model.bo.JourneyProgramResponse;
import za.co.discovery.health.journey.service.bo.BoJourneyService;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "api/bo/journey", produces = MediaType.APPLICATION_JSON_VALUE)
public class BoJourneyController {

    private final BoJourneyService boJourneyService;

    private final LoggedInUserService loggedInUserService;

    @GetMapping("/category")
    public Page<JourneyCategoryResponse> getAllJourneyCategories(@ParameterObject final Pageable pageable) {
        return boJourneyService.getAllJourneyCategories(pageable);
    }

    @GetMapping("/category/{categoryId}/programs")
    public List<JourneyProgramResponse> getJourneyPrograms(@PathVariable final Long categoryId) {
        return boJourneyService.getJourneyPrograms(categoryId);
    }

    @GetMapping("/category/{categoryId}/activities")
    public JourneyActivityResponse getJourneyActivities(
            @PathVariable final Long categoryId, @RequestParam(required = false) final Long entityId) {

        if (entityId == null) {
            return boJourneyService.getJourneyActivities(categoryId);
        } else {
            return boJourneyService.getJourneyActivities(entityId, categoryId);
        }
    }
}
