package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCustomerDefinition;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramMilestoneRewardCustomization;
import za.co.discovery.health.journey.database.projection.RewardProjection;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyProgramMilestoneRewardCustomizationRepository
        extends JourneyProgramMilestoneRewardCustomizationRepository {
    @Query("SELECT jpmrc FROM JourneyProgramMilestoneRewardCustomization jpmrc "
            + "WHERE jpmrc.journeyCustomerDefinition = ?1 "
            + "AND jpmrc.journeyProgramMilestone = ?2")
    Optional<JourneyProgramMilestoneRewardCustomization> findByCustomerAndMilestone(
            JourneyCustomerDefinition customerDefinition, JourneyProgramMilestone milestone);

    @Query(
            value = "WITH RankedRewards AS ( "
                    + "  SELECT jpmrc.reward_type, jpmrc.reward_value, jpmrc.ext_reward_ref, "
                    + "         ROW_NUMBER() OVER (PARTITION BY jpmrc.reward_type "
                    + "           ORDER BY "
                    + "             ((CASE WHEN definition.alliance = :alliance THEN 1 ELSE 0 END) + "
                    + "              (CASE WHEN definition.group_id = :groupParam THEN 1 ELSE 0 END) + "
                    + "              (CASE WHEN definition.branch = :branch THEN 1 ELSE 0 END)) DESC"
                    + "         ) as rn "
                    + "  FROM journey.journey_program_milestone_reward_customization jpmrc "
                    + "  LEFT JOIN journey.journey_customer_definition definition "
                    + "    ON jpmrc.customer_id = definition.customer_definition_id "
                    + "  LEFT JOIN JOURNEY.JOURNEY_PROGRAM_MILESTONE m "
                    + "  ON jpmrc.JOURNEY_PROGRAM_MILESTONE_ID = m.JOURNEY_PROGRAM_MILESTONE_ID "
                    + "  WHERE jpmrc.journey_program_id = :journeyProgramId "
                    + "    AND (:groupParam = definition.group_id OR definition.group_id = '*') "
                    + "    AND (:branch = definition.branch OR definition.branch = '*') "
                    + "    AND (:alliance = definition.alliance OR definition.alliance = '*') "
                    + "    AND m.MILESTONE_RANGE_FROM <= :iteration AND m.MILESTONE_RANGE_TO >= :iteration "
                    + "    AND jpmrc.eff_from <= :time "
                    + "    AND jpmrc.eff_to >= :time "
                    + ") "
                    + "SELECT reward_type as rewardType, reward_value as rewardValue, ext_reward_ref as extRewardRef "
                    + "FROM RankedRewards "
                    + "WHERE rn = 1",
            nativeQuery = true)
    List<RewardProjection> findRewardsByCriteria(
            @Param("journeyProgramId") long journeyProgramId,
            @Param("iteration") long iteration,
            @Param("alliance") String alliance,
            @Param("groupParam") String group,
            @Param("branch") String branch,
            @Param("time") LocalDateTime time);
}
