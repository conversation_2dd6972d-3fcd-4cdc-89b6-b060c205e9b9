package za.co.discovery.health.journey.model.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class CurrentUserMilestoneEnrollmentDto extends UserMilestoneEnrollmentDto {
    private long currentProgramId;

    public CurrentUserMilestoneEnrollmentDto(final UserMilestoneEnrollmentDtoBuilder<?, ?> userMilestoneEnrollmentDto) {
        super(userMilestoneEnrollmentDto);
    }
}
