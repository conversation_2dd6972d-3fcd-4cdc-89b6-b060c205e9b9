package za.co.discovery.health.journey.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategoryType;

import javax.annotation.PostConstruct;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class JourneyTypeStrategyRegistry {
    private final Map<String, JourneyTypeStrategy> strategies = new HashMap<>();
    private final List<JourneyTypeStrategy> strategyBeans;

    public JourneyTypeStrategyRegistry(final List<JourneyTypeStrategy> strategyBeans) {
        this.strategyBeans = strategyBeans;
    }

    @PostConstruct
    public void init() {
        strategyBeans.forEach(strategy -> {
            strategies.put(strategy.getProgressType(), strategy);
            log.info("Registered journey strategy: {}", strategy.getProgressType());
        });
    }

    public JourneyTypeStrategy getStrategy(final JourneyCategory category) {
        // Get progress type from category type, default to "DEFAULT"
        final String progressType = Optional.ofNullable(category.getJourneyCategoryType())
                .map(JourneyCategoryType::getProgressType)
                .filter(type -> !type.trim().isEmpty())
                .orElse("DEFAULT");

        JourneyTypeStrategy strategy = strategies.get(progressType);
        if (strategy == null) {
            log.warn("No strategy found for progress type: {}, using DEFAULT", progressType);
            strategy = strategies.get("DEFAULT");
        }

        return strategy;
    }
}
