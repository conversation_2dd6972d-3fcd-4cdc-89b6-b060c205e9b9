package za.co.discovery.health.journey.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class JourneyEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public void publish(final JourneyEvent event) {
        log.debug("Publishing event: {} for user: {}", event.getClass().getSimpleName(), event.getUserId());
        eventPublisher.publishEvent(event);
    }
}
