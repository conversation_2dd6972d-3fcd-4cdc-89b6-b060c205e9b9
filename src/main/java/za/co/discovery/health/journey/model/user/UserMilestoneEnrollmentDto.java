package za.co.discovery.health.journey.model.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class UserMilestoneEnrollmentDto {
    private Long milestoneId;
    private LocalDateTime milestoneFrom;
    private LocalDateTime milestoneTo;
    private Long iteration;
    private MilestoneStatus status;
    private List<ActivityDetails> activities; // activities which user will do in milestone ordered by order
    private List<UserEnrollmentMilestoneReward> rewards;

    @Setter
    @Getter
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    public static class ActivityDetails {
        private Long milestoneActivityId;
        private String mnemonic;
        private ActivityDetailsType type;
        private String name;
        private String icon;
        private long activityAmount;
        private long activityCompletionCount;
        private boolean isMandatory;
        private String status;
        private List<PreconditionEvaluationResult> preconditionResults;
    }
}
