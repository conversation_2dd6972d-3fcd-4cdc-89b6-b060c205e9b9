package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRecommendationOrder;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyRecommendationOrderRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class JourneyOrderService {
    private static final Long DEFAULT_ORDER = 1L;

    private final ExtendedJourneyRecommendationOrderRepository repository;

    public long getOrderByCategoryAndProgram(final long programId, final long categoryId, final LocalDateTime time) {
        return repository
                .findByProgramIdAndCategoryId(programId, categoryId, time)
                .map(JourneyRecommendationOrder::getProgramOrder)
                .orElse(DEFAULT_ORDER);
    }

    public Map<Long, Long> getOrderByCategoryAndPrograms(
            final long categoryId, final List<Long> programIds, final LocalDateTime time) {
        return repository.findByProgramIdInAndCategoryId(programIds, categoryId, time).stream()
                .collect(Collectors.toMap(
                        order -> order.getJourneyProgram().getJourneyProgramId(),
                        JourneyRecommendationOrder::getProgramOrder,
                        (existing, replacement) -> existing // In case of duplicate keys, keep the existing
                        // value
                        ));
    }
}
