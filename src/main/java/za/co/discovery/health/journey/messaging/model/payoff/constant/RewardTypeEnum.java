package za.co.discovery.health.journey.messaging.model.payoff.constant;

import lombok.Getter;

@Getter
public enum RewardTypeEnum {
    POINTS(1L, "Points"),
    GIFT_CARD(2L, "Gift Card"),
    SPIN(3L, "Spin"),
    GIFT_CARD_ADDED_TO_WHEEL(4L, "Gift Card Added to Wheel"),
    ACTIVITY_BASED_POINTS(5L, "Activity Based Points");

    private final long value;
    private final String name;

    RewardTypeEnum(final long value, final String name) {
        this.value = value;
        this.name = name;
    }
}
