package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.resolver.rule.impl.RuleCalculatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.resolver.rule.model.result.JourneyActivationRuleResult;
import za.co.discovery.health.journey.util.DMNConstants;
import za.co.discovery.health.journey.util.model.Audience;

import java.util.List;

@Service
@RequiredArgsConstructor
public class JourneyService {

    private final RuleCalculatorResolver ruleCalculatorResolver;

    @Transactional(readOnly = true)
    public List<JourneyActivationRuleResult.CategoryRecommendation> getRecommendedCategories(final Long entityId) {
        final JourneyActivationRuleResult result = (JourneyActivationRuleResult) ruleCalculatorResolver
                .getEvaluator(RuleType.JOURNEY_ACTIVATION)
                .calculate(
                        RuleType.JOURNEY_ACTIVATION,
                        RuleRequest.builder()
                                .entityId(entityId)
                                .audience(Audience.builder().entityId(entityId).build())
                                .ruleName(DMNConstants.JOURNEY_ACTIVATION_RULE_NAME)
                                .processorType(RuleProcessorType.DMN)
                                .build());

        return result.getCategories();
    }

    @Transactional(readOnly = true)
    public List<JourneyActivationRuleResult.CategoryRecommendation> getRecommendedCategories(
            final String branch, final String alliance, final String customer) {
        final Audience audience = buildAudience(branch, alliance, customer);
        final JourneyActivationRuleResult result = (JourneyActivationRuleResult) ruleCalculatorResolver
                .getEvaluator(RuleType.JOURNEY_ACTIVATION)
                .calculate(
                        RuleType.JOURNEY_ACTIVATION,
                        RuleRequest.builder()
                                .audience(audience)
                                .ruleName(DMNConstants.JOURNEY_ACTIVATION_RULE_NAME)
                                .processorType(RuleProcessorType.DMN)
                                .build());

        return result.getCategories();
    }

    @Transactional(readOnly = true)
    public CategoryConfigurationRuleResult.CategoryConfiguration getCategoryConfiguration(
            final Long entityId, final JourneyCategory journeyCategory) {
        if (journeyRuleDoesNotExist(journeyCategory)) {
            return getEmptyConfig();
        }
        return calculateConfig(getRuleRequest(entityId, emptyAudience(entityId), journeyCategory));
    }

    @Transactional(readOnly = true)
    public CategoryConfigurationRuleResult.CategoryConfiguration getCategoryConfiguration(
            final String branch, final String alliance, final String customer, final JourneyCategory journeyCategory) {
        if (journeyRuleDoesNotExist(journeyCategory)) {
            return null;
        }

        final Audience audience = buildAudience(branch, alliance, customer);
        return calculateConfig(getRuleRequest(null, audience, journeyCategory));
    }

    private Audience buildAudience(final String branch, final String alliance, final String customer) {
        return Audience.builder()
                .allianceNo(alliance)
                .groupNo(customer)
                .branchNo(branch)
                .build();
    }

    private CategoryConfigurationRuleResult.CategoryConfiguration calculateConfig(final RuleRequest request) {
        final CategoryConfigurationRuleResult result = (CategoryConfigurationRuleResult) ruleCalculatorResolver
                .getEvaluator(RuleType.CATEGORY_CONFIGURATION_RULE)
                .calculate(RuleType.CATEGORY_CONFIGURATION_RULE, request);
        return result.getConfiguration();
    }

    private CategoryConfigurationRuleResult.CategoryConfiguration getEmptyConfig() {
        return CategoryConfigurationRuleResult.CategoryConfiguration.builder()
                .enrollmentStartTime(null)
                .enrollmentEndTime(null)
                .journeyStartTime(null)
                .activityPrecondition(null)
                .enrollmentPrecondition(null)
                .maxParticipants(null)
                .monitoringPeriodDuration(null)
                .build();
    }

    private boolean journeyRuleDoesNotExist(final JourneyCategory journeyCategory) {
        return journeyCategory.getJourneyRules() == null
                || journeyCategory.getJourneyRules().getRuleSetName() == null
                || journeyCategory.getJourneyRules().getRuleSetType() == null;
    }

    private RuleRequest getRuleRequest(
            final Long entityId, final Audience audience, final JourneyCategory journeyCategory) {
        return RuleRequest.builder()
                .entityId(entityId)
                .audience(audience)
                .ruleName(journeyCategory.getJourneyRules().getRuleSetName())
                .processorType(RuleProcessorType.valueOf(
                        journeyCategory.getJourneyRules().getRuleSetType()))
                .build();
    }

    private Audience emptyAudience(final Long entityId) {
        return Audience.builder().entityId(entityId).build();
    }
}
