package za.co.discovery.health.journey.config.client;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import za.co.discovery.health.entity.ApiClient;
import za.co.discovery.health.entity.api.EntityApi;

@Component
public class EntityDataClientConfig {

    @Value("${integration.entity-data.url}")
    private String entityServiceBaseUrl;

    private final WebClient webClient;

    public EntityDataClientConfig(@Qualifier("b2b-webclient") final WebClient b2bWebClient) {
        this.webClient = b2bWebClient;
    }

    @Bean
    public EntityApi entityApi() {
        final ApiClient apiClient = new ApiClient(webClient);
        apiClient.setBasePath(entityServiceBaseUrl);
        return new Entity<PERSON>pi(apiClient);
    }
}
