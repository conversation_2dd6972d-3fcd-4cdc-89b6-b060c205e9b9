package za.co.discovery.health.journey.rule.dmn.mapper;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.rule.dmn.model.dmn.JourneyDmnModel;

@Component
@RequiredArgsConstructor
public class DmnModelMapper {

    public JourneyDmnModel toModel(final JourneyRules from) {
        return JourneyDmnModel.builder().name(from.getRuleSetName()).build();
    }

    public JourneyRules toEntity(final JourneyDmnModel from, final byte[] file) {
        final JourneyRules entity = new JourneyRules();
        entity.setRuleSet(file);
        entity.setRuleSetName(from.getName());
        entity.setRuleSetType(RuleProcessorType.DMN.name());
        return entity;
    }

    public JourneyDmnModel buildDmnModel(final String name) {
        return JourneyDmnModel.builder().name(name).build();
    }
}
