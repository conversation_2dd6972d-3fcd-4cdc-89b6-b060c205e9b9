package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneRepository;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleCalculatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.request.MilestoneTransitionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityRuleResult;
import za.co.discovery.health.journey.resolver.rule.model.result.GetMilestoneTransitionRuleResult;
import za.co.discovery.health.journey.service.journey.reward.JourneyEnrollmentRewardService;
import za.co.discovery.health.journey.util.DateUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("PMD.AvoidDuplicateLiterals")
public class JourneyEnrollmentMilestoneService {

    private final RuleCalculatorResolver resolver;
    private final JourneyEnrollmentRewardService rewardService;
    private final ExtendedJourneyEnrollmentMilestoneRepository repository;

    public boolean isMilestoneComplete(final Long milestoneId) {
        final JourneyEnrollmentMilestone milestone = repository
                .findById(milestoneId)
                .orElseThrow(
                        () -> new CoreException(ReasonCode.VALIDATION_ERROR, "Milestone not found: " + milestoneId));

        if (isSkipMilestone(milestone)) {
            return false; // Skip milestones are not considered complete
        }

        final JourneyProgramBehaviour journeyProgramBehaviour =
                milestone.getJourneyEnrollment().getJourneyProgram().getJourneyProgramBehaviour();
        final MilestoneTransitionRuleRequest request = buildRuleRequest(milestone, journeyProgramBehaviour);
        final RuleCalculator evaluator = resolver.getEvaluator(RuleType.MILESTONE_TRANSITION_RULE);
        final var result =
                (GetMilestoneTransitionRuleResult) evaluator.calculate(RuleType.MILESTONE_TRANSITION_RULE, request);
        if (!result.isSuccess()) {
            throw new CoreException(ReasonCode.EVALUATION_ERROR, "Failed to evaluate milestone completion");
        }
        return result.isCanTransition();
    }

    private static MilestoneTransitionRuleRequest buildRuleRequest(
            final JourneyEnrollmentMilestone milestone, final JourneyProgramBehaviour journeyProgramBehaviour) {
        return MilestoneTransitionRuleRequest.builder()
                .iteration(milestone.getMilestoneIteration())
                .activities(milestone.getJourneyEnrollmentMilestoneActivities().stream()
                        .map(it -> MilestoneTransitionRuleRequest.MilestoneActivityDetails.builder()
                                .mnemonic(it.getActivityMnemonicId())
                                .flexibility(Flexibility.fromDbValue(it.getActivityFlexibility()))
                                .requiredCount(it.getActivityAmount())
                                .completionCount(it.getActivityCompletionCount())
                                .build())
                        .collect(Collectors.toList()))
                .ruleName(journeyProgramBehaviour
                        .getJourneyRulesByMilestoneTransitionRulesId()
                        .getRuleSetName())
                .processorType(RuleProcessorType.valueOf(journeyProgramBehaviour
                        .getJourneyRulesByProgramActivityRecommendationRulesId()
                        .getRuleSetType()))
                .entityId(milestone.getJourneyEnrollment().getEntityId())
                .build();
    }

    public void markAsCompleted(final JourneyEnrollmentMilestone milestone, final LocalDateTime time) {
        milestone.setMilestoneStatus(MilestoneStatus.COMPLETED.getValue());
        milestone.setMilestoneCompletedDate(time);
        final JourneyEnrollmentMilestone savedMilestone = repository.saveAndFlush(milestone);

        rewardService.assignMilestoneReward(savedMilestone, time);
    }

    public void markAsLateCompleted(final JourneyEnrollmentMilestone milestone, final LocalDateTime time) {
        milestone.setMilestoneStatus(MilestoneStatus.LATE_COMPLETED.getValue());
        milestone.setMilestoneCompletedDate(time);
        repository.saveAndFlush(milestone);
    }

    public void markAsNotAchieved(final JourneyEnrollmentMilestone milestone) {
        milestone.setMilestoneStatus(MilestoneStatus.NOT_ACHIEVED.getValue());
        repository.saveAndFlush(milestone);
    }

    public void createMilestonesFromTemplates(
            final JourneyEnrollment enrollment,
            final GetActivityRuleResult activityResult,
            final LocalDateTime referenceDateTime,
            final long iteration) {

        if (activityResult.isSkipPeriod()) {
            createSkipMilestone(enrollment, referenceDateTime, iteration);
        } else if (!(activityResult.getActivities().isEmpty())) {
            createMilestoneWithCalculatedDates(
                    enrollment,
                    activityResult.getActivities(),
                    referenceDateTime,
                    iteration,
                    MilestoneStatus.ACTIVE.getValue());
        }
    }

    public void createSkipMilestone(final JourneyEnrollment enrollment, final LocalDateTime now, final long iteration) {
        createSkipMilestone(enrollment, now, iteration, MilestoneStatus.SKIP.getValue());
    }

    public void createSkipMilestone(
            final JourneyEnrollment enrollment,
            final LocalDateTime referenceTime,
            final long iteration,
            final String status) {

        final ChronoUnit milestoneUnit = getMilestoneUnit(enrollment);
        final LocalDateTime startDate = DateUtils.calculateStartDate(milestoneUnit, referenceTime);
        final LocalDateTime endDate = startDate.plus(1, milestoneUnit);

        final JourneyEnrollmentMilestone milestoneEntity =
                buildMilestoneEntity(enrollment, iteration, startDate, endDate, status);

        // No activities for skip milestones
        enrollment.getJourneyEnrollmentMilestones().add(repository.saveAndFlush(milestoneEntity));

        log.info("Created SKIP milestone for iteration {} (period: {} to {})", iteration, startDate, endDate);
    }

    /**
     * Creates a milestone with calculated dates based on the milestone unit
     */
    public void createMilestoneWithCalculatedDates(
            final JourneyEnrollment enrollment,
            final List<GetActivityRuleResult.RecommendedActivities> milestoneActivities,
            final LocalDateTime referenceDateTime,
            final long iteration,
            final String status) {

        final ChronoUnit milestoneUnit = getMilestoneUnit(enrollment);
        final LocalDateTime startDate = DateUtils.calculateStartDate(milestoneUnit, referenceDateTime);
        final LocalDateTime endDate = startDate.plus(1, milestoneUnit);

        createMilestone(enrollment, milestoneActivities, startDate, endDate, iteration, status);
    }

    /**
     * Creates a milestone with specific dates and status - used for backfilling
     */
    public void createMilestone(
            final JourneyEnrollment enrollment,
            final List<GetActivityRuleResult.RecommendedActivities> milestoneActivities,
            final LocalDateTime startDate,
            final LocalDateTime endDate,
            final long iteration,
            final String status) {

        final JourneyEnrollmentMilestone milestoneEntity =
                buildMilestoneEntity(enrollment, iteration, startDate, endDate, status);

        addActivitiesToMilestone(milestoneEntity, milestoneActivities);

        enrollment.getJourneyEnrollmentMilestones().add(repository.saveAndFlush(milestoneEntity));

        log.info(
                "Created milestone with status {} for iteration {} (period: {} to {})",
                status,
                iteration,
                startDate,
                endDate);
    }

    /**
     * Calculates milestone dates based on the previous milestone
     */
    public MilestoneDates calculateMilestoneDates(
            final JourneyEnrollmentMilestone previousMilestone, final JourneyEnrollment enrollment) {

        final ChronoUnit milestoneUnit = getMilestoneUnit(enrollment);
        final LocalDateTime startDate = previousMilestone.getMilestoneTo();
        final LocalDateTime adjustedStart = DateUtils.calculateStartDate(milestoneUnit, startDate);
        final LocalDateTime endDate = adjustedStart.plus(1, milestoneUnit);

        return new MilestoneDates(adjustedStart, endDate);
    }

    /**
     * Get the milestone unit from the enrollment's program behavior
     */
    public ChronoUnit getMilestoneUnit(final JourneyEnrollment enrollment) {
        return ChronoUnit.valueOf(enrollment
                .getJourneyProgram()
                .getJourneyProgramBehaviour()
                .getJourneyMilestone()
                .getDescription());
    }

    /**
     * Builds a milestone entity without activities
     */
    private JourneyEnrollmentMilestone buildMilestoneEntity(
            final JourneyEnrollment enrollment,
            final long iteration,
            final LocalDateTime startDate,
            final LocalDateTime endDate,
            final String status) {

        final JourneyEnrollmentMilestone milestoneEntity = new JourneyEnrollmentMilestone();
        milestoneEntity.setJourneyEnrollment(enrollment);
        milestoneEntity.setMilestoneIteration(iteration);
        milestoneEntity.setMilestoneFrom(startDate);
        milestoneEntity.setMilestoneTo(endDate);
        milestoneEntity.setMilestoneStatus(status);

        return milestoneEntity;
    }

    /**
     * Adds activities to a milestone
     */
    private void addActivitiesToMilestone(
            final JourneyEnrollmentMilestone milestone,
            final List<GetActivityRuleResult.RecommendedActivities> activities) {

        activities.forEach(activity -> {
            final var milestoneActivity = new JourneyEnrollmentMilestoneActivity();
            milestoneActivity.setJourneyEnrollmentMilestone(milestone);
            milestoneActivity.setActivityAmount(activity.getCount());
            milestoneActivity.setActivityCompletionCount(0L);
            milestoneActivity.setActivityType(activity.getType().name());
            milestoneActivity.setActivityName(activity.getName());
            milestoneActivity.setActivityIcon(activity.getIcon());
            milestoneActivity.setActivityMnemonicId(activity.getMnemonic());
            milestoneActivity.setActivityStatus(MilestoneStatus.ACTIVE.getValue());
            milestoneActivity.setActivityFlexibility(activity.getFlexibility().getDbValue());

            milestoneActivity.setAdditionalActivityMnemonicId(activity.getAdditionalMnemonic());
            milestoneActivity.setAdditionalActivityType(activity.getAdditionalType());
            milestone.getJourneyEnrollmentMilestoneActivities().add(milestoneActivity);
        });
    }

    public boolean isSkipMilestone(final JourneyEnrollmentMilestone milestone) {
        return MilestoneStatus.SKIP.getValue().equals(milestone.getMilestoneStatus());
    }

    public void markAsSkipped(final JourneyEnrollmentMilestone expiredMilestone) {
        expiredMilestone.setMilestoneStatus(MilestoneStatus.SKIPPED.getValue());
        repository.saveAndFlush(expiredMilestone);
        log.info("Marked milestone {} as SKIPPED", expiredMilestone.getJourneyEnrollmentMilestoneId());
    }

    /**
     * Simple DTO for milestone dates
     */
    public static class MilestoneDates {
        public final LocalDateTime startDate;
        public final LocalDateTime endDate;

        public MilestoneDates(final LocalDateTime startDate, final LocalDateTime endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }
}
