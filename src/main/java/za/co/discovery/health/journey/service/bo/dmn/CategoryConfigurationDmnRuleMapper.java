package za.co.discovery.health.journey.service.bo.dmn;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.model.bo.dmn.CategoryConfigurationDmnRuleDto;
import za.co.discovery.health.journey.model.bo.dmn.DmnTableRow;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class CategoryConfigurationDmnRuleMapper {

    /**
     * Convert DmnTableRow to ActivityRecommendationDmnRuleDto
     */
    public CategoryConfigurationDmnRuleDto toCategoryConfigurationRule(final DmnTableRow tableRow) {
        if (tableRow == null) {
            return null;
        }

        return CategoryConfigurationDmnRuleDto.builder()
                .ruleId(tableRow.getRuleId())
                .description(tableRow.getDescription())

                // Map input fields
                .alliance(getInputValue(tableRow, "input_alliance"))
                .branch(getInputValue(tableRow, "input_branch"))
                .group(getInputValue(tableRow, "input_group"))

                // Map output fields
                .journeyStartTime(getOutputValue(tableRow, "output_journeyStartTime"))
                .activityPrecondition(getOutputValue(tableRow, "output_activityPrecondition"))
                .enrollmentPrecondition(getOutputValue(tableRow, "output_enrollmentPrecondition"))
                .enrollmentStartTime(getOutputValue(tableRow, "output_enrollmentStartTime"))
                .enrollmentEndTime(getOutputValue(tableRow, "output_enrollmentEndTime"))
                .maxParticipants(parseInteger(getOutputValue(tableRow, "output_maxParticipants")))
                .build();
    }

    public List<CategoryConfigurationDmnRuleDto> toCategoryConfigurationRule(final List<DmnTableRow> tableRows) {
        if (tableRows == null || tableRows.isEmpty()) {
            return Collections.emptyList();
        }

        return tableRows.stream().map(this::toCategoryConfigurationRule).collect(Collectors.toList());
    }

    // Helper methods
    private String getInputValue(final DmnTableRow tableRow, final String key) {
        final String value = tableRow.getInputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private String getOutputValue(final DmnTableRow tableRow, final String key) {
        final String value = tableRow.getOutputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private Integer parseInteger(final String value) {
        if (value == null || value.isBlank()) {
            return null;
        }

        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            // Log the error or handle as needed
            log.error("Failed to parse integer: {}, Error: {}", value, e.getMessage());
            return null;
        }
    }
}
