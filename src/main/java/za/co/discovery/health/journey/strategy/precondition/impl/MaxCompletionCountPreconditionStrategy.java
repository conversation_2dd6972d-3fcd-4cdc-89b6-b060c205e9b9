package za.co.discovery.health.journey.strategy.precondition.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneActivityRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneRepository;
import za.co.discovery.health.journey.model.ActivityCompletionPreconditionDto;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.ActivityStatus;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;
import za.co.discovery.health.journey.strategy.precondition.ActivityCompletionPreconditionStrategy;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
@Slf4j
public class MaxCompletionCountPreconditionStrategy implements ActivityCompletionPreconditionStrategy {

    private final ExtendedJourneyEnrollmentMilestoneRepository milestoneRepository;

    private final ExtendedJourneyEnrollmentMilestoneActivityRepository activityRepository;

    @Override
    public ActivityCompletionPreconditionType getType() {
        return ActivityCompletionPreconditionType.APPOINTMENT_FREQUENCY;
    }

    @Override
    public PreconditionEvaluationResult evaluate(
            final ActivityCompletionPreconditionDto precondition,
            final String currentActivityMnemonicId,
            final ActivityDetailsType currentActivityType,
            final LocalDateTime time,
            final Long entityId,
            final JourneyProgramBehaviour behaviour) {

        log.debug("Evaluating max completion count precondition for entity {}", entityId);

        final int maxCompletions = Integer.parseInt(precondition.getIdentifier());

        final int currentCompletions = getCurrentCompletionCount(
                currentActivityMnemonicId, currentActivityType, time, entityId, behaviour.getJourneyProgramId());

        if (currentCompletions < maxCompletions) {
            return PreconditionEvaluationResult.satisfied(
                    getType(), precondition.getIdentifier(), precondition.getIteration());
        }

        return PreconditionEvaluationResult.failed(
                getType(),
                precondition.getIdentifier(),
                String.format(
                        "Activity '%s' has reached maximum completion limit (%d)",
                        precondition.getIdentifier(), maxCompletions),
                precondition.getIteration());
    }

    private int getCurrentCompletionCount(
            final String currentActivityMnemonicId,
            final ActivityDetailsType activityType,
            final LocalDateTime time,
            final Long entityId,
            final Long programId) {

        return milestoneRepository
                .getMilestoneForProgramByTime(programId, entityId, time)
                .filter(milestone -> milestone.getJourneyEnrollmentMilestoneActivities().stream()
                        .noneMatch(activity -> activity.getActivityMnemonicId().equals(currentActivityMnemonicId)
                                && !activity.getActivityStatus().equals(ActivityStatus.EXPIRED.getValue())))
                .map(milestone -> activityRepository.countCompletedActivitiesByMnemonic(
                        programId,
                        entityId,
                        activityType.name(),
                        ActivityStatus.LATE_COMPLETED.getValue(),
                        milestone.getMilestoneFrom(),
                        milestone.getMilestoneTo()))
                .orElse(0);
    }
}
