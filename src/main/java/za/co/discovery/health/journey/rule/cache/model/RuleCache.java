package za.co.discovery.health.journey.rule.cache.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.dmn.engine.DmnDecision;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;

@Data
@Builder
@RequiredArgsConstructor
public class RuleCache {
    private final RuleProcessorType processorType;
    private final String ruleContent;

    @JsonIgnore
    private final DmnDecision decision;
}
