package za.co.discovery.health.journey.messaging.producer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.config.KafkaTopics;
import za.co.discovery.health.journey.messaging.model.payoff.ActivityBasedPointPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.GiftCardAddedToWheelPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.GiftCardPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.PayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.SpinPayoffEvent;

@Profile("kafka")
@Slf4j
@RequiredArgsConstructor
@Component
public class JourneyRewardEventProducer {

    private final KafkaTemplate<String, PayoffEvent> template;

    public void send(final ActivityBasedPointPayoffEvent event) {
        log.info("[send] ActivityBasedPointPayoffEvent: {}", event);
        template.send(KafkaTopics.ACTIVITY_BASED_POINT_REWARD_PAYOFF_REQUEST, event);
    }

    public void send(final GiftCardAddedToWheelPayoffEvent event) {
        log.info("[send] GiftCardAddedToWheelPayoffEvent: {}", event);
        template.send(KafkaTopics.GIFT_CARD_ADDED_TO_WHEEL_REWARD_PAYOFF_REQUEST, event);
    }

    public void send(final GiftCardPayoffEvent event) {
        log.info("[send] GiftCardPayoffEvent: {}", event);
        template.send(KafkaTopics.GIFT_CARD_REWARD_PAYOFF_REQUEST, event);
    }

    public void send(final SpinPayoffEvent event) {
        log.info("[send] SpinPayoffEvent: {}", event);
        template.send(KafkaTopics.SPIN_REWARD_PAYOFF_REQUEST, event);
    }
}
