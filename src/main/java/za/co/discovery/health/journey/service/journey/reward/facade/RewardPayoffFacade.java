package za.co.discovery.health.journey.service.journey.reward.facade;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.constant.JourneyRewardTypeEnum;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneAward;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentProgramAward;
import za.co.discovery.health.journey.messaging.model.payoff.ActivityBasedPointPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.GiftCardAddedToWheelPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.GiftCardPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.SpinPayoffEvent;
import za.co.discovery.health.journey.messaging.model.payoff.constant.RewardTypeEnum;
import za.co.discovery.health.journey.messaging.producer.JourneyRewardEventProducer;
import za.co.discovery.health.journey.service.journey.reward.model.MilestoneAwardAdapter;
import za.co.discovery.health.journey.service.journey.reward.model.ProgramAwardAdapter;
import za.co.discovery.health.journey.service.journey.reward.model.RewardAward;

@RequiredArgsConstructor
@Component
public class RewardPayoffFacade {

    private final JourneyRewardEventProducer journeyRewardEventProducer;
    private static final String rewardSource = "Journey Reward";

    public void processRewardPayoff(final JourneyEnrollmentMilestoneAward award) {
        processRewardPayoff(new MilestoneAwardAdapter(award));
    }

    public void processRewardPayoff(final JourneyEnrollmentProgramAward award) {
        processRewardPayoff(new ProgramAwardAdapter(award));
    }

    private void processRewardPayoff(final RewardAward award) {
        final String rewardType = award.getRewardType();

        if (JourneyRewardTypeEnum.ACTIVITY_BASED_POINTS.name().equals(rewardType)) {
            processPoints(award);
        } else if (RewardTypeEnum.SPIN.name().equals(rewardType)) {
            processSpin(award);
        } else if (RewardTypeEnum.GIFT_CARD.name().equals(rewardType)) {
            processGiftCard(award);
        } else if (RewardTypeEnum.GIFT_CARD_ADDED_TO_WHEEL.name().equals(rewardType)) {
            processGiftCardAddedToWheel(award);
        } else {
            throw new IllegalArgumentException("Not supported reward type: " + rewardType);
        }
    }

    private void processPoints(final RewardAward award) {
        journeyRewardEventProducer.send(ActivityBasedPointPayoffEvent.builder()
                .rewardTypeEnum(RewardTypeEnum.ACTIVITY_BASED_POINTS)
                .activitiesNumber("1")
                .entityNo(award.getEntityId())
                .awardingActivityMnemonic(award.getExtRewardRef())
                .program(null)
                .rewardSource(rewardSource)
                .awardingEventDescription(award.getAwardingEventDescription())
                .build());
    }

    private void processSpin(final RewardAward award) {
        journeyRewardEventProducer.send(SpinPayoffEvent.builder()
                .rewardTypeEnum(RewardTypeEnum.SPIN)
                .rewardValue("1")
                .spinRewardIdentifier(award.getExtRewardRef())
                .entityNo(award.getEntityId())
                .program(null)
                .rewardSource(rewardSource)
                .awardingEventDescription(award.getAwardingEventDescription())
                .build());
    }

    private void processGiftCard(final RewardAward award) {
        journeyRewardEventProducer.send(GiftCardPayoffEvent.builder()
                .rewardTypeEnum(RewardTypeEnum.GIFT_CARD)
                .entityNo(award.getEntityId())
                .program(null)
                .rewardSource(rewardSource)
                .rewardValue(String.valueOf(award.getRewardValue()))
                .giftCardRewardIdentifier(award.getExtRewardRef())
                .giftCardRewardCurrency(null)
                .awardingEventDescription(award.getAwardingEventDescription())
                .build());
    }

    private void processGiftCardAddedToWheel(final RewardAward award) {
        journeyRewardEventProducer.send(GiftCardAddedToWheelPayoffEvent.builder()
                .rewardTypeEnum(RewardTypeEnum.GIFT_CARD_ADDED_TO_WHEEL)
                .entityNo(award.getEntityId())
                .program(null)
                .rewardSource(rewardSource)
                .rewardValue("1")
                .fulfillmentId(award.getExtRewardRef())
                .awardingEventDescription(award.getAwardingEventDescription())
                .build());
    }
}
