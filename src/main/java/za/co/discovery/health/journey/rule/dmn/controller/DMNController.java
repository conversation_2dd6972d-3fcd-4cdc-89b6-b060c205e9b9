package za.co.discovery.health.journey.rule.dmn.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import za.co.discovery.health.journey.rule.dmn.mapper.FileContentMapper;
import za.co.discovery.health.journey.rule.dmn.model.dmn.DmnFileContent;
import za.co.discovery.health.journey.rule.dmn.model.dmn.JourneyDmnModel;
import za.co.discovery.health.journey.rule.dmn.service.RuleDMNService;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/bo/dmn", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class DMNController {

    private final RuleDMNService ruleDmnService;
    private final FileContentMapper fileContentMapper;

    @Operation(summary = "Get All dmns")
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public List<DmnFileContent> getFile() {
        return ruleDmnService.getAll();
    }

    @Operation(summary = "Get dmn file by id as binary")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = String.class)))
    @GetMapping(value = "file", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<Resource> getFile(@RequestParam final String name) {
        return fileContentMapper.toResourceEntity(ruleDmnService.getByName(name));
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<JourneyDmnModel> add(
            @RequestParam final String name, @ModelAttribute final MultipartFile file) throws IOException {
        return ResponseEntity.ok(ruleDmnService.add(name, file.getBytes()));
    }

    @PutMapping(value = "/{name}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<JourneyDmnModel> update(
            @PathVariable final String name, @ModelAttribute final MultipartFile file) throws IOException {
        return ResponseEntity.ok(ruleDmnService.update(name, file.getBytes()));
    }

    @DeleteMapping
    public ResponseEntity<Void> delete(@RequestParam final String name) {
        ruleDmnService.delete(name);
        return ResponseEntity.noContent().build();
    }
}
