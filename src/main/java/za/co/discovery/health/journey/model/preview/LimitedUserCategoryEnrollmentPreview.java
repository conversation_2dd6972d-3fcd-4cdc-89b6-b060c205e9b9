package za.co.discovery.health.journey.model.preview;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.enums.UserJourneyState;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Setter
@Getter
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LimitedUserCategoryEnrollmentPreview {

    private Long categoryId;

    private String categoryName;

    private String categoryType;

    private String externalReference;

    private long journeyDuration;

    private ChronoUnit journeyDurationUnit;

    private UserJourneyState state;

    private LocalDateTime enrollmentStartTime;

    private LocalDateTime enrollmentEndTime;

    private LocalDateTime startTime;

    private Long maxParticipants;

    private List<String> preconditions;
}
