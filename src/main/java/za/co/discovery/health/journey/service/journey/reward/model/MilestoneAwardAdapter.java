package za.co.discovery.health.journey.service.journey.reward.model;

import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneAward;

import java.util.Optional;

public class MilestoneAwardAdapter implements RewardAward {
    private final JourneyEnrollmentMilestoneAward award;

    public MilestoneAwardAdapter(final JourneyEnrollmentMilestoneAward award) {
        this.award = award;
    }

    @Override
    public Long getEntityId() {
        return award.getJourneyEnrollmentMilestone().getJourneyEnrollment().getEntityId();
    }

    @Override
    public String getExtRewardRef() {
        return award.getExtRewardRef();
    }

    @Override
    public String getRewardType() {
        return award.getRewardType();
    }

    @Override
    public String getRewardValue() {
        return award.getRewardValue();
    }

    @Override
    public String getAwardingEventDescription() {
        return "Journey Milestone Reward for category: "
                + Optional.ofNullable(award)
                        .map(JourneyEnrollmentMilestoneAward::getJourneyEnrollmentMilestone)
                        .map(JourneyEnrollmentMilestone::getJourneyEnrollment)
                        .map(JourneyEnrollment::getJourneyCategory)
                        .map(JourneyCategory::getName)
                        .orElse("Unknown category");
    }
}
