package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Slf4j
@UtilityClass
public class DMNUtils {
    /**
     * Safely converts an object to a String.
     *
     * @param value Value to convert
     * @return String representation or null if value is null
     */
    public static String toStringOrNull(final Object value) {
        return Objects.nonNull(value) ? value.toString() : null;
    }

    /**
     * Safely converts an object to a String.
     *
     * @param value Value to convert
     * @return String representation or null if value is null
     */
    public static String toStringOrEmpty(final Object value) {
        return Objects.nonNull(value) ? value.toString() : StringUtils.EMPTY;
    }

    /**
     * Safely converts an object to a Long.
     *
     * @param value Value to convert
     * @return Long value or null if conversion fails
     */
    public static Long toLongOrNull(final Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.error("Invalid frequency value: {}", value, e);
            return null;
        }
    }
}
