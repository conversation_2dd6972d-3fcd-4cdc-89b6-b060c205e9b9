package za.co.discovery.health.journey.service.journey.reward.model;

import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentProgramAward;

import java.util.Optional;

public class ProgramAwardAdapter implements RewardAward {
    private final JourneyEnrollmentProgramAward award;

    public ProgramAwardAdapter(final JourneyEnrollmentProgramAward award) {
        this.award = award;
    }

    @Override
    public Long getEntityId() {
        return award.getJourneyEnrollment().getEntityId();
    }

    @Override
    public String getExtRewardRef() {
        return award.getExtRewardRef();
    }

    @Override
    public String getRewardType() {
        return award.getRewardType();
    }

    @Override
    public String getRewardValue() {
        return award.getRewardValue();
    }

    @Override
    public String getAwardingEventDescription() {
        return "Journey Program Reward for category: "
                + Optional.ofNullable(award)
                        .map(JourneyEnrollmentProgramAward::getJourneyEnrollment)
                        .map(JourneyEnrollment::getJourneyCategory)
                        .map(JourneyCategory::getName)
                        .orElse("Unknown category");
    }
}
