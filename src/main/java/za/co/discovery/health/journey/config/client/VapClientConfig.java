package za.co.discovery.health.journey.config.client;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;
import za.co.discovery.vap.ApiClient;
import za.co.discovery.vap.api.ActivityRegisterControllerApi;

@Configuration
@RequiredArgsConstructor
public class VapClientConfig {

    @Value("${integration.vap.url}")
    String vapUrl;

    @Primary
    @Bean
    public ActivityRegisterControllerApi activityRegisterControllerApi(final RestTemplate restTemplate) {
        final ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(vapUrl);
        return new ActivityRegisterControllerApi(apiClient);
    }
}
