package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyEnrollmentMilestoneRepository
        extends JourneyEnrollmentMilestoneRepository, JpaRepository<JourneyEnrollmentMilestone, Long> {
    @Query("SELECT v FROM JourneyEnrollmentMilestone v "
            + "WHERE v.journeyEnrollment.entityId = :entityId "
            + "AND v.milestoneStatus = 'ACTIVE' "
            + "AND v.milestoneFrom <= :completedAt "
            + "AND v.milestoneTo >= :completedAt")
    List<JourneyEnrollmentMilestone> findAllActiveMilestones(Long entityId, LocalDateTime completedAt);

    @Query("SELECT v FROM JourneyEnrollmentMilestone v "
            + "WHERE v.journeyEnrollment.entityId = :entityId "
            + "AND v.journeyEnrollment.journeyCategory.journeyCategoryId = :categoryId "
            + "AND v.milestoneFrom <= :currentTime "
            + "AND v.milestoneTo >= :currentTime")
    Optional<JourneyEnrollmentMilestone> findMilestoneForCategory(
            Long categoryId, Long entityId, LocalDateTime currentTime);

    @Query("SELECT v FROM JourneyEnrollmentMilestone v "
            + "WHERE v.journeyEnrollment.journeyEnrollmentId = :enrollmentId "
            + "order by v.milestoneIteration asc ")
    List<JourneyEnrollmentMilestone> getMilestonesByEnrollment(Long enrollmentId);

    @Query("SELECT DISTINCT m FROM JourneyEnrollmentMilestone m "
            + "LEFT JOIN FETCH m.journeyEnrollmentMilestoneActivities "
            + "JOIN m.journeyEnrollment je "
            + "WHERE je.entityId = :entityId "
            + "ORDER BY m.milestoneIteration ASC")
    List<JourneyEnrollmentMilestone> findAllMilestonesByEntityIdWithActivities(@Param("entityId") Long entityId);

    @Query("SELECT DISTINCT m FROM JourneyEnrollmentMilestone m "
            + "LEFT JOIN FETCH m.journeyEnrollmentMilestoneActivities "
            + "JOIN m.journeyEnrollment je "
            + "WHERE je.journeyProgram.journeyProgramId = :programId "
            + "AND :time between m.milestoneFrom and m.milestoneTo "
            + "AND je.entityId = :entityId ")
    Optional<JourneyEnrollmentMilestone> getMilestoneForProgramByTime(
            Long programId, Long entityId, LocalDateTime time);
}
