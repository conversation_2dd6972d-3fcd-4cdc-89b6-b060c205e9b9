package za.co.discovery.health.journey.model.preview;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import za.co.discovery.health.journey.model.JourneyBehaviourDto;

import java.util.List;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JourneyProgramPreview {

    private Long programId;
    private String programName;
    private JourneyBehaviourDto behaviour;
    private List<JourneyMilestonePreview> milestones;
    private List<JourneyRewardPreview> rewards;
    private Long order;
}
