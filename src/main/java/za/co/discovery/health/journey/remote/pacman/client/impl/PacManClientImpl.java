package za.co.discovery.health.journey.remote.pacman.client.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import za.co.discovery.health.journey.remote.pacman.client.PacManClient;
import za.co.discovery.health.journey.util.DateUtils;
import za.co.discovery.health.pacman.api.ActivityControllerApi;
import za.co.discovery.health.pacman.api.ActivityTransactionControllerApi;
import za.co.discovery.health.pacman.api.ProgramEnrolmentCohortActivityControllerApi;
import za.co.discovery.health.pacman.domain.ActivityFilter;
import za.co.discovery.health.pacman.domain.ActivityTransactionFilter;
import za.co.discovery.health.pacman.domain.ActivityTransactionResponse;
import za.co.discovery.health.pacman.domain.MemberActivityExclusionFilter;
import za.co.discovery.health.pacman.domain.PageActivity;
import za.co.discovery.health.pacman.domain.PageProgramEnrolmentCohortActivityDTO;
import za.co.discovery.health.pacman.domain.PagingRequest;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class PacManClientImpl implements PacManClient {
    private final ProgramEnrolmentCohortActivityControllerApi cohortActivityControllerApi;
    private final ActivityControllerApi activityControllerApi;
    private final ActivityTransactionControllerApi activityTransactionControllerApi;

    @Override
    public Mono<PageProgramEnrolmentCohortActivityDTO> getActivities(
            final PagingRequest pagingRequest, final String programName, final Long entityNo) {
        return cohortActivityControllerApi.retrieveMemberActivities(
                pagingRequest.getPage(),
                pagingRequest.getSize(),
                null,
                null,
                entityNo,
                null,
                null,
                null,
                null,
                true,
                programName,
                null,
                List.of());
    }

    @Override
    public Mono<Map<String, ProgramEnrolmentCohortActivityDTO>> getActivitiesByMnemonic(
            final Long entityId,
            final Set<String> mnemonics,
            final Predicate<ProgramEnrolmentCohortActivityDTO> predicate) {
        final LocalDate now = LocalDate.now();
        return cohortActivityControllerApi
                .getMemberActivitiesByMnemonics(entityId, new ArrayList<>(mnemonics), String.valueOf(entityId))
                .filter(it -> predicate.test(it)
                        && DateUtils.isBetweenOrEqual(
                                now,
                                Objects.requireNonNull(it.getEffForm()).toLocalDate(),
                                Objects.requireNonNull(it.getEffTo()).toLocalDate()))
                .collect(Collectors.toMap(ProgramEnrolmentCohortActivityDTO::getMnemonic, Function.identity()));
    }

    @Override
    public Mono<PageActivity> filterActivity(final PagingRequest pagingRequest, final ActivityFilter filter) {
        return activityControllerApi.filter5(
                pagingRequest.getPage(),
                pagingRequest.getSize(),
                pagingRequest.getSortField(),
                null,
                filter.getDescr(),
                filter.getActivityTypeId(),
                filter.getActivityEvent());
    }

    @Override
    public Mono<Map<String, ProgramEnrolmentCohortActivityDTO>> getActivitiesByMnemonicWithCustomExclusions(
            final Long entityId,
            final Set<String> mnemonics,
            final Predicate<ProgramEnrolmentCohortActivityDTO> predicate,
            final MemberActivityExclusionFilter exclusions) {
        final LocalDate now = LocalDate.now();

        return cohortActivityControllerApi
                .getMemberActivitiesByMnemonicsWithCustomExclusion(
                        exclusions, entityId, new ArrayList<>(mnemonics), String.valueOf(entityId))
                .filter(it -> predicate.test(it)
                        && DateUtils.isBetweenOrEqual(
                                now,
                                DateUtils.convertToLocalDate(it.getEffForm()),
                                DateUtils.convertToLocalDate(it.getEffTo())))
                .collect(Collectors.toMap(
                        ProgramEnrolmentCohortActivityDTO::getMnemonic,
                        Function.identity(),
                        (existing, replacement) -> {
                            if (Objects.isNull(existing.getProgramEnrolmentCohortActivityId())) {
                                return replacement;
                            }

                            if (Objects.isNull(replacement.getProgramEnrolmentCohortActivityId())) {
                                return existing;
                            }

                            if (Long.parseLong(existing.getProgramEnrolmentCohortActivityId())
                                    > Long.parseLong(replacement.getProgramEnrolmentCohortActivityId())) {
                                return existing;
                            } else {
                                return replacement;
                            }
                        }));
    }

    @Override
    public Mono<Map<Long, ActivityTransactionResponse>> getTransaction(
            final Long entityId, final Set<Long> activityTxnIds) {
        return activityTransactionControllerApi
                .filter(buildFilter(entityId, activityTxnIds), 0, activityTxnIds.size(), List.of())
                .map(
                        pageResponse -> Objects.requireNonNullElse(
                                        pageResponse.getContent(), new ArrayList<ActivityTransactionResponse>())
                                .stream()
                                .collect(Collectors.toMap(ActivityTransactionResponse::getId, Function.identity())))
                .defaultIfEmpty(Collections.emptyMap());
    }

    @Override
    public Mono<Map<Long, ActivityTransactionResponse>> getTransactionByMnemonic(
            final Long entityId, final String mnemonic) {
        return activityTransactionControllerApi
                .filter(buildFilter(entityId, mnemonic), 0, 10_000, List.of())
                .map(
                        pageResponse -> Objects.requireNonNullElse(
                                        pageResponse.getContent(), new ArrayList<ActivityTransactionResponse>())
                                .stream()
                                .collect(Collectors.toMap(ActivityTransactionResponse::getId, Function.identity())))
                .defaultIfEmpty(Collections.emptyMap());
    }

    private ActivityTransactionFilter buildFilter(final Long entityId, final Set<Long> activityTxnIds) {
        return new ActivityTransactionFilter().entityNo(entityId).activityTransactionIds(activityTxnIds);
    }

    private ActivityTransactionFilter buildFilter(final Long entityId, final String mnemonic) {
        return new ActivityTransactionFilter().entityNo(entityId).eventId(mnemonic);
    }
}
