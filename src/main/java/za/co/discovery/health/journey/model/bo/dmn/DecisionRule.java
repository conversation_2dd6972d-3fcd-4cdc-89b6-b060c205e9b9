package za.co.discovery.health.journey.model.bo.dmn;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class DecisionRule {
    // Getters and setters
    private Map<String, Object> inputs;
    private Map<String, Object> outputs;

    public DecisionRule() {
        this.inputs = new HashMap<>();
        this.outputs = new HashMap<>();
    }
}
