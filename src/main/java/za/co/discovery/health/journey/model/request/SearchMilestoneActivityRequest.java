package za.co.discovery.health.journey.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import za.co.discovery.health.journey.model.enums.ActivityStatus;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchMilestoneActivityRequest {
    private String activityMnemonicId;
    private Long entityId;
    private List<ActivityStatus> status;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime currentTime;
}
