package za.co.discovery.health.journey.rule.dmn.service;

import za.co.discovery.health.journey.rule.dmn.model.dmn.DmnFileContent;
import za.co.discovery.health.journey.rule.dmn.model.dmn.JourneyDmnModel;

import java.util.List;

public interface RuleDMNService {

    List<DmnFileContent> getAll();

    DmnFileContent getByName(String name);

    JourneyDmnModel add(String name, byte[] file);

    JourneyDmnModel update(String name, byte[] file);

    void delete(String name);
}
