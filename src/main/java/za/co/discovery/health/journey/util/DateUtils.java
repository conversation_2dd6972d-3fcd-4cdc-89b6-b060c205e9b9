package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.time.FastDateFormat;
import za.co.discovery.health.journey.util.model.CustomMonth;
import za.co.discovery.health.journey.util.model.CustomWeek;
import za.co.discovery.health.journey.util.model.MonthWeekPair;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@UtilityClass
public class DateUtils {
    public final LocalDateTime END_OF_ERA = LocalDateTime.of(9000, 1, 1, 1, 1);
    public static final FastDateFormat SDF_ENTITY_CLIENT = FastDateFormat.getInstance("yyyyMMdd");
    public static final DateTimeFormatter LDT_SDF_ENTITY_CLIENT = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter PROGRESS_CARD_TIME_FORMATTER = DateTimeFormatter.ofPattern("MMM dd");
    public static final Date MAX_TIME = Date.from(
            LocalDateTime.of(9000, 1, 1, 1, 1).atZone(ZoneId.systemDefault()).toInstant());

    public static final DateTimeFormatter DATE_FORMATTER_EXCEL = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static MonthWeekPair getWeekOfMonth(final LocalDate date, final LocalDateTime effFrom) {
        return new MonthWeekPair(getMonthDifference(date, effFrom).get(), getWeekDifference(date));
    }

    private static AtomicInteger getMonthDifference(final LocalDate date, final LocalDateTime effFrom) {
        LocalDate effFromDate = getCustomMonth(effFrom.toLocalDate()).getStartDay();
        final AtomicInteger difference = new AtomicInteger(0);

        while (!effFromDate.isAfter(date)) {
            effFromDate = getCustomMonth(effFromDate).getEndDay().plusDays(1);
            difference.incrementAndGet();
        }
        return difference;
    }

    private static long getWeekDifference(final LocalDate date) {
        final LocalDate startDay = getCustomMonth(date).getStartDay();

        // Calculate the difference in weeks between the first Monday and the given date
        return (date.toEpochDay() - startDay.toEpochDay()) / 7 + 1;
    }

    public static CustomMonth getCustomMonth(final LocalDate date) {
        // get Monday of the date week
        final LocalDate mondayOfThisWeek = getCustomWeek(date).getStartDay();

        // get first day of month
        final LocalDate firstMonday = mondayOfThisWeek
                .with(TemporalAdjusters.firstDayOfMonth())
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));

        // get last day of month
        final LocalDate lastSunday = mondayOfThisWeek
                .with(TemporalAdjusters.lastDayOfMonth())
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        return new CustomMonth(firstMonday, lastSunday);
    }

    public static CustomWeek getCustomWeek(final LocalDate date) {
        // get Monday of the date week
        final LocalDate mondayOfThisWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // get Sunday of the date week
        final LocalDate sundayOfThisWeek = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        return new CustomWeek(mondayOfThisWeek, sundayOfThisWeek);
    }

    public static OffsetDateTime toODT(final LocalDateTime ldt) {
        return ldt.atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    public LocalDate calculateExpirationDate(
            final Integer expiration, final String expirationDuration, final LocalDate currentDate) {
        switch (expirationDuration) {
            case "MONTH":
                return currentDate.plusMonths(expiration);
            case "DAYS":
                return currentDate.plusDays(expiration);
            case "YEAR":
                return currentDate.plusYears(expiration);
            default:
                throw new IllegalArgumentException("Invalid expiration duration: " + expirationDuration);
        }
    }

    public static boolean isCurrentDateInRange(final Date from, final Date to) {
        // no date limits
        if (from == null && to == null) {
            return true;
        }
        final Date now = new Date();
        // from date is not defined
        if (from == null) {
            return now.before(to);
        }
        // to date is not defined
        if (to == null) {
            return now.after(from);
        }
        return now.before(to) && now.after(from);
    }

    public static Date toDate(final LocalDate date) {
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(final LocalDateTime date) {
        return Date.from(date.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate toLocalDate(final Calendar calendar) {
        return LocalDate.of(
                calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
    }

    public static boolean isCompletionDateBetweenPolicyYears(
            final Date startOfPolicyYear, final Date endOfPolicyYear, final Calendar activityCompletionDate) {
        // Convert Calendar to Date for comparison
        final Date completionDate = activityCompletionDate.getTime();

        // Check if completionDate is between startOfPolicyYear and endOfPolicyYear
        return completionDate.compareTo(startOfPolicyYear) >= 0 && completionDate.compareTo(endOfPolicyYear) <= 0;
    }

    public static boolean isBeforeOrEqual(final LocalDate date1, final LocalDate date2) {
        if (Objects.equals(date1, date2)) {
            return true;
        }
        if (Objects.isNull(date1) || Objects.isNull(date2)) {
            return false;
        }
        return !date1.isAfter(date2);
    }

    public static boolean isAfterOrEqual(final LocalDate date1, final LocalDate date2) {
        if (Objects.equals(date1, date2)) {
            return true;
        }
        if (Objects.isNull(date1) || Objects.isNull(date2)) {
            return false;
        }
        return !date1.isBefore(date2);
    }

    public static boolean isBetweenOrEqual(
            final LocalDate targetDate, final LocalDate startDate, final LocalDate endDate) {
        // If targetDate is null, it cannot be between any dates.
        if (Objects.isNull(targetDate)) {
            return false;
        }
        // Check if targetDate is after or equal to startDate and before or equal to endDate.
        final boolean isAfterOrEqualStart = isAfterOrEqual(targetDate, startDate);
        final boolean isBeforeOrEqualEnd = isBeforeOrEqual(targetDate, endDate);

        return isAfterOrEqualStart && isBeforeOrEqualEnd;
    }

    public static LocalDateTime calculateStartDate(final ChronoUnit unit, final LocalDateTime dateTime) {
        switch (unit) {
                /* sub-day units: truncate, then advance one unit */
            case SECONDS:
            case MINUTES:
            case HOURS:
                return dateTime.truncatedTo(unit).plus(1, unit);

                /* start of tomorrow */
            case DAYS:
                return dateTime.truncatedTo(ChronoUnit.DAYS).plusDays(1);

                /* Monday 00:00 of the next ISO week */
            case WEEKS: {
                final LocalDate nextMonday =
                        dateTime.toLocalDate().with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
                return nextMonday.atStartOfDay();
            }

                /* first day (00:00) of the next month */
            case MONTHS:
                return dateTime.toLocalDate()
                        .with(TemporalAdjusters.firstDayOfNextMonth())
                        .atStartOfDay();

                /* 1 January (00:00) of the next year */
            case YEARS:
                return dateTime.toLocalDate()
                        .with(TemporalAdjusters.firstDayOfNextYear())
                        .atStartOfDay();

            default:
                throw new IllegalArgumentException("Unsupported unit: " + unit);
        }
    }

    public LocalDate convertToLocalDate(final OffsetDateTime offsetDateTimeToConvert) {
        if (Objects.isNull(offsetDateTimeToConvert)) {
            return null;
        }
        return offsetDateTimeToConvert.toLocalDate();
    }
}
