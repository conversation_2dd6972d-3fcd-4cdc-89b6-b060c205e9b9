package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;

import java.util.Optional;

@Repository
public interface ExtendedJourneyProgramBehaviourRepository extends JourneyProgramBehaviourRepository {
    @Query("SELECT jb FROM JourneyProgramBehaviour jb " + "WHERE jb.journeyProgram = :program ")
    Optional<JourneyProgramBehaviour> findByProgram(JourneyProgram program);
}
