package za.co.discovery.health.journey.config.exception;

/**
 * Exception thrown when a concurrent calculation attempt is detected
 * for the same entity+enrollmentId combination.
 */
public class ConcurrentCalculationException extends CoreException {
    private static final long serialVersionUID = -3314597752505792131L;

    /**
     * Creates a new ConcurrentCalculationException with a default message.
     */
    public ConcurrentCalculationException() {
        super(ReasonCode.VALIDATION_ERROR, "Calculation already in progress for this member enrollment");
    }

    public ConcurrentCalculationException(final ReasonCode reasonCode, final String message) {
        super(reasonCode, message);
    }

    public ConcurrentCalculationException(
            final ReasonCode reasonCode, final String message, final Throwable throwable) {
        super(reasonCode, message, throwable);
    }
}
