package za.co.discovery.health.journey.resolver.rule.model.request;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class MilestoneTransitionRuleRequest extends RuleRequest {
    private final Long iteration;

    private final List<MilestoneActivityDetails> activities;

    @Data
    @Builder
    public static class MilestoneActivityDetails {
        private final String mnemonic;
        private final Long requiredCount;
        private final Long completionCount;
        private final Flexibility flexibility;
    }
}
