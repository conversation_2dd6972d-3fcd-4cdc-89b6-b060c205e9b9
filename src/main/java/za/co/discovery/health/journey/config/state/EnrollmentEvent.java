package za.co.discovery.health.journey.config.state;

public enum EnrollmentEvent {
    // Initial activation event
    INITIALIZE,

    // Trigger milestone evaluation
    EVALUATE,

    // Continue with current program
    CONTINUE,

    // Complete enrollment successfully
    COMPLETE,

    // Mark enrollment as expired
    EXPIRE,

    // Retry failed operations
    RETRY,

    // Suspend enrollment
    SUSPEND,

    // Resume from suspension
    RESUME,

    // Handle errors
    ERROR_OCCURRED;

    public boolean isSystemEvent() {
        return this == ERROR_OCCURRED || this == EXPIRE;
    }
}
