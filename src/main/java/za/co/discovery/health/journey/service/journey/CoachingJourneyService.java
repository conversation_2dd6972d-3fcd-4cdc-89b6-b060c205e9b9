package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.database.projection.RewardProjection;
import za.co.discovery.health.journey.messaging.model.AttendeeType;
import za.co.discovery.health.journey.messaging.model.NotificationTemplate;
import za.co.discovery.health.journey.model.bo.ActivityDetails;
import za.co.discovery.health.journey.model.bo.dmn.ActivityRecommendationDmnRuleDto;
import za.co.discovery.health.journey.service.bo.BoJourneyService;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyProgramRewardFacade;
import za.co.discovery.health.journey.util.Constants;
import za.co.discovery.health.journey.util.JourneyConfigExtractionUtils;

import java.nio.charset.StandardCharsets;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.format.TextStyle;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class CoachingJourneyService {

    private final BoJourneyService boJourneyService;
    private final JourneyProgramRewardFacade rewardFacade;

    public NotificationTemplate createEnrollmentNotification(
            final JourneyEnrollment enrollment, final LocalDateTime startTime, final LocalDateTime endDate) {

        final DayOfWeek dayOfWeek = startTime.getDayOfWeek();
        final String dayOfWeekName = dayOfWeek.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
        final String maxSession = getMaxSession(enrollment);
        final String attendSession = getAttendSession(enrollment);
        final String description =
                Objects.requireNonNullElse(enrollment.getJourneyProgram().getDescription(), "");
        final String rewardValue = rewardFacade.getProgramRewards(enrollment, startTime).stream()
                .map(RewardProjection::getRewardValue)
                .findFirst()
                .orElse("");

        final Map<String, String> attributes = new HashMap<>();
        attributes.put("TopicName", enrollment.getJourneyCategory().getName());
        attributes.put("StartDate", startTime.toString());
        attributes.put("EndDate", endDate.toString());
        attributes.put("DayofWeek", dayOfWeekName);
        attributes.put("AttendSession", attendSession);
        attributes.put("MaxSession", maxSession);
        attributes.put("Reward", rewardValue);
        attributes.put("ProgramDescription", description);
        attributes.put(
                "JourneyId",
                enrollment.getJourneyCategory().getJourneyCategoryId().toString());

        return NotificationTemplate.builder()
                .attendeeType(AttendeeType.PATIENT)
                .type(Constants.ENROL_IN_PERSONAL_JOURNEY)
                .destinationEntityId(enrollment.getEntityId())
                .attributes(attributes)
                .build();
    }

    public NotificationTemplate createEnrollmentPeriodOpenedNotification(
            final Long entityId, final JourneyCategory journeyCategory, final LocalDateTime enrollmentEndDate) {

        final Map<String, String> attributes = new HashMap<>();
        attributes.put("JourneyId", journeyCategory.getJourneyCategoryId().toString());
        attributes.put("JourneyName", journeyCategory.getCategoryCode());
        attributes.put("EndDate", enrollmentEndDate.toString());

        return NotificationTemplate.builder()
                .attendeeType(AttendeeType.PATIENT)
                .type(Constants.PERSONAL_JOURNEY_ENROLLMENT_OPEN)
                .destinationEntityId(entityId)
                .attributes(attributes)
                .build();
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    private String getMaxSession(final JourneyEnrollment enrollment) {
        try {
            final List<ActivityDetails> appointments = boJourneyService.getProgramActivities(
                    enrollment.getJourneyProgram(),
                    enrollment.getEntityId(),
                    ActivityRecommendationDmnRuleDto.ActivityType.APPOINTMENT);
            return String.valueOf(appointments.size());
        } catch (Exception e) {
            log.warn(
                    "Failed to get max session for enrollment {}: {}",
                    enrollment.getJourneyEnrollmentId(),
                    e.getMessage());
            return "0";
        }
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    private String getAttendSession(final JourneyEnrollment enrollment) {
        try {
            final JourneyRules completionRules = enrollment
                    .getJourneyProgram()
                    .getJourneyProgramBehaviour()
                    .getJourneyRulesByProgramCompletionRulesId();

            if (completionRules == null || completionRules.getRuleSet() == null) {
                log.warn("No completion rules found for enrollment {}", enrollment.getJourneyEnrollmentId());
                return "0";
            }

            final String ruleExpression = new String(completionRules.getRuleSet(), StandardCharsets.UTF_8);
            return JourneyConfigExtractionUtils.extractAppointmentCountFromRule(ruleExpression);
        } catch (Exception e) {
            log.warn(
                    "Failed to get attend session for enrollment {}: {}",
                    enrollment.getJourneyEnrollmentId(),
                    e.getMessage());
            return "0";
        }
    }
}
