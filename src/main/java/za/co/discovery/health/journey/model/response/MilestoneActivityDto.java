package za.co.discovery.health.journey.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.ActivityStatus;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MilestoneActivityDto {
    private String journeyCategoryName;
    private Long milestoneActivityId;
    private String activityMnemonicId;
    private ActivityDetailsType activityType;
    private ActivityStatus activityStatus;
    private String activityValue;
    private LocalDateTime completedAt;
}
