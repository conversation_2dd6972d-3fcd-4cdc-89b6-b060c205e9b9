package za.co.discovery.health.journey.service.notification.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.databaseMapping.UserJourneyNotification;
import za.co.discovery.health.journey.database.repository.ExtendedUserJourneyNotificationRepository;
import za.co.discovery.health.journey.model.notification.NotificationStatus;
import za.co.discovery.health.journey.service.notification.UserJourneyNotificationService;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class UserJourneyNotificationServiceImpl implements UserJourneyNotificationService {

    private final ExtendedUserJourneyNotificationRepository notificationRepository;

    @Override
    public void cancelNotification(final UserJourneyNotification notification) {
        notification.setNotificationStatus(NotificationStatus.CANCELLED.name());
        notification.setNotifiedAt(LocalDateTime.now());
        notificationRepository.save(notification);
    }

    @Override
    public void updateSentNotification(final UserJourneyNotification notification, final LocalDateTime notifiedAt) {
        notification.setNotificationStatus(NotificationStatus.SENT.name());
        notification.setNotifiedAt(notifiedAt);
        notificationRepository.save(notification);
    }

    @Override
    public void updateNotificationStartEndTimes(
            final UserJourneyNotification notification, final LocalDateTime startTime, final LocalDateTime endTime) {
        notification.setEnrollmentStartTime(startTime);
        notification.setEnrollmentEndTime(endTime);
        notificationRepository.save(notification);
    }

    @Override
    public List<UserJourneyNotification> findScheduledNotifications() {
        return notificationRepository.findAllByNotificationStatus(NotificationStatus.SCHEDULED.name());
    }

    @Override
    public List<UserJourneyNotification> findScheduledNotifications(
            final String alliance, final String customer, final String branch, final Long journeyCategoryId) {
        return notificationRepository.findNotificationsWithStatus(
                alliance, customer, branch, journeyCategoryId, NotificationStatus.SCHEDULED.name());
    }

    @Override
    public List<UserJourneyNotification> findNotificationToSend(final LocalDateTime currentTime) {
        return notificationRepository.findNotificationsInEnrollmentPeriodWithStatus(
                currentTime, NotificationStatus.SCHEDULED.name());
    }
}
