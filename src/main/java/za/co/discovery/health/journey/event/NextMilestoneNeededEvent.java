package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;

@Getter
public class NextMilestoneNeededEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;
    private final long nextIteration;
    private final LocalDateTime startTime;

    public NextMilestoneNeededEvent(
            final JourneyEnrollment enrollment, final long nextIteration, final LocalDateTime startTime) {
        super(enrollment.getEntityId(), LocalDateTime.now());
        this.enrollment = enrollment;
        this.nextIteration = nextIteration;
        this.startTime = startTime;
    }
}
