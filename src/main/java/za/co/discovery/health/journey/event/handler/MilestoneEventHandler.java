package za.co.discovery.health.journey.event.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.event.EnrollmentCreatedEvent;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.MilestoneCompletedEvent;
import za.co.discovery.health.journey.event.MilestoneLateCompletedEvent;
import za.co.discovery.health.journey.event.NextMilestoneNeededEvent;
import za.co.discovery.health.journey.event.ProgramCompletedEvent;
import za.co.discovery.health.journey.event.ProgramExpiredEvent;
import za.co.discovery.health.journey.event.ProgramLateCompletedEvent;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentMilestoneService;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentService;
import za.co.discovery.health.journey.service.journey.JourneyProgramService;

@Slf4j
@Component
@RequiredArgsConstructor
public class MilestoneEventHandler {
    private final JourneyEnrollmentMilestoneService milestoneService;
    private final JourneyEnrollmentService enrollmentService;
    private final JourneyProgramService programService;
    private final JourneyEventPublisher eventPublisher;

    @EventListener
    @Transactional
    public void handleEnrollmentCreated(final EnrollmentCreatedEvent event) {
        log.info(
                "Creating initial milestone for enrollment: {}",
                event.getEnrollment().getJourneyEnrollmentId());

        final var activities = programService.getMilestoneActivities(
                event.getEnrollment().getJourneyProgram().getJourneyProgramId(),
                event.getEnrollment().getEntityId(),
                1L);

        milestoneService.createMilestonesFromTemplates(
                event.getEnrollment(), activities, event.getJourneyStartTime(), 1L);
    }

    @EventListener
    @Transactional
    public void handleNextMilestoneNeeded(final NextMilestoneNeededEvent event) {
        log.info(
                "Creating milestone {} for enrollment: {}",
                event.getNextIteration(),
                event.getEnrollment().getJourneyEnrollmentId());

        if (event.getNextIteration()
                == event.getEnrollment()
                                .getJourneyProgram()
                                .getJourneyProgramBehaviour()
                                .getProgramDuration()
                        + 1) {
            eventPublisher.publish(new ProgramExpiredEvent(event.getEnrollment(), event.getOccurredAt()));
            return;
        }

        final var activities = programService.getMilestoneActivities(
                event.getEnrollment().getJourneyProgram().getJourneyProgramId(),
                event.getEnrollment().getEntityId(),
                event.getNextIteration());

        milestoneService.createMilestonesFromTemplates(
                event.getEnrollment(), activities, event.getStartTime(), event.getNextIteration());
    }

    @EventListener
    @Transactional
    public void handleMilestoneCompleted(final MilestoneCompletedEvent event) {
        log.info("Milestone {} completed", event.getMilestone().getJourneyEnrollmentMilestoneId());

        milestoneService.markAsCompleted(event.getMilestone(), event.getCompletedAt());

        // Check if program is complete
        if (enrollmentService.isProgramComplete(event.getEnrollment().getJourneyEnrollmentId())) {
            eventPublisher.publish(new ProgramCompletedEvent(
                    event.getEnrollment(), event.getMilestone().getMilestoneTo()));
        } else {
            // Need next milestone
            eventPublisher.publish(new NextMilestoneNeededEvent(
                    event.getEnrollment(),
                    event.getMilestone().getMilestoneIteration() + 1,
                    event.getMilestone().getMilestoneTo()));
        }
    }

    @EventListener
    @Transactional
    public void handleMilestoneLateCompleted(final MilestoneLateCompletedEvent event) {
        log.info("Milestone {} Late completed", event.getMilestone().getJourneyEnrollmentMilestoneId());

        milestoneService.markAsLateCompleted(event.getMilestone(), event.getCompletedAt());

        if (enrollmentService.isProgramComplete(event.getEnrollment().getJourneyEnrollmentId())) {
            eventPublisher.publish(new ProgramLateCompletedEvent(
                    event.getEnrollment(), event.getMilestone().getMilestoneTo()));
        }
    }
}
