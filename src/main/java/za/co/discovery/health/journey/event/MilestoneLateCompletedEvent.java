package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;

import java.time.LocalDateTime;

@Getter
public class MilestoneLateCompletedEvent extends JourneyEvent {
    private final JourneyEnrollmentMilestone milestone;
    private final JourneyEnrollment enrollment;
    private final LocalDateTime completedAt;

    public MilestoneLateCompletedEvent(
            final JourneyEnrollmentMilestone milestone,
            final JourneyEnrollment enrollment,
            final LocalDateTime completedAt) {
        super(enrollment.getEntityId(), LocalDateTime.now());
        this.milestone = milestone;
        this.enrollment = enrollment;
        this.completedAt = completedAt;
    }
}
