package za.co.discovery.health.journey.messaging.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NotificationTemplate {
    private Long destinationEntityId;
    private String type;
    private Map<String, String> attributes;
    private AttendeeType attendeeType;
}
