package za.co.discovery.health.journey.config.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import za.co.discovery.health.dataattribute.ApiClient;
import za.co.discovery.health.dataattribute.api.DataControllerApi;

import java.text.SimpleDateFormat;
import java.util.TimeZone;

@Configuration
public class DataAttributeServiceClientConfig {
    private static final String DATA_ATTRIBUTES_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    private final WebClient webClient;

    @Value("${integration.data-attribute-service.url}")
    private String baseUrl;

    public DataAttributeServiceClientConfig(@Qualifier("b2b-webclient") final WebClient b2bWebClient) {
        this.webClient = b2bWebClient;
    }

    @Bean
    public DataControllerApi dataAttributeServiceControllerApi() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATA_ATTRIBUTES_DATE_FORMAT);
        dateFormat.setTimeZone(TimeZone.getDefault());

        ObjectMapper mapper = ApiClient.createDefaultObjectMapper(dateFormat);

        final ApiClient apiClient = new ApiClient(webClient, mapper, dateFormat);
        apiClient.setBasePath(baseUrl);
        return new DataControllerApi(apiClient);
    }
}
