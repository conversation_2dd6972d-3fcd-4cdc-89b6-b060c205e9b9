package za.co.discovery.health.journey.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Getter
@RequiredArgsConstructor
public enum MilestoneStatus {
    ACTIVE("ACTIVE"),
    LOCKED("LOCKED"),
    COMPLETED("COMPLETED"),
    LATE_COMPLETED("LATE_COMPLETED"),
    NOT_ACHIEVED("NOT_ACHIEVED"),
    SKIP("SKIP"),
    SKIPPED("SKIPPED"),
    ;

    private final String value;

    public static MilestoneStatus fromValue(final String value) {
        for (final MilestoneStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown milestone status: " + value);
    }

    public static List<MilestoneStatus> completedMilestoneStatuses() {
        return List.of(COMPLETED, SKIPPED, NOT_ACHIEVED);
    }
}
