package za.co.discovery.health.journey;

import lombok.NoArgsConstructor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableCaching
@EnableScheduling
@NoArgsConstructor
@ConfigurationPropertiesScan
@SpringBootApplication(scanBasePackages = {"za.co.discovery.health"})
public class JourneyApplication extends SpringBootServletInitializer {
    public static void main(final String[] args) {
        SpringApplication.run(JourneyApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(final SpringApplicationBuilder application) {
        return application.sources(JourneyApplication.class);
    }
}
