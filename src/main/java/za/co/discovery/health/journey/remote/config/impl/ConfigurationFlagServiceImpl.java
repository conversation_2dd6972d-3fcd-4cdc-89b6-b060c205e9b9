package za.co.discovery.health.journey.remote.config.impl;

import com.za.disocvery.health.configflag.api.ConfigurationFlagControllerApi;
import com.za.disocvery.health.configflag.domain.ConfigurationFlagDto;
import com.za.disocvery.health.configflag.domain.ConfigurationFlagRequest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.remote.config.ConfigurationFlagService;
import za.co.discovery.health.journey.util.Constants;
import za.co.discovery.health.journey.util.model.Audience;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ConfigurationFlagServiceImpl implements ConfigurationFlagService {
    private final ConfigurationFlagControllerApi b2bConfigurationFlagControllerApi;

    public ConfigurationFlagServiceImpl(
            @Qualifier("b2bconfigurationFlagControllerApi")
                    final ConfigurationFlagControllerApi b2bConfigurationFlagControllerApi) {
        this.b2bConfigurationFlagControllerApi = b2bConfigurationFlagControllerApi;
    }

    @Override
    public List<ConfigurationFlagDto> getConfigFlags(final long entityNo) {

        final ConfigurationFlagRequest configurationFlagRequest = new ConfigurationFlagRequest()
                .includeAudience(true)
                .includeDasAttributes(true)
                .includeControlCenterFlags(false)
                .includeVapConfig(true)
                .includeEmployeeIncentivePlan(false)
                .controlCenterCached(false)
                .dasCached(true)
                .entityId(entityNo);
        final List<ConfigurationFlagDto> configurationFlags =
                b2bConfigurationFlagControllerApi.getConfigurationFlags(configurationFlagRequest);
        if (configurationFlags == null || configurationFlags.isEmpty()) {
            return List.of();
        }
        configurationFlags.add(
                new ConfigurationFlagDto().key(Constants.ENTITY_NO).value(String.valueOf(entityNo)));
        return configurationFlags;
    }

    @Override
    public Audience getAudience(final long entityNo) {
        final ConfigurationFlagRequest configurationFlagRequest = new ConfigurationFlagRequest()
                .includeAudience(true)
                .includeDasAttributes(false)
                .includeControlCenterFlags(false)
                .includeVapConfig(false)
                .includeEmployeeIncentivePlan(false)
                .entityId(entityNo);

        final Map<String, Object> collect =
                b2bConfigurationFlagControllerApi.getConfigurationFlags(configurationFlagRequest).stream()
                        .filter(it -> it.getValue() != null)
                        .collect(Collectors.toMap(
                                ConfigurationFlagDto::getKey, ConfigurationFlagDto::getValue, (a, b) -> b));

        return Audience.builder()
                .allianceNo(String.valueOf(collect.get(Constants.ALLIANCE)))
                .branchNo(String.valueOf(collect.get(Constants.BRANCH)))
                .groupNo(String.valueOf(collect.get(Constants.GROUP)))
                .role(String.valueOf(collect.get(Constants.ROLE)))
                .build();
    }
}
