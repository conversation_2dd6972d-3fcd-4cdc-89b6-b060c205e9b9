package za.co.discovery.health.journey.messaging.model.payoff;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.messaging.model.payoff.constant.RewardTypeEnum;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class PayoffEvent {
    private Long entityNo;
    private String activity;
    private String program;
    private Long uniqueId;
    private RewardTypeEnum rewardTypeEnum;
    private String rewardSource;
    private String awardingEventDescription;

    @Override
    public String toString() {
        return "PayoffEvent{"
                + "entityNo="
                + entityNo
                + ", activity='"
                + activity
                + '\''
                + ", program='"
                + program
                + '\''
                + ", uniqueId="
                + uniqueId
                + ", rewardTypeEnum="
                + rewardTypeEnum
                + ", rewardSource='"
                + rewardSource
                + ", awardingEventDescription='"
                + awardingEventDescription
                + '\''
                + '}';
    }
}
