package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@UtilityClass
@Slf4j
public class JourneyConfigExtractionUtils {
    public static String extractAppointmentCountFromRule(final String ruleExpression) {
        if (ruleExpression == null || ruleExpression.isBlank()) {
            return "0";
        }

        if (!ruleExpression.trim().startsWith("<?xml")) {
            return extractFromJexlExpression(ruleExpression);
        }
        return extractFromDmnXml(ruleExpression);
    }

    public static LocalDateTime getEndDateForProgram(final JourneyProgram program, final LocalDateTime startDate) {
        return startDate.plus(
                program.getJourneyProgramBehaviour().getProgramDuration(),
                ChronoUnit.valueOf(program.getJourneyProgramBehaviour()
                        .getJourneyMilestone()
                        .getDescription()));
    }

    private String extractFromJexlExpression(final String jexlExpression) {
        final Pattern mnemonicsAtLeastPattern =
                Pattern.compile("program\\.hasCompletedMnemonicsAtLeast\\s*\\([^,]+,\\s*(\\d+)\\s*,");

        final Matcher matcher = mnemonicsAtLeastPattern.matcher(jexlExpression);
        if (matcher.find()) {
            return matcher.group(1);
        }

        final Pattern appointmentPattern =
                Pattern.compile("program\\.hasCompletedAtLeastAppointments\\s*\\([^,]*,?\\s*(\\d+)\\s*\\)");

        final Matcher appointmentMatcher = appointmentPattern.matcher(jexlExpression);
        if (appointmentMatcher.find()) {
            return appointmentMatcher.group(1);
        }

        log.warn("Could not extract appointment count from JEXL expression: {}", jexlExpression);
        return "0";
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    private String extractFromDmnXml(final String dmnXml) {
        try {
            final Pattern appointmentRulePattern =
                    Pattern.compile("<text>\"APPOINTMENT\"</text>", Pattern.CASE_INSENSITIVE);

            final Matcher matcher = appointmentRulePattern.matcher(dmnXml);
            int appointmentCount = 0;
            while (matcher.find()) {
                appointmentCount++;
            }
            return String.valueOf(appointmentCount);
        } catch (Exception e) {
            log.warn("Could not parse DMN XML for appointment count: {}", e.getMessage());
            return "0";
        }
    }
}
