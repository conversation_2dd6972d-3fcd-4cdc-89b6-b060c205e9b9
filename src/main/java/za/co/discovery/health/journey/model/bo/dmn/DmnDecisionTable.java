package za.co.discovery.health.journey.model.bo.dmn;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Setter
@Getter
@SuppressWarnings("PMD")
public class DmnDecisionTable {
    private String id;
    private String name;
    private String hitPolicy = "FIRST";
    private String aggregation; // For COLLECT hit policy
    private LinkedHashMap<String, String> inputColumns;
    private LinkedHashMap<String, String> outputColumns;
    private List<DecisionRule> rules;

    public DmnDecisionTable() {
        this.inputColumns = new LinkedHashMap<>();
        this.outputColumns = new LinkedHashMap<>();
        this.rules = new ArrayList<>();
    }
}
