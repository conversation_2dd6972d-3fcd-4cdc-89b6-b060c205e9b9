package za.co.discovery.health.journey.messaging.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityCompletionRequestMessage {
    private long entityNo;
    private String mnemonic;
    private LocalDate date;
    private String activityValue;
}
