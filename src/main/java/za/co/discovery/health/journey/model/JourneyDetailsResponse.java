package za.co.discovery.health.journey.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.preview.LimitedUserCategoryEnrollmentPreview;
import za.co.discovery.health.journey.model.user.UserProgramEnrollmentDto;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class JourneyDetailsResponse extends LimitedUserCategoryEnrollmentPreview {
    private LocalDateTime monitoringPeriodEndTime;

    private List<UserProgramEnrollmentDto> journeyPrograms;
}
