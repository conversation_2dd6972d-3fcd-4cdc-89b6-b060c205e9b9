package za.co.discovery.health.journey.resolver.rule.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ActivityCompletionRuleRequest extends RuleRequest {
    private final Long iteration;
    private final String activityMnemonic;
}
