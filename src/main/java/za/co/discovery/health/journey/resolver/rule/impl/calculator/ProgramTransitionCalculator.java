package za.co.discovery.health.journey.resolver.rule.impl.calculator;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ProgramTransitionRuleEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.request.ProgramTransitionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetProgramTransitionRuleResult;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.util.DMNConstants;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class ProgramTransitionCalculator implements RuleCalculator {
    private final RuleCacheService cacheService;
    private final RuleEvaluatorResolver evaluator;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.PROGRAM_COMPLETION_RULE;
    }

    @Override
    public GetProgramTransitionRuleResult calculate(final RuleType type, final RuleRequest ruleRequest) {
        if (!(ruleRequest instanceof ProgramTransitionRuleRequest)) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Expected an ProgramTransitionRuleRequest");
        }
        final ProgramTransitionRuleRequest programRequest = (ProgramTransitionRuleRequest) ruleRequest;

        final RuleCache ruleCache = cacheService.getRuleCache(ruleRequest.getRuleName());
        final Map<String, Object> vars = Map.of(DMNConstants.PROGRAM_TRANSITION, programRequest);

        // PREPARE VARIABLES
        final ProgramTransitionRuleEvaluationResult evaluate = (ProgramTransitionRuleEvaluationResult) evaluator
                .getEvaluator(type, ruleCache.getProcessorType())
                .evaluate(ruleRequest.getEntityId(), ruleCache, vars);

        if (evaluate.isSuccess()) {
            return GetProgramTransitionRuleResult.builder()
                    .success(true)
                    .canTransition(evaluate.isCanTransition())
                    .build();
        } else {
            return GetProgramTransitionRuleResult.builder()
                    .success(false)
                    .canTransition(false)
                    .build();
        }
    }
}
