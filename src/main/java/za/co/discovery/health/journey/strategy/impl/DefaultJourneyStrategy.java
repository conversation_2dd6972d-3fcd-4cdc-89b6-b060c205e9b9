package za.co.discovery.health.journey.strategy.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.rule.conversion.ConfigFlagJexlRunner;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentService;
import za.co.discovery.health.journey.strategy.EnrollmentValidator;
import za.co.discovery.health.journey.strategy.JourneyTypeStrategy;
import za.co.discovery.health.journey.strategy.MilestoneManager;
import za.co.discovery.health.journey.util.Constants;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultJourneyStrategy implements JourneyTypeStrategy {
    private final JourneyEnrollmentService enrollmentService;
    private final ConfigFlagJexlRunner jexlRunner;

    @Override
    public String getProgressType() {
        return "DEFAULT"; // This will be used for all existing journeys
    }

    @Override
    public EnrollmentValidator getEnrollmentValidator() {
        return new DefaultEnrollmentValidator();
    }

    @Override
    public MilestoneManager getMilestoneManager() {
        return new DefaultMilestoneManager();
    }

    @RequiredArgsConstructor
    private class DefaultEnrollmentValidator implements EnrollmentValidator {

        @Override
        public boolean canEnroll(
                final Long userId,
                final JourneyCategory category,
                final CategoryConfigurationRuleResult.CategoryConfiguration config,
                final LocalDateTime now) {
            // Replicate isJourneyInvalid logic (inverted)
            if (isEnrollmentWindowClosed(config, now)) {
                return false;
            }

            if (config.getMaxParticipants() != null) {
                final long currentCount = enrollmentService.countMembersInCategory(category.getJourneyCategoryId());
                if (currentCount >= config.getMaxParticipants()) {
                    log.info("Category {} is full for user {}", category.getJourneyCategoryId(), userId);
                    return false;
                }
            }

            return !hasPreconditionFailed(userId, config);
        }

        @Override
        public LocalDateTime calculateJourneyStartTime(
                final CategoryConfigurationRuleResult.CategoryConfiguration config, final LocalDateTime now) {
            // Replicate determineJourneyStartTime
            if (config.getJourneyStartTime() == null) {
                return now;
            }
            return now.isAfter(config.getJourneyStartTime()) ? now : config.getJourneyStartTime();
        }

        private boolean isEnrollmentWindowClosed(
                final CategoryConfigurationRuleResult.CategoryConfiguration category, final LocalDateTime now) {
            if (category.getEnrollmentEndTime() != null && now.isAfter(category.getEnrollmentEndTime())) {
                return true;
            }
            return category.getEnrollmentStartTime() != null && now.isBefore(category.getEnrollmentStartTime());
        }

        private boolean hasPreconditionFailed(
                final Long userId, final CategoryConfigurationRuleResult.CategoryConfiguration category) {
            return Objects.nonNull(category.getEnrollmentPrecondition())
                    && !jexlRunner.run(
                            userId,
                            category.getEnrollmentPrecondition(),
                            Map.of(Constants.ENROLLMENT_START_TIME, category.getEnrollmentStartTime()));
        }
    }

    private static class DefaultMilestoneManager implements MilestoneManager {

        @Override
        public boolean isMilestoneExpired(final JourneyEnrollmentMilestone milestone, final LocalDateTime now) {
            return milestone.getMilestoneTo() != null && now.isAfter(milestone.getMilestoneTo());
        }

        @Override
        public boolean shouldBackfillMissedMilestones() {
            return true;
        }
    }
}
