package za.co.discovery.health.journey.model.bo.dmn;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryConfigurationDmnRuleDto {
    // Rule identification
    private String ruleId;
    private String description;

    // Input fields
    private String alliance;
    private String branch;
    private String group;

    // Output fields
    private String journeyStartTime;
    private String activityPrecondition;
    private String enrollmentPrecondition;
    private String enrollmentStartTime;
    private String enrollmentEndTime;
    private Integer maxParticipants;
}
