package za.co.discovery.health.journey.strategy.precondition.validator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.strategy.precondition.ActivityCompletionPreconditionStrategy;

import javax.annotation.PostConstruct;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ActivityPreconditionStrategyRegistry {

    private final List<ActivityCompletionPreconditionStrategy> strategies;
    private final Map<ActivityCompletionPreconditionType, ActivityCompletionPreconditionStrategy> strategyMap =
            new HashMap<>();

    @PostConstruct
    public void initializeRegistry() {
        strategyMap.putAll(strategies.stream()
                .collect(Collectors.toMap(ActivityCompletionPreconditionStrategy::getType, Function.identity())));

        log.info("Initialized {} precondition strategies: {}", strategyMap.size(), strategyMap.keySet());
    }

    public ActivityCompletionPreconditionStrategy getStrategy(final ActivityCompletionPreconditionType type) {
        final ActivityCompletionPreconditionStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("No strategy found for precondition type: " + type);
        }
        return strategy;
    }

    public boolean hasStrategy(final ActivityCompletionPreconditionType type) {
        return strategyMap.containsKey(type);
    }
}
