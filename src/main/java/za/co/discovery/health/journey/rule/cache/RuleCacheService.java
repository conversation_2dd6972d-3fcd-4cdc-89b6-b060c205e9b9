package za.co.discovery.health.journey.rule.cache;

import lombok.RequiredArgsConstructor;
import org.camunda.bpm.dmn.engine.DmnDecision;
import org.camunda.bpm.dmn.engine.DmnDecisionRuleResult;
import org.camunda.bpm.dmn.engine.DmnDecisionTableResult;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.rule.conversion.JexlRunner;
import za.co.discovery.health.journey.rule.conversion.service.RuleJExlService;
import za.co.discovery.health.journey.rule.dmn.service.RuleDMNService;

import javax.annotation.PostConstruct;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class RuleCacheService {

    private final DmnEngine dmnEngine;
    private final JexlRunner jexlRunner;
    private final RuleDMNService ruleDMNService;
    private final RuleJExlService jexlService;
    private final Map<String, RuleCache> decisionCache = new ConcurrentHashMap<>();
    private final Map<Long, RuleCache> ruleCacheMapById = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        populateDmnCache();
        populateJexlCache();
    }

    private void populateJexlCache() {
        jexlService.getAll().forEach(journeyJExlModel -> {
            decisionCache.put(
                    journeyJExlModel.getName(),
                    RuleCache.builder()
                            .processorType(RuleProcessorType.JEXL)
                            .ruleContent(journeyJExlModel.getRule())
                            .build());

            ruleCacheMapById.put(
                    journeyJExlModel.getId(),
                    RuleCache.builder()
                            .processorType(RuleProcessorType.JEXL)
                            .ruleContent(journeyJExlModel.getRule())
                            .build());
        });
    }

    private void populateDmnCache() {
        ruleDMNService.getAll().forEach(dmnFileContent -> {
            try (InputStream inputStream = new ByteArrayInputStream(dmnFileContent.getContent())) {

                final DmnDecision decision = dmnEngine.parseDecision("decision", inputStream);
                decisionCache.put(
                        dmnFileContent.getName(),
                        RuleCache.builder()
                                .processorType(RuleProcessorType.DMN)
                                .ruleContent(new String(dmnFileContent.getContent(), StandardCharsets.UTF_8))
                                .decision(decision)
                                .build());

                ruleCacheMapById.put(
                        dmnFileContent.getId(),
                        RuleCache.builder()
                                .processorType(RuleProcessorType.DMN)
                                .ruleContent(new String(dmnFileContent.getContent(), StandardCharsets.UTF_8))
                                .decision(decision)
                                .build());
            } catch (IOException e) {
                throw new CoreException(ReasonCode.GENERAL_ERROR, "Error while evaluating DMN decision", e);
            }
        });
    }

    public RuleCache getRuleCache(final String ruleName) {
        return decisionCache.get(ruleName);
    }

    public RuleCache getRuleCache(final Long id) {
        return ruleCacheMapById.get(id);
    }

    public DmnDecisionRuleResult evaluateDmn(final RuleCache ruleCache, final Map<String, Object> variables) {
        if (ruleCache == null) {
            throw new CoreException(ReasonCode.GENERAL_ERROR, "No Rule Cache found.");
        }
        if (ruleCache.getProcessorType() != RuleProcessorType.DMN) {
            throw new CoreException(ReasonCode.GENERAL_ERROR, "Rule Cache is not a DMN Rule.");
        }

        final DmnDecisionTableResult decisionTableResult =
                dmnEngine.evaluateDecisionTable(ruleCache.getDecision(), variables);
        return decisionTableResult.getSingleResult();
    }

    public boolean evaluateJexl(final RuleCache ruleCache, final Map<String, Object> variables) {
        if (ruleCache == null) {
            throw new CoreException(ReasonCode.GENERAL_ERROR, "No Rule Cache found.");
        }
        if (ruleCache.getProcessorType() != RuleProcessorType.JEXL) {
            throw new CoreException(ReasonCode.GENERAL_ERROR, "Rule Cache is not a JEXL Rule.");
        }
        return jexlRunner.run(ruleCache.getRuleContent(), variables);
    }
}
