package za.co.discovery.health.journey.database.repository;

import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyRulesRepository extends JourneyRulesRepository {

    Optional<JourneyRules> findByRuleSetName(String ruleSetName); // Find by rule set name

    void deleteAllByRuleSetNameAndRuleSetType(String ruleSetName, String type); // Find by rule set name

    Optional<JourneyRules> findByRuleSetNameAndRuleSetType(String name, String type);

    List<JourneyRules> findAllByRuleSetType(String ruleSetType);
}
