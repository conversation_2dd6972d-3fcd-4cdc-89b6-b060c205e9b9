package za.co.discovery.health.journey.config.cache;

import lombok.RequiredArgsConstructor;
import net.sf.ehcache.config.CacheConfiguration;
import net.sf.ehcache.management.ManagementService;
import org.springframework.cache.CacheManager;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import za.co.discovery.health.journey.config.cache.model.DbCacheProperties;

import javax.management.MBeanServer;

import java.lang.management.ManagementFactory;

@Configuration
@Profile("db-cache")
@RequiredArgsConstructor
public class CacheConfig {

    @Bean(destroyMethod = "shutdown")
    public net.sf.ehcache.CacheManager ehCacheManager(final DbCacheProperties dbCacheProperties) {

        final CacheConfiguration cacheConfiguration = new CacheConfiguration();
        cacheConfiguration.setName(dbCacheProperties.getName());
        cacheConfiguration.setMemoryStoreEvictionPolicy(dbCacheProperties.getMemoryStoreEvictionPolicy());
        cacheConfiguration.setMaxEntriesLocalHeap(dbCacheProperties.getMaxEntriesLocalHeap());

        final net.sf.ehcache.config.Configuration config = new net.sf.ehcache.config.Configuration();
        config.addCache(cacheConfiguration);

        return net.sf.ehcache.CacheManager.newInstance(config);
    }

    @Bean
    public CacheManager cacheManager(final net.sf.ehcache.CacheManager cacheManager) {
        final MBeanServer beanServer = ManagementFactory.getPlatformMBeanServer();
        ManagementService.registerMBeans(cacheManager, beanServer, false, false, false, true);
        return new EhCacheCacheManager(cacheManager);
    }
}
