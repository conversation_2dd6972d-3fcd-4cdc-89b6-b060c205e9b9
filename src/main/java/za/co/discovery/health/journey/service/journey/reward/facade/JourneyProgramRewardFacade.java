package za.co.discovery.health.journey.service.journey.reward.facade;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.projection.RewardProjection;
import za.co.discovery.health.journey.remote.config.ConfigurationFlagService;
import za.co.discovery.health.journey.service.journey.reward.ProgramRewardService;
import za.co.discovery.health.journey.util.model.Audience;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class JourneyProgramRewardFacade {
    private final ProgramRewardService service;
    private final ConfigurationFlagService configurationFlagService;

    public List<RewardProjection> getProgramRewards(final JourneyEnrollment enrollment, final LocalDateTime time) {
        return getProgramRewards(
                enrollment.getEntityId(), enrollment.getJourneyProgram().getJourneyProgramId(), time);
    }

    public List<RewardProjection> getProgramRewards(
            final long entityId, final long programId, final LocalDateTime time) {
        final Audience audience = configurationFlagService.getAudience(entityId);

        return service.getProgramRewards(audience, programId, time);
    }

    public List<RewardProjection> getMilestoneReward(
            final JourneyEnrollmentMilestone milestone, final LocalDateTime time) {
        final JourneyEnrollment enrollment = milestone.getJourneyEnrollment();

        return getMilestoneReward(
                enrollment.getEntityId(),
                milestone.getMilestoneIteration(),
                enrollment.getJourneyProgram().getJourneyProgramId(),
                time);
    }

    public List<RewardProjection> getMilestoneReward(
            final long entityId, final long iteration, final long programId, final LocalDateTime time) {
        final Audience audience = configurationFlagService.getAudience(entityId);

        return service.getMilestoneRewards(audience, iteration, programId, time);
    }
}
