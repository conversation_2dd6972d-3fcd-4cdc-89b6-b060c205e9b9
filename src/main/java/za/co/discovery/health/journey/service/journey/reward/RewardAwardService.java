package za.co.discovery.health.journey.service.journey.reward;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import za.co.discovery.health.hs.starter.exception.NotFoundException;
import za.co.discovery.health.journey.constant.JourneyRewardAwardStatusEnum;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneAward;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentProgramAward;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneAwardRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentProgramAwardRepository;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class RewardAwardService {
    private final ExtendedJourneyEnrollmentMilestoneAwardRepository journeyEnrollmentMilestoneAwardRepository;
    private final ExtendedJourneyEnrollmentProgramAwardRepository journeyEnrollmentProgramAwardRepository;

    @Transactional
    public List<JourneyEnrollmentMilestoneAward> getAllMilestoneRewardsByStatus(
            final JourneyRewardAwardStatusEnum status) {
        return journeyEnrollmentMilestoneAwardRepository.findMilestoneAwardsByAwardStatus(status.name());
    }

    @Transactional
    public List<JourneyEnrollmentProgramAward> getAllProgramRewardsByStatus(final JourneyRewardAwardStatusEnum status) {
        return journeyEnrollmentProgramAwardRepository.findProgramAwardsByAwardStatus(status.name());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void markMilestoneAwardAsProcessed(final Long id) {
        try {
            final JourneyEnrollmentMilestoneAward awardCopy = journeyEnrollmentMilestoneAwardRepository
                    .findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            String.format("No JourneyEnrollmentMilestoneAward found for id %d", id)));

            awardCopy.setAwardStatus(JourneyRewardAwardStatusEnum.PROCESSED.name());
            journeyEnrollmentMilestoneAwardRepository.save(awardCopy);
        } catch (RestClientException e) {
            throw new IllegalStateException(String.format("Failed to award Program Reward with id %d", id), e);
        } catch (DataIntegrityViolationException e) {
            throw new IllegalStateException(
                    String.format(
                            "Failed to award Program Reward with id %d due to a database integrity violation", id),
                    e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void markProgramAwardAsProcessed(final Long id) {
        try {
            final JourneyEnrollmentProgramAward awardCopy = journeyEnrollmentProgramAwardRepository
                    .findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            String.format("No JourneyEnrollmentMilestoneAward found for id %d", id)));

            awardCopy.setAwardStatus(JourneyRewardAwardStatusEnum.PROCESSED.name());
            journeyEnrollmentProgramAwardRepository.save(awardCopy);
        } catch (RestClientException e) {
            throw new IllegalStateException(String.format("Failed to award Program Reward with id %d", id), e);
        } catch (DataIntegrityViolationException e) {
            throw new IllegalStateException(
                    String.format(
                            "Failed to award Program Reward with id %d due to a database integrity violation", id),
                    e);
        }
    }
}
