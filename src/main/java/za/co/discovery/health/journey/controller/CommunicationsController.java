package za.co.discovery.health.journey.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import za.co.discovery.health.journey.facade.JourneyCommunicationsFacade;
import za.co.discovery.health.journey.model.communication.JourneyCategoryCommunicationDataPoint;

/**
 * REST controller for communications.
 * Delegates business logic to the CommunicationsFacade.
 */
@RestController
@RequestMapping("/api/communication")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "Communication Data Points", description = "API to retrieve Journey Communication Data Points")
public class CommunicationsController {
    private final JourneyCommunicationsFacade communicationsFacade;

    @GetMapping("/journey")
    @Operation(
            summary = "Get communication data points",
            description = "Gets communication data points based on the journey ID")
    @ApiResponses(
            value = {
                @ApiResponse(responseCode = "200", description = "Data points retrieved successfully"),
                @ApiResponse(responseCode = "400", description = "Invalid request data"),
                @ApiResponse(responseCode = "500", description = "Internal server error")
            })
    public Mono<JourneyCategoryCommunicationDataPoint> getCommunicationDataPoints(
            @RequestParam final Long entityId, final Long categoryId) {
        return communicationsFacade.getCommunicationDataPoints(entityId, categoryId);
    }
}
