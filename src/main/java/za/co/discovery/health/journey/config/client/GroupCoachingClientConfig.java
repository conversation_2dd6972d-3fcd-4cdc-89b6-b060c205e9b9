package za.co.discovery.health.journey.config.client;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import za.co.discovery.health.group.coaching.ApiClient;
import za.co.discovery.health.group.coaching.api.AppointmentControllerApi;

@Configuration
@RequiredArgsConstructor
public class GroupCoachingClientConfig {
    private final WebClient webClient;

    @Value("${integration.group-coaching.url}")
    private String baseUrl;

    @Bean
    public AppointmentControllerApi appointmentControllerApi() {
        final ApiClient apiClient = getApiClient();
        return new AppointmentControllerApi(apiClient);
    }

    private ApiClient getApiClient() {
        final ApiClient apiClient = new ApiClient(webClient);
        apiClient.setBasePath(baseUrl);
        return apiClient;
    }
}
