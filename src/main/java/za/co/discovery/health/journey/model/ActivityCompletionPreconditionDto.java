package za.co.discovery.health.journey.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityCompletionRuleResult;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCompletionPreconditionDto {

    private Long iteration;
    private String identifier;
    private ActivityCompletionPreconditionType preconditionType;

    public static ActivityCompletionPreconditionDto of(
            final GetActivityCompletionRuleResult.ActivityCompletionRules rules) {
        return ActivityCompletionPreconditionDto.builder()
                .identifier(rules.getIdentifier())
                .iteration(rules.getIteration())
                .preconditionType(rules.getPreconditionType())
                .build();
    }
}
