// package za.co.discovery.health.journey.config.state;
//
// import org.springframework.context.annotation.Configuration;
// import org.springframework.statemachine.config.EnableStateMachineFactory;
// import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
// import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
// import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
//
// import java.util.EnumSet;
//
// @Configuration
// @EnableStateMachineFactory
// public class EnrollmentStateMachineConfig
//    extends EnumStateMachineConfigurerAdapter<EnrollmentState, EnrollmentEvent> {
//
//    @Override
//    public void configure(StateMachineStateConfigurer<EnrollmentState, EnrollmentEvent> states)
//        throws Exception {
//        states
//            .withStates()
//            .initial(EnrollmentState.INITIAL)
//            .states(EnumSet.allOf(EnrollmentState.class))
//            .end(EnrollmentState.COMPLETED)
//            .end(EnrollmentState.EXPIRED);
//    }
//
//    @Override
//    public void configure(StateMachineTransitionConfigurer<EnrollmentState, EnrollmentEvent>
// transitions)
//        throws Exception {
//        transitions
//            .withExternal()
//            .source(EnrollmentState.INITIAL)
//            .target(EnrollmentState.ACTIVE)
//            .event(EnrollmentEvent.INITIALIZE)
//
//            .and()
//            .withExternal()
//            .source(EnrollmentState.ACTIVE)
//            .target(EnrollmentState.EVALUATING)
//            .event(EnrollmentEvent.EVALUATE)
//
//            .and()
//            .withExternal()
//            .source(EnrollmentState.EVALUATING)
//            .target(EnrollmentState.ACTIVE)
//            .event(EnrollmentEvent.CONTINUE)
//
//            .and()
//            .withExternal()
//            .source(EnrollmentState.EVALUATING)
//            .target(EnrollmentState.COMPLETED)
//            .event(EnrollmentEvent.COMPLETE);
//    }
// }
