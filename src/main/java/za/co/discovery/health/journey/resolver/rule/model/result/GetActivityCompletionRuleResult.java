package za.co.discovery.health.journey.resolver.rule.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.resolver.rule.model.RuleResult;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ActivityCompletionRuleEvaluationResult;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class GetActivityCompletionRuleResult extends RuleResult {
    private final List<ActivityCompletionRules> rules;

    @Data
    @Builder
    @AllArgsConstructor
    public static class ActivityCompletionRules {
        private final String identifier;

        private final Long iteration;

        private final ActivityCompletionPreconditionType preconditionType;

        public static ActivityCompletionRules of(
                final ActivityCompletionRuleEvaluationResult.ActivityCompletionResult output) {
            return ActivityCompletionRules.builder()
                    .identifier(output.getIdentifier())
                    .iteration(output.getIteration())
                    .preconditionType(output.getPreconditionType())
                    .build();
        }
    }
}
