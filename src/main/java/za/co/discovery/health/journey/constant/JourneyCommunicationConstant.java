package za.co.discovery.health.journey.constant;

public enum JourneyCommunicationConstant {
    TOPIC_NAME("TopicName"),
    START_DATE("StartDate"),
    END_DATE("EndDate"),
    ENROLLMENT_START_DATE("EnrollmentStartDate"),
    ENROLLMENT_END_DATE("EnrollmentEndDate"),
    DAY_OF_WEEK("DayOfWeekOfCoachingSessions"),
    ATTEND_SESSION("AttendSession"),
    MAX_SESSION("MaxSession"),
    REWARD_VALUE("RewardValue"),
    REWARD_TYPE("RewardType"),
    PROGRAM_DESCRIPTION("ProgramDescription"),
    JOURNEY_ID("JourneyId"),
    JOURNEY_EXTERNAL_REFERENCE_ID("ExternalReferenceId"),
    PROGRAM_SENTENCE_DESCRIPTION("ProgramSentenceDescription");

    JourneyCommunicationConstant(final String name) {
        // next line
    }
}
