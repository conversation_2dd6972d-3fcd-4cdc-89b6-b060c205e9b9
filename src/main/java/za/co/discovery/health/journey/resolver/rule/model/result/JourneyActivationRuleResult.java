package za.co.discovery.health.journey.resolver.rule.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.RuleResult;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.JourneyActivationEvaluationResult;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class JourneyActivationRuleResult extends RuleResult {
    private final List<CategoryRecommendation> categories;

    @Data
    @Builder
    @AllArgsConstructor
    public static class CategoryRecommendation {
        private final String category;

        public static CategoryRecommendation of(
                final JourneyActivationEvaluationResult.CategoryRecommendationResult output) {
            return CategoryRecommendation.builder()
                    .category(output.getCategory())
                    .build();
        }
    }
}
