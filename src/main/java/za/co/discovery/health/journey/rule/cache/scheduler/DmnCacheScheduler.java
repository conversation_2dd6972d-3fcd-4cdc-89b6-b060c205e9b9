package za.co.discovery.health.journey.rule.cache.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;

@Slf4j
@Component
@RequiredArgsConstructor
public class DmnCacheScheduler {
    private final RuleCacheService cacheService;

    @Scheduled(
            initialDelayString = "${scheduler.rule.initial-delay:PT10M}",
            fixedDelayString = "${scheduler.rule.fixed-delay:PT10M}")
    public void refreshCache() {
        log.info("Refreshing DMN cache...");
        cacheService.init();
    }
}
