package za.co.discovery.health.journey.resolver.rule.model.output;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import za.co.discovery.health.journey.util.DMNConstants;
import za.co.discovery.health.journey.util.DMNUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@Getter
@Builder(access = AccessLevel.PRIVATE)
public class CategoryConfigurationDMNRuleOutput {

    private final DMNOutput output;

    /**
     * Creates an {@link CategoryConfigurationDMNRuleOutput} from a list of result maps.
     * Each map is expected to contain keys defined in {@link DMNConstants}.
     *
     * @param result List of maps containing DMN rule output data
     * @return An instance of {@link CategoryConfigurationDMNRuleOutput}, or an empty one if invalid input
     */
    public static CategoryConfigurationDMNRuleOutput of(final List<Map<String, Object>> result) {
        // No match found or timePeriod not configured
        if (result == null || result.isEmpty()) {
            return CategoryConfigurationDMNRuleOutput.builder().output(null).build();
        }

        final DMNOutput outputs = result.stream()
                .map(CategoryConfigurationDMNRuleOutput::convertToDMNOutput)
                .findFirst()
                .orElse(null);

        return CategoryConfigurationDMNRuleOutput.builder().output(outputs).build();
    }

    /**
     * Converts a single map of DMN data to a {@link DMNOutput} object.
     *
     * @param categoryMap Map containing activity data
     * @return {@link DMNOutput} built from the map; never null (fields may be null)
     */
    private static DMNOutput convertToDMNOutput(final Map<String, Object> categoryMap) {
        // Safely convert map values to the correct data types
        final String enrollmentStartTime =
                DMNUtils.toStringOrNull(categoryMap.get(DMNConstants.JOURNEY_ENROLLMENT_START_TIME));
        final String enrollmentEndTime =
                DMNUtils.toStringOrNull(categoryMap.get(DMNConstants.JOURNEY_ENROLLMENT_END_TIME));
        final String startTime = DMNUtils.toStringOrNull(categoryMap.get(DMNConstants.JOURNEY_START_TIME));
        final String activityPrecondition =
                DMNUtils.toStringOrEmpty(categoryMap.get(DMNConstants.JOURNEY_ACTIVITY_PRECONDITION));
        final String enrollmentPrecondition =
                DMNUtils.toStringOrEmpty(categoryMap.get(DMNConstants.JOURNEY_ENROLLMENT_PRECONDITION));

        final Long maxParticipants = DMNUtils.toLongOrNull(categoryMap.get(DMNConstants.JOURNEY_MAX_PARTICIPANTS));

        final Long monitoringPeriodDuration =
                DMNUtils.toLongOrNull(categoryMap.get(DMNConstants.JOURNEY_MONITORING_PERIOD_DURATION));

        return DMNOutput.builder()
                .enrollmentStartTime(enrollmentStartTime)
                .enrollmentEndTime(enrollmentEndTime)
                .journeyStartTime(startTime)
                .activityPrecondition(activityPrecondition)
                .enrollmentPrecondition(enrollmentPrecondition)
                .maxParticipants(maxParticipants)
                .monitoringPeriodDuration(monitoringPeriodDuration)
                .build();
    }

    @Data
    @Builder
    public static class DMNOutput {
        private final String enrollmentStartTime;
        private final String enrollmentEndTime;
        private final String journeyStartTime;
        private final String enrollmentPrecondition;
        private final String activityPrecondition;
        private final Long maxParticipants;
        private final Long monitoringPeriodDuration;
    }
}
