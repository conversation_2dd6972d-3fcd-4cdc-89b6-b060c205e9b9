package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEligibilitySnapshot;

import java.util.List;

public interface ExtendedJourneyEligibilitySnapshotRepository extends JourneyEligibilitySnapshotRepository {

    @Query("SELECT s FROM JourneyEligibilitySnapshot s")
    List<JourneyEligibilitySnapshot> getAll();
}
