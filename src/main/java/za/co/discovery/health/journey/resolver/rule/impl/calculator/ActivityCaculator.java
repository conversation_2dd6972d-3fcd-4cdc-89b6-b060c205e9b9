package za.co.discovery.health.journey.resolver.rule.impl.calculator;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ActivityRuleEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.request.ActivityRecommendationRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityRuleResult;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.util.DMNConstants;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ActivityCaculator implements RuleCalculator {
    private final RuleCacheService cacheService;
    private final RuleEvaluatorResolver evaluator;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.ACTIVITY_RECOMMENDATION_RULE;
    }

    @Override
    public GetActivityRuleResult calculate(final RuleType type, final RuleRequest ruleRequest) {
        if (!(ruleRequest instanceof ActivityRecommendationRuleRequest)) {
            throw new IllegalArgumentException("Expected an ActivityRecommendationRuleRequest");
        }

        final ActivityRecommendationRuleRequest activityRequest = (ActivityRecommendationRuleRequest) ruleRequest;
        final RuleCache ruleCache = cacheService.getRuleCache(ruleRequest.getRuleName());
        final Map<String, Object> vars = Map.of(DMNConstants.MILESTONE_VALUE, activityRequest.getIteration());

        final ActivityRuleEvaluationResult evaluate = (ActivityRuleEvaluationResult) evaluator
                .getEvaluator(type, ruleCache.getProcessorType())
                .evaluate(ruleRequest.getEntityId(), ruleCache, vars);

        if (evaluate.isSuccess()) {
            // Check if this is a skip period
            final boolean isSkipPeriod = evaluate.getResults().stream()
                    .anyMatch(ActivityRuleEvaluationResult.RecommendedActivityResult::isSkipPeriod);

            if (isSkipPeriod) {
                return GetActivityRuleResult.builder()
                        .activities(List.of())
                        .skipPeriod(true)
                        .ruleType(ruleCache.getProcessorType())
                        .success(true)
                        .build();
            }

            return GetActivityRuleResult.builder()
                    .activities(evaluate.getResults().stream()
                            .filter(result -> !result.isSkipPeriod()) // Filter out skip
                            // markers
                            .map(GetActivityRuleResult.RecommendedActivities::of)
                            .collect(Collectors.toList()))
                    .skipPeriod(false)
                    .ruleType(ruleCache.getProcessorType())
                    .success(true)
                    .build();
        } else {
            return GetActivityRuleResult.builder()
                    .activities(List.of())
                    .skipPeriod(false)
                    .ruleType(ruleCache.getProcessorType())
                    .success(false)
                    .build();
        }
    }
}
