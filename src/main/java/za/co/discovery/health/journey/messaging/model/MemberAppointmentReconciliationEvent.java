package za.co.discovery.health.journey.messaging.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberAppointmentReconciliationEvent {
    private Long consultId;
    private Long appointmentId;
    private Long sessionId;
    private Long entityNo;
    private String role;
    private State state;

    public enum State {
        ATTENDED,
        MISSED,
        CANCELLED
    }
}
