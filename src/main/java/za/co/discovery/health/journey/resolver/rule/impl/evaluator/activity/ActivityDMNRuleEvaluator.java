package za.co.discovery.health.journey.resolver.rule.impl.evaluator.activity;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.remote.config.ConfigurationFlagService;
import za.co.discovery.health.journey.resolver.rule.RuleEvaluator;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ActivityRuleEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.output.ActivityDMNRuleOutput;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.service.DmnEvaluationService;
import za.co.discovery.health.journey.util.DMNConstants;
import za.co.discovery.health.journey.util.model.Audience;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityDMNRuleEvaluator implements RuleEvaluator {

    private final ConfigurationFlagService configurationFlagService;

    private final DmnEvaluationService dmnEvaluationService;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.ACTIVITY_RECOMMENDATION_RULE;
    }

    @Override
    public RuleProcessorType getHandledProcessorType() {
        return RuleProcessorType.DMN;
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    @Override
    public ActivityRuleEvaluationResult evaluate(
            final Long entityId, final RuleCache cache, final Map<String, Object> vars) {
        try {
            final Audience audience = configurationFlagService.getAudience(entityId);

            final HashMap<String, Object> varMap = new HashMap<>(vars);
            varMap.putAll(Map.of(
                    DMNConstants.ALLIANCE_NO, audience.getAllianceNo(),
                    DMNConstants.BRANCH_NO, audience.getBranchNo(),
                    DMNConstants.GROUP_NO, audience.getGroupNo()));

            final ActivityDMNRuleOutput dmnOutput =
                    dmnEvaluationService.evaluate(cache, varMap, ActivityDMNRuleOutput::of);
            return ActivityRuleEvaluationResult.of(dmnOutput, true);
        } catch (Exception e) {
            log.error("Error evaluating DMN Rule.", e);
            return ActivityRuleEvaluationResult.of(null, false);
        }
    }
}
