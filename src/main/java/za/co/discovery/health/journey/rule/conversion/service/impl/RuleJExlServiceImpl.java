package za.co.discovery.health.journey.rule.conversion.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyRulesRepository;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.rule.conversion.mapper.JexlModelMapper;
import za.co.discovery.health.journey.rule.conversion.model.JourneyJExlModel;
import za.co.discovery.health.journey.rule.conversion.service.RuleJExlService;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RuleJExlServiceImpl implements RuleJExlService {

    private final ExtendedJourneyRulesRepository repository;
    private final JexlModelMapper modelMapper;

    @Override
    public List<JourneyJExlModel> getAll() {
        return repository.findAllByRuleSetType(RuleProcessorType.JEXL.name()).parallelStream()
                .map(modelMapper::toModel)
                .collect(Collectors.toList());
    }

    @Override
    public JourneyJExlModel getByName(final String name) {
        final JourneyRules rules = repository
                .findByRuleSetNameAndRuleSetType(name, RuleProcessorType.JEXL.name())
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR, String.format("No JEXL was found with name %s", name)));
        return modelMapper.toModel(rules);
    }

    @Override
    public JourneyJExlModel add(final String name, final String rule) {
        final JourneyJExlModel model = modelMapper.buildJexlModel(null, name, rule);

        repository.findByRuleSetName(name).ifPresent((it) -> {
            if (it.getRuleSetType().equals(RuleProcessorType.JEXL.name())) {
                throw new CoreException(
                        ReasonCode.VALIDATION_ERROR, String.format("JEXL with name %s already exists", name));
            } else {
                throw new CoreException(
                        ReasonCode.VALIDATION_ERROR,
                        String.format(
                                "Rule with name %s already exists and it is of type: %s", name, it.getRuleSetType()));
            }
        });

        JourneyRules dmnEntity = modelMapper.toEntity(model);
        dmnEntity = repository.save(dmnEntity);

        return modelMapper.toModel(dmnEntity);
    }

    @Override
    public JourneyJExlModel update(final String name, final String rule) {
        final JourneyRules updatedRule = repository
                .findByRuleSetName(name)
                .map((it) -> {
                    it.setRuleSet(rule.getBytes(StandardCharsets.UTF_8));
                    it.setRuleSetType(RuleProcessorType.JEXL.name());
                    return repository.save(it);
                })
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR, String.format("No JEXL was found with name %s", name)));
        return modelMapper.toModel(updatedRule);
    }

    @Override
    public void delete(final String name) {
        repository.deleteAllByRuleSetNameAndRuleSetType(name, RuleProcessorType.JEXL.name());
    }
}
