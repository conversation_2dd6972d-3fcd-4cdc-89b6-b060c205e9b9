package za.co.discovery.health.journey.config.client;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.function.client.WebClient;
import za.co.discovery.health.pacman.ApiClient;
import za.co.discovery.health.pacman.api.ActivityControllerApi;
import za.co.discovery.health.pacman.api.ActivityTransactionControllerApi;
import za.co.discovery.health.pacman.api.ProgramEnrolmentCohortActivityControllerApi;

@Configuration
public class PacManClientConfig {

    private final WebClient webClient;

    public PacManClientConfig(final @Qualifier("b2b-webclient") WebClient webClient) {
        this.webClient = webClient;
    }

    @Bean
    @Primary
    public ProgramEnrolmentCohortActivityControllerApi programEnrolmentCohortActivityControllerApi(
            @Value("${integration.pac-man.url}") final String baseUrl) {

        return new ProgramEnrolmentCohortActivityControllerApi(new ApiClient(webClient).setBasePath(baseUrl));
    }

    @Bean
    @Primary
    public ActivityControllerApi activityControllerApi(@Value("${integration.pac-man.url}") final String baseUrl) {

        return new ActivityControllerApi(new ApiClient(webClient
                        .mutate()
                        .codecs((configurer) -> {
                            configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024); // 10MB
                            configurer.defaultCodecs().enableLoggingRequestDetails(true);
                        })
                        .build())
                .setBasePath(baseUrl));
    }

    @Bean
    public ActivityTransactionControllerApi activityTransactionControllerApi(
            @Value("${integration.pac-man.url}") final String baseUrl) {
        return new ActivityTransactionControllerApi(new ApiClient(webClient).setBasePath(baseUrl));
    }
}
