package za.co.discovery.health.journey.service.bo.dmn;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.model.bo.dmn.DecisionRule;
import za.co.discovery.health.journey.model.bo.dmn.DmnTableRow;
import za.co.discovery.health.journey.model.bo.dmn.JourneyActivationDmnDto;
import za.co.discovery.health.journey.rule.dmn.model.dmn.JourneyEligibilityRule;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class JourneyActivationDmnRuleMapper {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    /**
     * Convert DmnTableRow to DmnRule
     */
    public JourneyActivationDmnDto toCustomRule(final DmnTableRow tableRow) {
        if (tableRow == null) {
            return null;
        }

        return JourneyActivationDmnDto.builder()
                .ruleId(tableRow.getRuleId())
                .description(tableRow.getDescription())

                // Map input fields
                .alliance(getInputValue(tableRow, "alliance"))
                .branch(getInputValue(tableRow, "branch"))
                .group(getInputValue(tableRow, "group"))

                // Map output fields
                .enrollmentPrecondition(getOutputValue(tableRow, "Enrollment Precondition"))
                .activityPrecondition(getOutputValue(tableRow, "Activity Precondition"))
                .journeyCategory(getOutputValue(tableRow, "journeyCategory"))

                // Parse datetime fields
                .enrollmentStartTime(parseDateTime(getOutputValue(tableRow, "Enrollment Start Time")))
                .enrollmentEndTime(parseDateTime(getOutputValue(tableRow, "Enrollment End Time")))
                .journeyStartTime(parseDateTime(getOutputValue(tableRow, "Journey Start Time")))
                .build();
    }

    public JourneyEligibilityRule toEligibilityRule(final DecisionRule tableRow) {
        if (tableRow == null) {
            return null;
        }

        return JourneyEligibilityRule.builder()
                .alliance(getInputValue(tableRow, "alliance"))
                .customer(getInputValue(tableRow, "group"))
                .branch(getInputValue(tableRow, "branch"))
                .journeyCategory(getOutputValue(tableRow, "journeyCategory"))
                .build();
    }

    /**
     * Convert DmnRule back to DmnTableRow
     */
    public DmnTableRow toTableRow(final JourneyActivationDmnDto rule) {
        if (rule == null) {
            return null;
        }

        final DmnTableRow tableRow = new DmnTableRow();
        tableRow.setRuleId(rule.getRuleId());
        tableRow.setDescription(rule.getDescription());

        // Set input values
        setInputValue(tableRow, "alliance", rule.getAlliance());
        setInputValue(tableRow, "branch", rule.getBranch());
        setInputValue(tableRow, "group", rule.getGroup());

        // Set output values
        setOutputValue(tableRow, "Enrollment Precondition", rule.getEnrollmentPrecondition());
        setOutputValue(tableRow, "Activity Precondition", rule.getActivityPrecondition());
        setOutputValue(tableRow, "journeyCategory", rule.getJourneyCategory());
        setOutputValue(tableRow, "Enrollment Start Time", formatDateTime(rule.getEnrollmentStartTime()));
        setOutputValue(tableRow, "Enrollment End Time", formatDateTime(rule.getEnrollmentEndTime()));
        setOutputValue(tableRow, "Journey Start Time", formatDateTime(rule.getJourneyStartTime()));

        return tableRow;
    }

    /**
     * Convert list of DmnTableRows to list of DmnRules
     */
    public List<JourneyActivationDmnDto> toCustomRules(final List<DmnTableRow> tableRows) {
        if (tableRows == null) {
            return Collections.emptyList();
        }

        return tableRows.stream().map(this::toCustomRule).collect(Collectors.toList());
    }

    public List<JourneyEligibilityRule> toEligibilityRules(final List<DecisionRule> tableRows) {
        if (tableRows == null) {
            return Collections.emptyList();
        }

        return tableRows.stream().map(this::toEligibilityRule).collect(Collectors.toList());
    }

    /**
     * Convert list of DmnRules to list of DmnTableRows
     */
    public List<DmnTableRow> toTableRows(final List<JourneyActivationDmnDto> rules) {
        if (rules == null) {
            return Collections.emptyList();
        }

        return rules.stream().map(this::toTableRow).collect(Collectors.toList());
    }

    // Helper methods
    private String getInputValue(final DmnTableRow tableRow, final String key) {
        final String value = tableRow.getInputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private String getInputValue(final DecisionRule tableRow, final String key) {
        final String value = (String) tableRow.getInputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private String getOutputValue(final DmnTableRow tableRow, final String key) {
        final String value = tableRow.getOutputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private String getOutputValue(final DecisionRule tableRow, final String key) {
        final String value = (String) tableRow.getOutputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private void setInputValue(final DmnTableRow tableRow, final String key, final String value) {
        tableRow.getInputs().put(key, value == null ? "" : value);
    }

    private void setOutputValue(final DmnTableRow tableRow, final String key, final String value) {
        tableRow.getOutputs().put(key, value == null ? "" : value);
    }

    private LocalDateTime parseDateTime(final String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.isBlank()) {
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr.trim(), DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            // Log the error or handle as needed
            log.error("Failed to parse datetime: {}, Error: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }

    private String formatDateTime(final LocalDateTime dateTime) {
        return dateTime == null ? null : dateTime.format(DATE_FORMATTER);
    }
}
