package za.co.discovery.health.journey.strategy;

import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;

import java.time.LocalDateTime;

public interface EnrollmentValidator {
    boolean canEnroll(
            Long userId,
            JourneyCategory category,
            CategoryConfigurationRuleResult.CategoryConfiguration config,
            LocalDateTime now);

    LocalDateTime calculateJourneyStartTime(
            CategoryConfigurationRuleResult.CategoryConfiguration config, LocalDateTime now);
}
