package za.co.discovery.health.journey.config;

import lombok.RequiredArgsConstructor;
import net.javacrumbs.shedlock.core.DefaultLockingTaskExecutor;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.core.LockingTaskExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Shedlock distributed locking.
 * Sets up the necessary beans to enable distributed locking for concurrent operations.
 */
@Configuration
@RequiredArgsConstructor
public class ShedlockConfiguration {

    /**
     * Creates a LockingTaskExecutor that can be used to execute code
     * within a lock context.
     *
     * @param lockProvider The configured lock provider
     * @return A LockingTaskExecutor instance
     */
    @Bean
    public LockingTaskExecutor lockingTaskExecutor(final LockProvider lockProvider) {
        return new DefaultLockingTaskExecutor(lockProvider);
    }
}
