package za.co.discovery.health.journey.remote.pacman.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import za.co.discovery.health.journey.remote.pacman.client.PacManClient;
import za.co.discovery.health.journey.remote.pacman.mapper.ActivityMapper;
import za.co.discovery.health.journey.remote.pacman.model.ProgramEnrolmentCohortActivityDTOPageable;
import za.co.discovery.health.journey.remote.pacman.service.PacManService;
import za.co.discovery.health.journey.util.DateUtils;
import za.co.discovery.health.pacman.domain.Activity;
import za.co.discovery.health.pacman.domain.ActivityFilter;
import za.co.discovery.health.pacman.domain.ActivityTransactionResponse;
import za.co.discovery.health.pacman.domain.MemberActivityExclusionFilter;
import za.co.discovery.health.pacman.domain.PageActivity;
import za.co.discovery.health.pacman.domain.PagingRequest;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class PacManServiceImpl implements PacManService {
    private final PacManClient pacManClient;
    private final ActivityMapper activityMapper;

    @Override
    public Mono<ProgramEnrolmentCohortActivityDTOPageable> getActivities(
            final PagingRequest pagingRequest, final String programName, final Long entityNo) {
        return pacManClient.getActivities(pagingRequest, programName, entityNo).map(activityMapper::map);
    }

    @Override
    public Mono<Map<String, Activity>> filterActivity(final PagingRequest pagingRequest, final ActivityFilter filter) {
        return pacManClient
                .filterActivity(pagingRequest, filter)
                .mapNotNull(PageActivity::getContent)
                .defaultIfEmpty(Collections.emptyList())
                .map(it -> it.stream()
                        .collect(Collectors.toMap(
                                Activity::getActivityEvent,
                                Function.identity(),
                                (existing, replacement) -> replacement)))
                .defaultIfEmpty(Collections.emptyMap());
    }

    @Override
    public Mono<Map<String, ProgramEnrolmentCohortActivityDTO>> getActivitiesByMnemonicWithCustomExclusions(
            final Long entityId,
            final Set<String> mnemonics,
            final Predicate<ProgramEnrolmentCohortActivityDTO> predicate) {
        final MemberActivityExclusionFilter exclusions = new MemberActivityExclusionFilter();
        exclusions.includeOutdated(false);
        exclusions.includeInvisible(true);
        exclusions.includeManuallyAdded(true);
        exclusions.includeCancelled(false);
        exclusions.excludeNoPointActivities(false);
        return pacManClient.getActivitiesByMnemonicWithCustomExclusions(entityId, mnemonics, predicate, exclusions);
    }

    @Override
    public Mono<Map<Long, ActivityTransactionResponse>> getTransaction(
            final Long entityId, final List<Long> activityTxnIds) {
        return pacManClient.getTransaction(entityId, new HashSet<>(activityTxnIds));
    }

    @Override
    public Mono<Boolean> getActivityCompleted(
            final Long entityId, final String mnemonic, final LocalDateTime enrollmentStartTime) {
        return pacManClient
                .getTransactionByMnemonic(entityId, mnemonic)
                .filter(it -> it.values().stream()
                        .anyMatch(txn -> txn.getCreatedDate() != null
                                && txn.getCreatedDate().isAfter(DateUtils.toODT(enrollmentStartTime))))
                .map(it -> !it.isEmpty())
                .defaultIfEmpty(false);
    }
}
