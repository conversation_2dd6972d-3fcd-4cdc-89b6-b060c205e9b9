package za.co.discovery.health.journey.rule.conversion.mapper;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.rule.conversion.model.JourneyJExlModel;

import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
public class JexlModelMapper {

    public JourneyJExlModel toModel(final JourneyRules from) {
        return JourneyJExlModel.builder()
                .id(from.getRuleId())
                .name(from.getRuleSetName())
                .rule(new String(from.getRuleSet(), StandardCharsets.UTF_8))
                .build();
    }

    public JourneyRules toEntity(final JourneyJExlModel from) {
        final JourneyRules entity = new JourneyRules();
        entity.setRuleSet(from.getRule().getBytes(StandardCharsets.UTF_8));
        entity.setRuleSetName(from.getName());
        entity.setRuleSetType(RuleProcessorType.JEXL.name());
        return entity;
    }

    public JourneyJExlModel buildJexlModel(final Long id, final String name, final String rule) {
        return JourneyJExlModel.builder().id(id).rule(rule).name(name).build();
    }
}
