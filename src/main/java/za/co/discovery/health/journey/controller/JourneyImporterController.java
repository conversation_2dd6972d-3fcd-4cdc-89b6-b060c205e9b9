package za.co.discovery.health.journey.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.constant.JourneyAttr;
import za.co.discovery.health.journey.model.imprt.EnrollJourneyTemplateRequest;
import za.co.discovery.health.journey.service.journey.JourneyEnrollmentService;
import za.co.discovery.health.journey.service.journey.JourneyImporterService;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/v1/journey/import", produces = MediaType.APPLICATION_JSON_VALUE)
public class JourneyImporterController {
    private final JourneyImporterService journeyImporterService;
    private final JourneyEnrollmentService journeyEnrollmentService;

    @PostMapping
    public long enrollWithJourneyTemplate(@RequestBody final EnrollJourneyTemplateRequest request) {
        return journeyImporterService.importJourney(request.getJourneyTemplate());
    }

    @DeleteMapping("/all-migrated-dpp-journeys")
    public void deleteAllMigratedJourneys() {
        journeyEnrollmentService.deleteEnrollmentsWithAttribute(
            JourneyAttr.WELLSPARK_DPP_MIGRATION.getName(), Boolean.TRUE.toString());
    }

    @DeleteMapping("/migrated-dpp-journey")
    public void deleteMigratedJourneyForUser(@RequestParam("entityNo") final long entityNo) {
        journeyEnrollmentService.deleteEnrollmentsWithAttributeForUser(
            entityNo, JourneyAttr.WELLSPARK_DPP_MIGRATION.getName(), Boolean.TRUE.toString());
    }
}
