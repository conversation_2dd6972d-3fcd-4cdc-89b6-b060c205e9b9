package za.co.discovery.health.journey.remote.pacman.service;

import reactor.core.publisher.Mono;
import za.co.discovery.health.journey.remote.pacman.model.ProgramEnrolmentCohortActivityDTOPageable;
import za.co.discovery.health.pacman.domain.Activity;
import za.co.discovery.health.pacman.domain.ActivityFilter;
import za.co.discovery.health.pacman.domain.ActivityTransactionResponse;
import za.co.discovery.health.pacman.domain.PagingRequest;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

public interface PacManService {

    Mono<ProgramEnrolmentCohortActivityDTOPageable> getActivities(
            PagingRequest pagingRequest, String programName, Long entityNo);

    default Mono<ProgramEnrolmentCohortActivityDTOPageable> getActivities(
            final String programName, final Long entityNo) {
        final PagingRequest pagingRequest = new PagingRequest().page(0).size(Integer.MAX_VALUE);
        return getActivities(pagingRequest, programName, entityNo);
    }

    Mono<Map<String, Activity>> filterActivity(PagingRequest pagingRequest, ActivityFilter filter);

    default Mono<Map<String, Activity>> filterActivity(final Set<String> mnemonics) {
        if (mnemonics == null || mnemonics.isEmpty()) {
            return Mono.just(Map.of());
        }
        final PagingRequest pagingRequest = new PagingRequest().page(0).size(Integer.MAX_VALUE);
        return filterActivity(pagingRequest, new ActivityFilter().activityEvent(new ArrayList<>(mnemonics)));
    }

    Mono<Map<String, ProgramEnrolmentCohortActivityDTO>> getActivitiesByMnemonicWithCustomExclusions(
            Long entityId, Set<String> mnemonics, Predicate<ProgramEnrolmentCohortActivityDTO> predicate);

    Mono<Map<Long, ActivityTransactionResponse>> getTransaction(Long entityId, List<Long> activityTxnIds);

    Mono<Boolean> getActivityCompleted(Long entityId, String mnemonic, LocalDateTime enrollmentStartTime);
}
