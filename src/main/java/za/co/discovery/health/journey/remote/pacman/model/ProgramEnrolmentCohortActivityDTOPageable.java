package za.co.discovery.health.journey.remote.pacman.model;

import lombok.Data;
import za.co.discovery.health.pacman.domain.PageableObject;
import za.co.discovery.health.pacman.domain.SortObject;

import java.util.List;

@Data
public class ProgramEnrolmentCohortActivityDTOPageable {
    private Integer totalPages;
    private Long totalElements;
    private Integer number;
    private Integer size;
    private List<ProgramEnrolmentCohortActivityDTOResponse> content;
    private SortObject sort;
    private Boolean first;
    private Boolean last;
    private Integer numberOfElements;
    private PageableObject pageable;
    private Boolean empty;
}
