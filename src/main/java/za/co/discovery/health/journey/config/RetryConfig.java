package za.co.discovery.health.journey.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.ExceptionClassifierRetryPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import za.co.discovery.health.journey.config.exception.ConcurrentCalculationException;

import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class RetryConfig {

    @Bean
    public RetryTemplate retryTemplate() {
        final RetryTemplate retryTemplate = new RetryTemplate();

        // Retry up to 5 times with exponential backoff
        final ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000); // Initial delay: 1 second
        backOffPolicy.setMultiplier(2); // Double the delay each retry
        backOffPolicy.setMaxInterval(10_000); // Maximum delay: 10 seconds

        final SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(5); // Total attempts = initial + 4 retries

        retryTemplate.setBackOffPolicy(backOffPolicy);

        final ExceptionClassifierRetryPolicy exceptionClassifierRetryPolicy = new ExceptionClassifierRetryPolicy();
        final Map<Class<? extends Throwable>, RetryPolicy> policyMap = new HashMap<>();
        policyMap.put(ConcurrentCalculationException.class, retryPolicy);
        exceptionClassifierRetryPolicy.setPolicyMap(policyMap);

        retryTemplate.setRetryPolicy(exceptionClassifierRetryPolicy);

        return retryTemplate;
    }
}
