package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;

import java.time.LocalDateTime;

@Getter
public class JourneyDrivenActivityCompletedEvent extends JourneyEvent {
    private final JourneyEnrollmentMilestoneActivity activity;
    private final LocalDateTime completedAt;

    public JourneyDrivenActivityCompletedEvent(
            final JourneyEnrollmentMilestoneActivity activity, final LocalDateTime completedAt) {
        super(activity.getJourneyEnrollmentMilestone().getJourneyEnrollment().getEntityId(), LocalDateTime.now());
        this.activity = activity;
        this.completedAt = completedAt;
    }
}
