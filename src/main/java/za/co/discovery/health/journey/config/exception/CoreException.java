package za.co.discovery.health.journey.config.exception;

import lombok.Getter;

@Getter
public class CoreException extends RuntimeException {
    private static final Long serialVersionUID = 123123123L;
    private final ReasonCode reasonCode;

    public CoreException(final ReasonCode reasonCode) {
        super(reasonCode.getDescription());
        this.reasonCode = reasonCode;
    }

    public CoreException(final ReasonCode reasonCode, final String message) {
        super(message);
        this.reasonCode = reasonCode;
    }

    public CoreException(final ReasonCode reasonCode, final Exception ex) {
        super(ex);
        this.reasonCode = reasonCode;
    }

    public CoreException(final ReasonCode reasonCode, final String message, final Exception ex) {
        super(message, ex);
        this.reasonCode = reasonCode;
    }

    public CoreException(final ReasonCode reasonCode, final String message, final Throwable throwable) {
        super(message, throwable);
        this.reasonCode = reasonCode;
    }
}
