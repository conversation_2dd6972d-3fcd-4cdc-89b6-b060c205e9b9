package za.co.discovery.health.journey.config.exception;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponse<T> {

    @JsonProperty("reasonCode")
    private ReasonCode reasonCode;

    @JsonProperty("internalCode")
    private int internalCode;

    @JsonProperty("description")
    private String description;

    @JsonProperty("message")
    private String message;

    @JsonProperty("errorDto")
    private T errorDto;
}
