package za.co.discovery.health.journey.strategy.precondition;

import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.model.ActivityCompletionPreconditionDto;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;

import java.time.LocalDateTime;

public interface ActivityCompletionPreconditionStrategy {
    ActivityCompletionPreconditionType getType();

    PreconditionEvaluationResult evaluate(
            ActivityCompletionPreconditionDto precondition,
            String currentActivityMnemonicId,
            ActivityDetailsType currentActivityType,
            LocalDateTime time,
            Long entityId,
            JourneyProgramBehaviour behaviour);
}
