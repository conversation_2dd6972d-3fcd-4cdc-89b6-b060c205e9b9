package za.co.discovery.health.journey.resolver.rule.impl.calculator;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.MilestoneTransitionRuleEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.request.MilestoneTransitionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetMilestoneTransitionRuleResult;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.util.DMNConstants;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class MilestoneTransitionCalculator implements RuleCalculator {
    private final RuleCacheService cacheService;
    private final RuleEvaluatorResolver evaluator;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.MILESTONE_TRANSITION_RULE;
    }

    @Override
    public GetMilestoneTransitionRuleResult calculate(final RuleType type, final RuleRequest ruleRequest) {
        if (!(ruleRequest instanceof MilestoneTransitionRuleRequest)) {
            throw new IllegalArgumentException("Expected an MilestoneTransitionRuleRequest");
        }
        final MilestoneTransitionRuleRequest milestoneRequest = (MilestoneTransitionRuleRequest) ruleRequest;

        final RuleCache ruleCache = cacheService.getRuleCache(ruleRequest.getRuleName());
        final Map<String, Object> vars = Map.of(
                DMNConstants.ITERATION, milestoneRequest.getIteration(),
                DMNConstants.MILESTONE_TRANSITION_ACTIVITIES, milestoneRequest.getActivities());

        // PREPARE VARIABLES
        final MilestoneTransitionRuleEvaluationResult evaluate = (MilestoneTransitionRuleEvaluationResult) evaluator
                .getEvaluator(type, ruleCache.getProcessorType())
                .evaluate(ruleRequest.getEntityId(), ruleCache, vars);

        if (evaluate.isSuccess()) {
            return GetMilestoneTransitionRuleResult.builder()
                    .success(true)
                    .canTransition(evaluate.isCanTransition())
                    .build();
        } else {
            return GetMilestoneTransitionRuleResult.builder()
                    .success(false)
                    .canTransition(false)
                    .build();
        }
    }
}
