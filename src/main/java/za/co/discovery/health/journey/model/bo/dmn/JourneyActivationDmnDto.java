package za.co.discovery.health.journey.model.bo.dmn;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JourneyActivationDmnDto {

    // Rule identification
    private String ruleId;
    private String description;

    // Input fields
    private String alliance;
    private String branch;
    private String group;

    // Output fields
    private String enrollmentPrecondition;
    private String activityPrecondition;
    private String journeyCategory;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime enrollmentStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime enrollmentEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime journeyStartTime;

    // Helper methods
    public boolean hasValidEnrollmentPeriod() {
        return enrollmentStartTime != null && enrollmentEndTime != null;
    }

    public boolean isEnrollmentActive(final LocalDateTime currentTime) {
        if (!hasValidEnrollmentPeriod()) {
            return false;
        }
        return currentTime.isAfter(enrollmentStartTime) && currentTime.isBefore(enrollmentEndTime);
    }

    public boolean isJourneyStarted(final LocalDateTime currentTime) {
        return journeyStartTime != null && currentTime.isAfter(journeyStartTime);
    }

    public boolean hasInputCriteria() {
        return alliance != null && !alliance.isEmpty()
                || branch != null && !branch.isEmpty()
                || group != null && !group.isEmpty();
    }
}
