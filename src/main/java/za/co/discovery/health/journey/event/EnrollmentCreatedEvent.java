package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;

@Getter
public class EnrollmentCreatedEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;
    private final LocalDateTime journeyStartTime;

    public EnrollmentCreatedEvent(final JourneyEnrollment enrollment, final LocalDateTime journeyStartTime) {
        super(enrollment.getEntityId(), LocalDateTime.now());
        this.enrollment = enrollment;
        this.journeyStartTime = journeyStartTime;
    }
}
