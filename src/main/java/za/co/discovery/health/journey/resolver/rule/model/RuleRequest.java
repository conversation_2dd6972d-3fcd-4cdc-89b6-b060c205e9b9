package za.co.discovery.health.journey.resolver.rule.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.util.model.Audience;

@Data
@SuperBuilder
@AllArgsConstructor
public class RuleRequest {
    private final RuleProcessorType processorType;
    private final String ruleName;
    private final Long entityId;
    private final Audience audience;
}
