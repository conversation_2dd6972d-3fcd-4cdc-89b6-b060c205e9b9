package za.co.discovery.health.journey.resolver.rule.model.request;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ProgramTransitionRuleRequest extends RuleRequest {
    private final Long programDuration;
    private final List<MilestoneDetails> milestones;

    @Data
    @Builder
    public static class MilestoneDetails {
        private final Long iteration;
        private final String status;
        private final List<ActivityDetails> activities;
    }

    @Data
    @Builder
    public static class ActivityDetails {
        private final String status;
        private final String mnemonic;
        private final String activityType;
    }
}
