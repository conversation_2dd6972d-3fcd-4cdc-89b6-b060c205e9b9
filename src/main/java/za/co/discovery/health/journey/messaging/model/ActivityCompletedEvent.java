package za.co.discovery.health.journey.messaging.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCompletedEvent {
    private String entityId;
    private long enrolmentId;
    private long activityTransactionId;
    private boolean isGroupProgramActivity;
    private LocalDate createdDate;
    private LocalDate transactionDate;
    private ActivityAllocationStatusEnum allocationStatusEnum;
    private String value;
}
