package za.co.discovery.health.journey.remote.pacman.model;

import lombok.Builder;
import lombok.Data;
import za.co.discovery.health.pacman.domain.CmsContent;
import za.co.discovery.health.pacman.domain.ProgramEnrolmentCohortActivityDTO;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class ProgramEnrolmentCohortActivityDTOResponse {
    private String programId;
    private String programName;
    private String programEnrolmentId;
    private String programEnrolmentCohortActivityId;
    private String activityId;
    private String activityName;
    private ProgramEnrolmentCohortActivityDTO.ActivityStatusEnum activityStatus;
    private ActivityPointsResponse activityPoints;
    private String mnemonic;
    private OffsetDateTime effFrom;
    private OffsetDateTime effTo;
    private CmsContent content;
    private String activityType;
    private List<Long> categoryIds;
    private String frequency;
    private String timeToComplete;
    private OffsetDateTime activityCompletedDate;
    private Map<String, String> attributes;
    private List<String> roles;
    private Boolean isPhysicalActivity;
    private Boolean manuallyAdded;
}
