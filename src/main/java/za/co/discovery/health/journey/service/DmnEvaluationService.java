package za.co.discovery.health.journey.service;

import lombok.RequiredArgsConstructor;
import org.camunda.bpm.dmn.engine.DmnDecisionTableResult;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Service
@RequiredArgsConstructor
public class DmnEvaluationService {

    private final DmnEngine dmnEngine;

    /**
     * Evaluates the DMN decision table contained in the given {@link RuleCache},
     * then applies the provided {@code resultMapper} to transform the raw DMN results
     * into any desired output type.
     *
     * @param ruleCache    The {@link RuleCache} containing the DMN configuration
     * @param variables    Variables to be fed into the DMN engine
     * @param resultMapper A function that takes the raw DMN result list
     *                     and returns a transformed object of type {@code T}
     * @param <T>          The desired return type (e.g., {@code ActivityDMNRuleOutput}, etc.)
     * @return An instance of type {@code T}, as generated by {@code resultMapper}
     */
    public <T> T evaluate(
            final RuleCache ruleCache,
            final Map<String, Object> variables,
            final Function<List<Map<String, Object>>, T> resultMapper) {
        validateCache(ruleCache);

        final DmnDecisionTableResult decisionTableResult =
                dmnEngine.evaluateDecisionTable(ruleCache.getDecision(), variables);

        final List<Map<String, Object>> resultList = decisionTableResult.getResultList();

        return resultMapper.apply(resultList);
    }

    private void validateCache(final RuleCache ruleCache) {
        if (ruleCache == null) {
            throw new CoreException(ReasonCode.GENERAL_ERROR, "No Rule Cache found.");
        }
        if (ruleCache.getProcessorType() != RuleProcessorType.DMN) {
            throw new CoreException(ReasonCode.GENERAL_ERROR, "Rule Cache is not a DMN Rule.");
        }
    }
}
