package za.co.discovery.health.journey.resolver.rule.impl.calculator;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.JourneyActivationEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.result.JourneyActivationRuleResult;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class JourneyActivationCalculator implements RuleCalculator {
    private final RuleCacheService cacheService;
    private final RuleEvaluatorResolver evaluator;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.JOURNEY_ACTIVATION;
    }

    @Override
    public JourneyActivationRuleResult calculate(final RuleType type, final RuleRequest ruleRequest) {
        final RuleCache ruleCache = cacheService.getRuleCache(ruleRequest.getRuleName());

        // PREPARE VARIABLES
        final JourneyActivationEvaluationResult evaluate = (JourneyActivationEvaluationResult) evaluator
                .getEvaluator(type, ruleCache.getProcessorType())
                .evaluate(ruleRequest.getEntityId(), ruleCache, Map.of());

        if (evaluate.isSuccess()) {
            return JourneyActivationRuleResult.builder()
                    .categories(evaluate.getResults().stream()
                            .map(JourneyActivationRuleResult.CategoryRecommendation::of)
                            .collect(Collectors.toList()))
                    .ruleType(ruleCache.getProcessorType())
                    .success(true)
                    .build();
        } else {
            return JourneyActivationRuleResult.builder()
                    .categories(List.of())
                    .ruleType(ruleCache.getProcessorType())
                    .success(false)
                    .build();
        }
    }
}
