package za.co.discovery.health.journey.resolver.rule.model.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.RuleResult;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class GetMilestoneTransitionRuleResult extends RuleResult {
    private final boolean canTransition;
}
