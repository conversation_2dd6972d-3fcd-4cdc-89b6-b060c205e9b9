package za.co.discovery.health.journey.service.precondition;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.model.ActivityCompletionPreconditionDto;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.impl.RuleCalculatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.request.ActivityCompletionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityCompletionRuleResult;
import za.co.discovery.health.journey.strategy.precondition.ActivityCompletionPreconditionStrategy;
import za.co.discovery.health.journey.strategy.precondition.validator.ActivityPreconditionStrategyRegistry;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ActivityPreconditionEvaluationService implements ActivityPreconditionEvaluator {

    private final RuleCalculatorResolver resolver;
    private final ActivityPreconditionStrategyRegistry strategyRegistry;

    @Override
    public List<PreconditionEvaluationResult> evaluateAllPreconditions(
            final Long entityId,
            final Long iteration,
            final String activityMnemonic,
            final ActivityDetailsType activityType,
            final LocalDateTime time,
            final JourneyProgramBehaviour behaviour) {

        log.debug(
                "Evaluating preconditions for activity {} in iteration {} for entity {}",
                activityMnemonic,
                iteration,
                entityId);

        final List<ActivityCompletionPreconditionDto> preconditions =
                evaluateActivityCompletionRule(entityId, iteration, activityMnemonic, behaviour);

        if (preconditions.isEmpty()) {
            log.debug("No preconditions found for activity {}", activityMnemonic);
            return Collections.emptyList();
        }

        return preconditions.stream()
                .map(precondition -> evaluateSinglePrecondition(
                        precondition, activityMnemonic, activityType, time, entityId, behaviour))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    private PreconditionEvaluationResult evaluateSinglePrecondition(
            final ActivityCompletionPreconditionDto precondition,
            final String currentActivityMnemonicId,
            final ActivityDetailsType currentActivityType,
            final LocalDateTime time,
            final Long entityId,
            final JourneyProgramBehaviour behaviour) {

        try {
            if (!strategyRegistry.hasStrategy(precondition.getPreconditionType())) {
                log.warn("No strategy found for precondition type: {}", precondition.getPreconditionType());
                return PreconditionEvaluationResult.satisfied(
                        precondition.getPreconditionType(), precondition.getIdentifier(), precondition.getIteration());
            }

            final ActivityCompletionPreconditionStrategy strategy =
                    strategyRegistry.getStrategy(precondition.getPreconditionType());

            return strategy.evaluate(
                    precondition, currentActivityMnemonicId, currentActivityType, time, entityId, behaviour);

        } catch (Exception e) {
            log.error(
                    "Error evaluating precondition {} for entity {}: {}",
                    precondition.getIdentifier(),
                    entityId,
                    e.getMessage(),
                    e);

            return PreconditionEvaluationResult.failed(
                    precondition.getPreconditionType(),
                    precondition.getIdentifier(),
                    "System error evaluating precondition",
                    precondition.getIteration());
        }
    }

    private List<ActivityCompletionPreconditionDto> evaluateActivityCompletionRule(
            final Long entityId,
            final long iteration,
            final String activityMnemonic,
            final JourneyProgramBehaviour behaviour) {

        final JourneyRules rule = behaviour.getJourneyRulesByProgramActivityCompletionRulesId();

        if (rule == null) {
            return List.of();
        }

        final ActivityCompletionRuleRequest request = ActivityCompletionRuleRequest.builder()
                .iteration(iteration)
                .activityMnemonic(activityMnemonic)
                .ruleName(rule.getRuleSetName())
                .processorType(RuleProcessorType.valueOf(rule.getRuleSetType()))
                .entityId(entityId)
                .build();

        final GetActivityCompletionRuleResult result =
                (GetActivityCompletionRuleResult) resolver.getEvaluator(RuleType.ACTIVITY_COMPLETION_RULE)
                        .calculate(RuleType.ACTIVITY_COMPLETION_RULE, request);
        if (!result.isSuccess()) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Rule evaluation failed for activity completion rule");
        }
        return result.getRules().stream()
                .map(ActivityCompletionPreconditionDto::of)
                .collect(Collectors.toList());
    }
}
