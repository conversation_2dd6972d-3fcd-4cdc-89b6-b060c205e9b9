package za.co.discovery.health.journey.model.bo.dmn;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class DmnTableRow {
    private String ruleId;
    private Map<String, String> inputs = new HashMap<>();
    private Map<String, String> outputs = new HashMap<>();
    private String description;

    @Override
    public String toString() {
        return "DmnTableRow{" + "ruleId='" + ruleId + '\'' + ", inputs=" + inputs + ", outputs=" + outputs + '}';
    }
}
