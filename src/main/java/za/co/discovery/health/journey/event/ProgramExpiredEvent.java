package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;

import java.time.LocalDateTime;

@Getter
public class ProgramExpiredEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;

    public ProgramExpiredEvent(final JourneyEnrollment enrollment, final LocalDateTime requestedAt) {
        super(enrollment.getEntityId(), requestedAt);
        this.enrollment = enrollment;
    }
}
