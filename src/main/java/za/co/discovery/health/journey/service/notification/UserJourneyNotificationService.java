package za.co.discovery.health.journey.service.notification;

import za.co.discovery.health.journey.database.databaseMapping.UserJourneyNotification;

import java.time.LocalDateTime;
import java.util.List;

public interface UserJourneyNotificationService {

    void cancelNotification(UserJourneyNotification notification);

    void updateSentNotification(UserJourneyNotification notification, LocalDateTime notifiedAt);

    void updateNotificationStartEndTimes(
            UserJourneyNotification notification, LocalDateTime startTime, LocalDateTime endTime);

    List<UserJourneyNotification> findScheduledNotifications();

    List<UserJourneyNotification> findScheduledNotifications(
            String alliance, String customer, String branch, Long journeyCategoryId);

    List<UserJourneyNotification> findNotificationToSend(LocalDateTime currentTime);
}
