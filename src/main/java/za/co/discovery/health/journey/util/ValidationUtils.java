package za.co.discovery.health.journey.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Supplier;

/**
 * Utility class for common validation operations
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ValidationUtils {

    /**
     * Validates that a user ID is valid
     */
    public static void validateUserId(final Long userId) {
        if (userId == null || userId <= 0) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Invalid user ID: " + userId);
        }
    }

    /**
     * Validates that a category ID is valid
     */
    public static void validateCategoryId(final Long categoryId) {
        if (categoryId == null || categoryId <= 0) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Invalid category ID: " + categoryId);
        }
    }

    /**
     * Validates that a program ID is valid
     */
    public static void validateProgramId(final Long programId) {
        if (programId == null || programId <= 0) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "Invalid program ID: " + programId);
        }
    }

    /**
     * Validates that a timestamp is not null and not in the future beyond reasonable limits
     */
    public static void validateTimestamp(final LocalDateTime timestamp, final String fieldName) {
        if (timestamp == null) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, fieldName + " cannot be null");
        }

        // Allow up to 1 hour in the future to account for clock differences
        final LocalDateTime maxAllowed = LocalDateTime.now().plusMonths(10);
        if (timestamp.isAfter(maxAllowed)) {
            throw new CoreException(
                    ReasonCode.VALIDATION_ERROR, fieldName + " cannot be more than 1 hour in the future");
        }
    }

    /**
     * Validates that a string is not null or empty
     */
    public static void validateNotEmpty(final String value, final String fieldName) {
        if (value == null || value.isBlank()) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, fieldName + " cannot be null or empty");
        }
    }

    /**
     * Validates that a collection is not null or empty
     */
    public static void validateNotEmpty(final List<?> collection, final String fieldName) {
        if (collection == null || collection.isEmpty()) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, fieldName + " cannot be null or empty");
        }
    }

    /**
     * Returns the value if not null, otherwise throws CoreException
     */
    public static <T> T requireNonNull(final T value, final String fieldName) {
        if (value == null) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, fieldName + " cannot be null");
        }
        return value;
    }

    /**
     * Returns the value if found, otherwise throws CoreException with custom message
     */
    public static <T> T requireFound(final T value, final Supplier<String> errorMessage) {
        if (value == null) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, errorMessage.get());
        }
        return value;
    }
}
