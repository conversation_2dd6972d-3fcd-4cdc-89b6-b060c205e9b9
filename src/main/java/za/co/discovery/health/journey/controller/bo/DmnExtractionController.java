package za.co.discovery.health.journey.controller.bo;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.model.bo.dmn.DmnTableRow;
import za.co.discovery.health.journey.service.bo.dmn.DmnTableExtractor;

import java.util.List;

@RestController
@RequestMapping("/api/bo/dmn")
@RequiredArgsConstructor
public class DmnExtractionController {

    private final DmnTableExtractor dmnTableExtractor;

    @GetMapping("/extract/{ruleName}/{decisionKey}")
    public ResponseEntity<List<DmnTableRow>> extractTableRows(
            @PathVariable final String ruleName, @PathVariable final String decisionKey) {

        final List<DmnTableRow> rows = dmnTableExtractor.extractTableRows(ruleName, decisionKey);
        return ResponseEntity.ok(rows);
    }
}
