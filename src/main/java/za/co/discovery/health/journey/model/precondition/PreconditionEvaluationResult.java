package za.co.discovery.health.journey.model.precondition;

import lombok.Builder;
import lombok.ToString;
import lombok.Value;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;

@Value
@Builder
@ToString
@SuppressWarnings("PMD.AvoidFieldNameMatchingMethodName")
public class PreconditionEvaluationResult {
    boolean satisfied;
    String message;
    ActivityCompletionPreconditionType type;
    String identifier;
    Long iteration;

    public static PreconditionEvaluationResult satisfied(
            final ActivityCompletionPreconditionType type, final String identifier, final Long iteration) {
        return PreconditionEvaluationResult.builder()
                .satisfied(true)
                .type(type)
                .identifier(identifier)
                .message("Precondition satisfied")
                .iteration(iteration)
                .build();
    }

    public static PreconditionEvaluationResult failed(
            final ActivityCompletionPreconditionType type,
            final String identifier,
            final String message,
            final Long iteration) {
        return PreconditionEvaluationResult.builder()
                .satisfied(false)
                .type(type)
                .identifier(identifier)
                .message(message)
                .iteration(iteration)
                .build();
    }
}
