package za.co.discovery.health.journey.model.preview;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;

import java.util.List;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class JourneyMilestonePreview {
    private Long iteration;
    private List<ActivityDetails> activities; // activities which user will do in milestone ordered by order
    private List<JourneyRewardPreview> rewards;

    @Setter
    @Getter
    @Builder
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityDetails {
        private String mnemonic;
        private ActivityDetailsType type;
        private String name;
        private String icon;
        private long activityAmount;
        private boolean isMandatory;
        private List<PreconditionEvaluationResult> preconditionResults;
    }
}
