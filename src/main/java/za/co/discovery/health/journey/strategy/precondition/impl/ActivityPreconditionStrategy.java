package za.co.discovery.health.journey.strategy.precondition.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneActivityRepository;
import za.co.discovery.health.journey.model.ActivityCompletionPreconditionDto;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.ActivityStatus;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;
import za.co.discovery.health.journey.strategy.precondition.ActivityCompletionPreconditionStrategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class ActivityPreconditionStrategy implements ActivityCompletionPreconditionStrategy {

    private final ExtendedJourneyEnrollmentMilestoneActivityRepository activityRepository;

    @Override
    public ActivityCompletionPreconditionType getType() {
        return ActivityCompletionPreconditionType.ACTIVITY_COMPLETION;
    }

    @Override
    public PreconditionEvaluationResult evaluate(
            final ActivityCompletionPreconditionDto precondition,
            final String currentActivityMnemonicId,
            final ActivityDetailsType currentActivityType,
            final LocalDateTime time,
            final Long entityId,
            final JourneyProgramBehaviour behaviour) {

        log.debug("Evaluating activity precondition: {} for entity {}", precondition.getIdentifier(), entityId);

        final Optional<JourneyEnrollmentMilestoneActivity> prerequisiteActivity =
                activityRepository.getActivityByIteration(
                        behaviour.getJourneyProgramId(),
                        entityId,
                        precondition.getIteration(),
                        precondition.getIdentifier());

        if (prerequisiteActivity.isEmpty()) {
            return PreconditionEvaluationResult.failed(
                    getType(),
                    precondition.getIdentifier(),
                    String.format(
                            "Required activity '%s' not yet assigned/completed %d",
                            precondition.getIdentifier(), precondition.getIteration()),
                    precondition.getIteration());
        }

        final boolean isCompleted = List.of(
                        ActivityStatus.COMPLETED.getValue(), ActivityStatus.LATE_COMPLETED.getValue())
                .contains(prerequisiteActivity.get().getActivityStatus());

        if (isCompleted) {
            return PreconditionEvaluationResult.satisfied(
                    getType(), precondition.getIdentifier(), precondition.getIteration());
        }

        return PreconditionEvaluationResult.failed(
                getType(),
                precondition.getIdentifier(),
                String.format(
                        "Complete activity '%s' in week %d before attempting this activity",
                        precondition.getIdentifier(), precondition.getIteration()),
                precondition.getIteration());
    }
}
