package za.co.discovery.health.journey.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum EnrollmentStatus {
    ACTIVE("ACTIVE"),
    COMPLETED("COMPLETED"),
    LATE_COMPLETED("LATE_COMPLETED"),
    NOT_ACHIEVED("NOT_ACHIEVED"),
    CANCELLED("CANCELLED");

    private final String value;

    public static EnrollmentStatus fromValue(final String value) {
        for (final EnrollmentStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enrollment status: " + value);
    }
}
