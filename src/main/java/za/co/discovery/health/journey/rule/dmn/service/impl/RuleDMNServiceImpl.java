package za.co.discovery.health.journey.rule.dmn.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyRulesRepository;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.rule.dmn.mapper.DmnModelMapper;
import za.co.discovery.health.journey.rule.dmn.mapper.FileContentMapper;
import za.co.discovery.health.journey.rule.dmn.model.dmn.DmnFileContent;
import za.co.discovery.health.journey.rule.dmn.model.dmn.JourneyDmnModel;
import za.co.discovery.health.journey.rule.dmn.service.RuleDMNService;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RuleDMNServiceImpl implements RuleDMNService {

    private final ExtendedJourneyRulesRepository repository;
    private final FileContentMapper fileContentMapper;
    private final DmnModelMapper dmnMapper;

    @Override
    public List<DmnFileContent> getAll() {
        return repository.findAllByRuleSetType(RuleProcessorType.DMN.name()).parallelStream()
                .map(fileContentMapper::toModel)
                .collect(Collectors.toList());
    }

    @Override
    public DmnFileContent getByName(final String name) {
        final JourneyRules dmn = repository
                .findByRuleSetNameAndRuleSetType(name, RuleProcessorType.DMN.name())
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR, String.format("No DMN was found with name %s", name)));
        return fileContentMapper.toModel(dmn);
    }

    @Override
    public JourneyDmnModel add(final String name, final byte[] file) {
        final JourneyDmnModel dmnModel = dmnMapper.buildDmnModel(name);

        repository.findByRuleSetName(name).ifPresent((it) -> {
            if (it.getRuleSetType().equals(RuleProcessorType.DMN.name())) {
                throw new CoreException(
                        ReasonCode.VALIDATION_ERROR, String.format("DMN with name %s already exists", name));
            } else {
                throw new CoreException(
                        ReasonCode.VALIDATION_ERROR,
                        String.format(
                                "Rule with name %s already exists and it is of type: %s", name, it.getRuleSetType()));
            }
        });

        JourneyRules dmnEntity = dmnMapper.toEntity(dmnModel, file);
        dmnEntity = repository.save(dmnEntity);

        return dmnMapper.toModel(dmnEntity);
    }

    @Override
    public JourneyDmnModel update(final String name, final byte[] file) {
        final JourneyRules updatedRule = repository
                .findByRuleSetName(name)
                .map((it) -> {
                    it.setRuleSet(file);
                    it.setRuleSetType(RuleProcessorType.DMN.name());
                    return repository.save(it);
                })
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR, String.format("No DMN was found with name %s", name)));
        return dmnMapper.toModel(updatedRule);
    }

    @Override
    public void delete(final String name) {
        repository.deleteAllByRuleSetNameAndRuleSetType(name, RuleProcessorType.DMN.name());
    }
}
