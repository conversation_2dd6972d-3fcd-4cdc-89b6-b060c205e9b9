package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import za.co.discovery.health.journey.database.databaseMapping.UserJourneyNotification;

import java.time.LocalDateTime;
import java.util.List;

@SuppressWarnings("PMD.UseObjectForClearerAPI")
public interface ExtendedUserJourneyNotificationRepository
        extends UserJourneyNotificationRepository, JpaRepository<UserJourneyNotification, Long> {

    @Query("SELECT notification FROM UserJourneyNotification notification "
            + "WHERE notification.notificationStatus = :status "
            + "AND :currentTime >= notification.enrollmentStartTime AND :currentTime < notification.enrollmentEndTime")
    List<UserJourneyNotification> findNotificationsInEnrollmentPeriodWithStatus(
            LocalDateTime currentTime, String status);

    List<UserJourneyNotification> findAllByNotificationStatus(String status);

    @Query("SELECT n FROM UserJourneyNotification n WHERE "
            + "(:alliance IS NULL OR n.alliance = :alliance) AND "
            + "(:customer IS NULL OR n.customer = :customer) AND "
            + "(:branch IS NULL OR n.branch = :branch) AND "
            + "n.journeyCategory.journeyCategoryId = :journeyCategoryId AND "
            + "n.notificationStatus = :status")
    List<UserJourneyNotification> findNotificationsWithStatus(
            String alliance, String customer, String branch, Long journeyCategoryId, String status);
}
