package za.co.discovery.health.journey.service.lock;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockConfiguration;
import net.javacrumbs.shedlock.core.LockingTaskExecutor;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.ConcurrentCalculationException;
import za.co.discovery.health.journey.config.exception.ReasonCode;

import java.time.Duration;
import java.time.Instant;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class LockingTaskExecutorFacade {
    private final LockingTaskExecutor lockingTaskExecutor;
    private final RetryTemplate retryTemplate;

    private static final Duration MAX_LOCK_DURATION = Duration.ofSeconds(30);
    private static final Duration MIN_LOCK_DURATION = Duration.ofMillis(0);

    @SneakyThrows
    @SuppressWarnings("PMD.AvoidCatchingThrowable")
    public <T> T executeWithLockWait(final Supplier<T> supplier, final String lockName) {
        return retryTemplate.execute(context -> {
            try {
                final LockingTaskExecutor.TaskResult<T> result = lockingTaskExecutor.executeWithLock(
                        supplier::get,
                        new LockConfiguration(Instant.now(), lockName, MAX_LOCK_DURATION, MIN_LOCK_DURATION));

                if (result.wasExecuted()) {
                    log.info("Lock '{}' acquired, task executed", lockName);
                    return result.getResult();
                } else {
                    log.warn(
                            "Lock '{}' not acquired, Retry count: {}, task not executed",
                            lockName,
                            context.getRetryCount());
                    // Trigger retry by throwing an exception
                    throw new ConcurrentCalculationException(
                            ReasonCode.PROCESSING_ERROR,
                            "Lock not acquired for " + lockName + " after " + context.getRetryCount() + " attempts");
                }
            } catch (ConcurrentCalculationException e) {
                log.warn("Lock '{}' not acquired (attempt {}/5). Retrying...", lockName, context.getRetryCount() + 1);
                throw e;
            }
        });
    }
}
