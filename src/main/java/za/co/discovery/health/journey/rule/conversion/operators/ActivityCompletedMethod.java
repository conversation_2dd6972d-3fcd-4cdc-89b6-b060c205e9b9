package za.co.discovery.health.journey.rule.conversion.operators;

import za.co.discovery.health.journey.remote.pacman.service.PacManService;

import java.time.LocalDateTime;

public class ActivityCompletedMethod {
    private final PacManService pacManService;
    private final Long entityId;
    private final LocalDateTime enrollmentStartTime;

    public ActivityCompletedMethod(
            final PacManService pacManService, final Long entityId, final LocalDateTime enrollmentStartTime) {
        this.pacManService = pacManService;
        this.entityId = entityId;
        this.enrollmentStartTime = enrollmentStartTime == null ? LocalDateTime.MIN : enrollmentStartTime;
    }

    public boolean isCompleted(final Long entityId, final String mnemonic, final LocalDateTime enrollmentStartTime) {
        final Boolean activityCompleted = pacManService
                .getActivityCompleted(entityId, mnemonic, enrollmentStartTime)
                .block();
        return activityCompleted != null && activityCompleted;
    }

    public boolean isCompleted(final Long entityId, final String mnemonic) {
        return isCompleted(entityId, mnemonic, enrollmentStartTime);
    }

    public boolean isCompleted(final String mnemonic, final LocalDateTime enrollmentStartTime) {
        return isCompleted(entityId, mnemonic, enrollmentStartTime);
    }

    public boolean isCompleted(final String mnemonic) {
        return isCompleted(entityId, mnemonic);
    }
}
