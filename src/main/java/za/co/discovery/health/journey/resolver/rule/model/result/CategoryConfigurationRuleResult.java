package za.co.discovery.health.journey.resolver.rule.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.resolver.rule.model.RuleResult;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.CategoryConfigurationEvaluationResult;

import java.time.Duration;
import java.time.LocalDateTime;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class CategoryConfigurationRuleResult extends RuleResult {
    private final CategoryConfiguration configuration;

    @Data
    @Builder
    @AllArgsConstructor
    public static class CategoryConfiguration {
        private final LocalDateTime enrollmentStartTime;
        private final LocalDateTime enrollmentEndTime;
        private final LocalDateTime journeyStartTime;
        private final String enrollmentPrecondition;
        private final String activityPrecondition;
        private final Long maxParticipants;
        private final Duration monitoringPeriodDuration;

        public static CategoryConfiguration of(
                final CategoryConfigurationEvaluationResult.CategoryConfigurationResult output) {
            return CategoryConfiguration.builder()
                    .enrollmentStartTime(output.getEnrollmentStartTime())
                    .enrollmentEndTime(output.getEnrollmentEndTime())
                    .journeyStartTime(output.getJourneyStartTime())
                    .activityPrecondition(output.getActivityPrecondition())
                    .enrollmentPrecondition(output.getEnrollmentPrecondition())
                    .maxParticipants(output.getMaxParticipants())
                    .monitoringPeriodDuration(
                            output.getMonitoringPeriodDuration() == null
                                    ? Duration.ZERO
                                    : output.getMonitoringPeriodDuration())
                    .build();
        }
    }
}
