package za.co.discovery.health.journey.resolver.rule.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;

import javax.annotation.PostConstruct;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RuleCalculatorResolver {

    private final List<RuleCalculator> calculators;

    private Map<RuleType, RuleCalculator> calculatorMap;

    /**
     * Convert the list of evaluators into a map so that lookups can avoid streaming.
     */
    @PostConstruct
    public void initEvaluatorMap() {
        calculatorMap = calculators.stream()
                // Collect to a map keyed by (RuleType, RuleProcessorType)
                .collect(Collectors.toMap(
                        RuleCalculator::getHandledRuleType, Function.identity(), (existing, duplicate) -> {
                            throw new CoreException(
                                    ReasonCode.GENERAL_ERROR,
                                    "Multiple calculators found for the same RuleType/RuleProcessorType combination: "
                                            + existing.getClass().getName()
                                            + " vs. "
                                            + duplicate.getClass().getName());
                        }));
    }

    /**
     * Looks up the evaluator that can handle the given RuleType and RuleProcessorType.
     *
     * @throws CoreException if no evaluator is found
     */
    public RuleCalculator getEvaluator(final RuleType type) {
        final RuleCalculator evaluator = calculatorMap.get(type);

        if (evaluator == null) {
            throw new CoreException(ReasonCode.VALIDATION_ERROR, "No calculator found for rule type " + type);
        }
        return evaluator;
    }
}
