package za.co.discovery.health.journey.rule.conversion.operators;

import lombok.RequiredArgsConstructor;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.resolver.rule.model.request.ProgramTransitionRuleRequest;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class ProgramTransitionMethod {
    private static final String COMPLETED = "COMPLETED";

    private final ProgramTransitionRuleRequest ruleRequest;

    public boolean hasCompletedAllMilestones(final ProgramTransitionRuleRequest milestoneDetails) {
        return milestoneDetails.getMilestones().stream()
                        .filter(it -> Objects.equals(it.getStatus(), COMPLETED))
                        .count()
                == milestoneDetails.getProgramDuration();
    }

    public boolean hasCompletedAllMilestones() {
        return hasCompletedAllMilestones(ruleRequest);
    }

    public boolean hasCompletedAtLeast(final ProgramTransitionRuleRequest milestoneDetails, final int count) {

        return milestoneDetails.getMilestones().stream()
                        .filter(it -> Objects.equals(it.getStatus(), COMPLETED))
                        .count()
                >= count;
    }

    public boolean hasCompletedAtLeast(final int count) {
        return hasCompletedAtLeast(ruleRequest, count);
    }

    public boolean hasCompletedMnemonics(
            final ProgramTransitionRuleRequest milestoneDetails, final String... mnemonics) {

        return new HashSet<>(milestoneDetails.getMilestones().stream()
                        .flatMap(it -> it.getActivities().stream())
                        .filter(it -> Objects.equals(it.getStatus(), COMPLETED))
                        .map(ProgramTransitionRuleRequest.ActivityDetails::getMnemonic)
                        .collect(Collectors.toList()))
                .containsAll(List.of(mnemonics));
    }

    public boolean hasCompletedMnemonics(final String... mnemonics) {
        return hasCompletedMnemonics(ruleRequest, mnemonics);
    }

    public boolean hasCompletedMnemonicsAtLeast(
            final ProgramTransitionRuleRequest milestoneDetails, final int count, final String... mnemonics) {

        return milestoneDetails.getMilestones().stream()
                        .flatMap(it -> it.getActivities().stream())
                        .filter(it -> Objects.equals(it.getStatus(), COMPLETED)
                                && List.of(mnemonics).contains(it.getMnemonic()))
                        .count()
                >= count;
    }

    public boolean hasCompletedMnemonicsAtLeast(final int count, final String... mnemonics) {
        return hasCompletedMnemonicsAtLeast(ruleRequest, count, mnemonics);
    }

    public boolean hasCompletedAtLeastAppointments(
            final ProgramTransitionRuleRequest milestoneDetails, final int count) {
        return hasCompletedMnemonicsAtLeast(
                count,
                milestoneDetails.getMilestones().stream()
                        .flatMap(it -> it.getActivities().stream())
                        .filter(it -> Objects.equals(it.getActivityType(), ActivityDetailsType.APPOINTMENT.name()))
                        .map(ProgramTransitionRuleRequest.ActivityDetails::getMnemonic)
                        .toArray(String[]::new));
    }

    public boolean hasCompletedAtLeastAppointments(final int count) {
        return hasCompletedAtLeastAppointments(ruleRequest, count);
    }

    public boolean hasCompletedAtLeastActivities(final ProgramTransitionRuleRequest milestoneDetails, final int count) {
        return hasCompletedMnemonicsAtLeast(
                count,
                milestoneDetails.getMilestones().stream()
                        .flatMap(it -> it.getActivities().stream())
                        .filter(it -> Objects.equals(it.getActivityType(), ActivityDetailsType.ACTIVITY.name()))
                        .map(ProgramTransitionRuleRequest.ActivityDetails::getMnemonic)
                        .toArray(String[]::new));
    }

    public boolean hasCompletedAtLeastActivities(final int count) {
        return hasCompletedAtLeastActivities(ruleRequest, count);
    }
}
