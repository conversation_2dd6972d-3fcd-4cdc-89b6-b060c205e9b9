package za.co.discovery.health.journey.resolver.rule.model.evaluator;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.enums.ActivityCompletionPreconditionType;
import za.co.discovery.health.journey.resolver.rule.model.output.ActivityCompletionDMNRuleOutput;

import java.util.List;
import java.util.stream.Collectors;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class ActivityCompletionRuleEvaluationResult extends RuleEvaluationResult {
    private List<ActivityCompletionResult> results;

    public static ActivityCompletionRuleEvaluationResult of(
            final ActivityCompletionDMNRuleOutput results, final boolean success) {

        if (success) {
            return ActivityCompletionRuleEvaluationResult.builder()
                    .results(results.getOutputs().stream()
                            .map(ActivityCompletionResult::of)
                            .collect(Collectors.toList()))
                    .success(true)
                    .build();
        } else {
            return ActivityCompletionRuleEvaluationResult.builder()
                    .results(List.of())
                    .success(false)
                    .build();
        }
    }

    @Data
    @SuperBuilder(toBuilder = true)
    public static class ActivityCompletionResult {
        private String identifier;
        private Long iteration;
        private ActivityCompletionPreconditionType preconditionType;

        public static ActivityCompletionResult of(final ActivityCompletionDMNRuleOutput.DMNOutput output) {
            return ActivityCompletionResult.builder()
                    .identifier(output.getIdentifier())
                    .iteration(output.getIteration())
                    .preconditionType(
                            ActivityCompletionPreconditionType.valueOf(output.getCompletionPreconditionType()))
                    .build();
        }
    }
}
