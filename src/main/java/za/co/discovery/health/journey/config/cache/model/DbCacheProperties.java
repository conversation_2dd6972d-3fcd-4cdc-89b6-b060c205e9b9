package za.co.discovery.health.journey.config.cache.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Profile;

@Profile("db-cache")
@ConfigurationProperties(prefix = "db-cache")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DbCacheProperties {
    private String name;
    private Long maxEntriesLocalHeap;
    private String memoryStoreEvictionPolicy;
}
