package za.co.discovery.health.journey.resolver.rule.model.output;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import za.co.discovery.health.journey.util.DMNConstants;
import za.co.discovery.health.journey.util.DMNUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Builder(access = AccessLevel.PRIVATE)
public class ActivityCompletionDMNRuleOutput {

    private final List<DMNOutput> outputs;

    public static ActivityCompletionDMNRuleOutput of(final List<Map<String, Object>> result) {
        if (result == null || result.isEmpty()) {
            return ActivityCompletionDMNRuleOutput.builder()
                    .outputs(Collections.emptyList())
                    .build();
        }

        final List<DMNOutput> outputs = result.stream()
                .map(ActivityCompletionDMNRuleOutput::convertToDMNOutput)
                .filter(ActivityCompletionDMNRuleOutput::hasValidMnemonic)
                .collect(Collectors.toList());

        return ActivityCompletionDMNRuleOutput.builder().outputs(outputs).build();
    }

    private static DMNOutput convertToDMNOutput(final Map<String, Object> activityMap) {
        final Long iteration = DMNUtils.toLongOrNull(activityMap.get(DMNConstants.ITERATION));
        final String identifier = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.COMPLETION_IDENTIFIER));
        final String completionPreconditionType =
                DMNUtils.toStringOrNull(activityMap.get(DMNConstants.COMPLETION_PRECONDITION_TYPE));

        return DMNOutput.builder()
                .identifier(identifier)
                .iteration(iteration)
                .completionPreconditionType(completionPreconditionType)
                .build();
    }

    private static boolean hasValidMnemonic(final DMNOutput output) {
        if (Objects.isNull(output.getIdentifier())) {
            log.error("Mnemonic is null. Skipping DMNOutput: {}", output);
            return false;
        }
        return true;
    }

    @Data
    @Builder
    public static class DMNOutput {
        private final Long iteration;
        private final String identifier;
        private final String completionPreconditionType;
    }
}
