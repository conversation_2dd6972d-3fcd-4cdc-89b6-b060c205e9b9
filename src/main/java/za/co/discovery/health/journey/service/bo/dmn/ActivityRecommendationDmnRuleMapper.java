package za.co.discovery.health.journey.service.bo.dmn;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.model.bo.dmn.ActivityRecommendationDmnRuleDto;
import za.co.discovery.health.journey.model.bo.dmn.DmnTableRow;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityRecommendationDmnRuleMapper {

    /**
     * Convert DmnTableRow to ActivityRecommendationDmnRuleDto
     */
    public ActivityRecommendationDmnRuleDto toActivityRule(final DmnTableRow tableRow) {
        if (tableRow == null) {
            return null;
        }

        return ActivityRecommendationDmnRuleDto.builder()
                .ruleId(tableRow.getRuleId())
                .description(tableRow.getDescription())

                // Map input fields
                .alliance(getInputValue(tableRow, "input_alliance"))
                .branch(getInputValue(tableRow, "input_branch"))
                .group(getInputValue(tableRow, "input_group"))
                .milestone(parseInteger(getInputValue(tableRow, "input_milestoneValue")))

                // Map output fields
                .activityName(getOutputValue(tableRow, "output_activityName"))
                .activityIcon(getOutputValue(tableRow, "output_activityIcon"))
                .completionFlexibility(parseCompletionFlexibility(getOutputValue(tableRow, "output_completionType")))
                .activity(getOutputValue(tableRow, "output_ActivityId"))
                .frequency(parseInteger(getOutputValue(tableRow, "output_frequency")))
                .activityType(parseActivityType(getOutputValue(tableRow, "output_activityType")))
                .build();
    }

    /**
     * Convert ActivityRecommendationDmnRuleDto back to DmnTableRow
     */
    public DmnTableRow toTableRow(final ActivityRecommendationDmnRuleDto rule) {
        if (rule == null) {
            return null;
        }

        final DmnTableRow tableRow = new DmnTableRow();
        tableRow.setRuleId(rule.getRuleId());
        tableRow.setDescription(rule.getDescription());

        // Set input values
        setInputValue(tableRow, "alliance", rule.getAlliance());
        setInputValue(tableRow, "branch", rule.getBranch());
        setInputValue(tableRow, "group", rule.getGroup());
        setInputValue(
                tableRow,
                "Milestone",
                rule.getMilestone() == null ? null : rule.getMilestone().toString());

        // Set output values
        setOutputValue(tableRow, "activityName", rule.getActivityName());
        setOutputValue(tableRow, "activityIcon", rule.getActivityIcon());
        setOutputValue(
                tableRow,
                "Completion Flexibility",
                rule.getCompletionFlexibility() == null
                        ? null
                        : rule.getCompletionFlexibility().getValue());
        setOutputValue(tableRow, "Activity", rule.getActivity());
        setOutputValue(
                tableRow,
                "Frequency",
                rule.getFrequency() == null ? null : rule.getFrequency().toString());
        setOutputValue(
                tableRow,
                "activityType",
                rule.getActivityType() == null ? null : rule.getActivityType().getValue());

        return tableRow;
    }

    /**
     * Convert list of DmnTableRows to list of ActivityRecommendationDmnRuleDtos
     */
    public List<ActivityRecommendationDmnRuleDto> toActivityRules(final List<DmnTableRow> tableRows) {
        if (tableRows == null) {
            return Collections.emptyList();
        }

        return tableRows.stream().map(this::toActivityRule).collect(Collectors.toList());
    }

    /**
     * Convert list of ActivityRecommendationDmnRuleDtos to list of DmnTableRows
     */
    public List<DmnTableRow> toTableRows(final List<ActivityRecommendationDmnRuleDto> rules) {
        if (rules == null) {
            return Collections.emptyList();
        }

        return rules.stream().map(this::toTableRow).collect(Collectors.toList());
    }

    // Helper methods
    private String getInputValue(final DmnTableRow tableRow, final String key) {
        final String value = tableRow.getInputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private String getOutputValue(final DmnTableRow tableRow, final String key) {
        final String value = tableRow.getOutputs().get(key);
        return value == null || value.isBlank() ? null : value.trim();
    }

    private void setInputValue(final DmnTableRow tableRow, final String key, final String value) {
        tableRow.getInputs().put(key, value == null ? "" : value);
    }

    private void setOutputValue(final DmnTableRow tableRow, final String key, final String value) {
        tableRow.getOutputs().put(key, value == null ? "" : value);
    }

    private Integer parseInteger(final String value) {
        if (value == null || value.isBlank()) {
            return null;
        }

        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            // Log the error or handle as needed
            log.error("Failed to parse integer: {}, Error: {}", value, e.getMessage());
            return null;
        }
    }

    private ActivityRecommendationDmnRuleDto.CompletionFlexibility parseCompletionFlexibility(final String value) {
        if (value == null || value.isBlank()) {
            return ActivityRecommendationDmnRuleDto.CompletionFlexibility.MANDATORY; // Default
        }

        return ActivityRecommendationDmnRuleDto.CompletionFlexibility.fromValue(value.trim());
    }

    private ActivityRecommendationDmnRuleDto.ActivityType parseActivityType(final String value) {
        if (value == null || value.isBlank()) {
            return ActivityRecommendationDmnRuleDto.ActivityType.ACTIVITY; // Default
        }

        return ActivityRecommendationDmnRuleDto.ActivityType.fromValue(value.trim());
    }
}
