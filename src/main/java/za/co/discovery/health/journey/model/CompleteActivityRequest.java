package za.co.discovery.health.journey.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
public class CompleteActivityRequest {
    private String mnemonic;
    private Long activityTnxId;
    private Long entityId;
    private String value;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime completedAt;

    private ActivityDetailsType activityDetailsType;
}
