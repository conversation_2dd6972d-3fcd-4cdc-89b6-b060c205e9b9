package za.co.discovery.health.journey.resolver.rule.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum Flexibility {
    OPTIONAL("Optional", "O"),
    MANDATORY("Mandatory", "M");
    private final String value;
    private final String dbValue;

    public static Flexibility fromValue(final String value) {
        for (final Flexibility f : Flexibility.values()) {
            if (f.value.equalsIgnoreCase(value)) {
                return f;
            }
        }
        throw new IllegalArgumentException("Unknown Flexibility value: " + value);
    }

    public static Flexibility fromDbValue(final String value) {
        for (final Flexibility f : Flexibility.values()) {
            if (f.dbValue.equalsIgnoreCase(value)) {
                return f;
            }
        }
        throw new IllegalArgumentException("Unknown Flexibility value: " + value);
    }
}
