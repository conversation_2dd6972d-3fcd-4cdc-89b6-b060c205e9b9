package za.co.discovery.health.journey.event.handler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.config.KafkaTopics;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivityTxn;
import za.co.discovery.health.journey.event.EnrollmentCreatedEvent;
import za.co.discovery.health.journey.event.JourneyDrivenActivityCompletedEvent;
import za.co.discovery.health.journey.messaging.model.ActivityCompletionRequestMessage;
import za.co.discovery.health.journey.messaging.model.JourneyEnrollmentEvent;
import za.co.discovery.health.journey.messaging.model.NotificationTemplate;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.service.journey.CoachingJourneyService;
import za.co.discovery.health.journey.util.Constants;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Slf4j
@Component
@Profile("kafka")
@RequiredArgsConstructor
public class ExternalProviderEventHandler {

    private final KafkaTemplate<String, JourneyEnrollmentEvent> enrollmentEvent;
    private final KafkaTemplate<String, ActivityCompletionRequestMessage> activityCompletionRequestMessageKafkaTemplate;
    private final KafkaTemplate<String, NotificationTemplate> notificationTemplateKafkaTemplate;
    private final CoachingJourneyService coachingJourneyService;

    @EventListener
    @Transactional
    public void handleEnrollmentCreated(final EnrollmentCreatedEvent event) {
        final JourneyEnrollment enrollment = event.getEnrollment();
        log.info("Publishing enrollment created event for enrollment: {}", enrollment.getJourneyEnrollmentId());

        enrollmentEvent.send(
                KafkaTopics.JOURNEY_ENROLLMENT_EVENT,
                JourneyEnrollmentEvent.builder()
                        .enrollmentId(enrollment.getJourneyEnrollmentId())
                        .entityId(enrollment.getEntityId())
                        .categoryId(enrollment.getJourneyCategory().getJourneyCategoryId())
                        .programId(enrollment.getJourneyProgram().getJourneyProgramId())
                        .categoryType(enrollment
                                .getJourneyCategory()
                                .getJourneyCategoryType()
                                .getName())
                        .externalReference(enrollment.getJourneyCategory().getExternalReference())
                        .enrollmentDate(enrollment.getEnrollmentDate())
                        .build());

        if (Constants.COACHING_JOURNEY_CATEGORY_TYPE_NAME.equals(
                enrollment.getJourneyCategory().getJourneyCategoryType().getName())) {

            final LocalDateTime endDate = event.getJourneyStartTime()
                    .plus(
                            enrollment
                                    .getJourneyProgram()
                                    .getJourneyProgramBehaviour()
                                    .getProgramDuration(),
                            ChronoUnit.valueOf(enrollment
                                    .getJourneyProgram()
                                    .getJourneyProgramBehaviour()
                                    .getJourneyMilestone()
                                    .getDescription()));

            final NotificationTemplate notification = coachingJourneyService.createEnrollmentNotification(
                    enrollment, event.getJourneyStartTime(), endDate);

            notificationTemplateKafkaTemplate.send(KafkaTopics.NOTIFICATION_MANAGER_SEND, notification);
        }

        log.info("Enrollment created event published for enrollment: {}", enrollment.getJourneyEnrollmentId());
    }

    @EventListener
    @Transactional
    public void handleJourneyDrivenActivityCompleted(final JourneyDrivenActivityCompletedEvent event) {
        log.info(
                "Handling activity completed event for enrollment: {}",
                event.getActivity().getJourneyEnrollmentMilestoneActivityId());

        final boolean isJourneyDrivenActivity =
                event.getActivity().getActivityType().equals(ActivityDetailsType.JOURNEY_DRIVEN_ACTIVITY.name());

        if (isJourneyDrivenActivity) {
            final String value = event.getActivity().getJourneyEnrollmentMilestoneActivityTxns().stream()
                    .findFirst()
                    .map(JourneyEnrollmentMilestoneActivityTxn::getValue)
                    .orElse(null);
            activityCompletionRequestMessageKafkaTemplate.send(
                    KafkaTopics.ACTIVITY_COMPLETION_REQUEST,
                    ActivityCompletionRequestMessage.builder()
                            .entityNo(event.getUserId())
                            .activityValue(value)
                            .mnemonic(event.getActivity().getActivityMnemonicId())
                            .date(event.getActivity()
                                    .getJourneyEnrollmentMilestone()
                                    .getMilestoneFrom()
                                    .toLocalDate())
                            .build());
        }
    }
}
