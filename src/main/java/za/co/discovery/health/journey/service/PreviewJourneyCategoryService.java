package za.co.discovery.health.journey.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategoryRepository;
import za.co.discovery.health.journey.mapper.JourneyEnrollmentMapper;
import za.co.discovery.health.journey.model.preview.JourneyCategoryPreview;
import za.co.discovery.health.journey.model.preview.JourneyMilestonePreview;
import za.co.discovery.health.journey.model.preview.JourneyProgramPreview;
import za.co.discovery.health.journey.model.preview.JourneyRewardPreview;
import za.co.discovery.health.journey.model.preview.LimitedJourneyCategoryPreview;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.service.journey.JourneyOrderService;
import za.co.discovery.health.journey.service.journey.JourneyProgramService;
import za.co.discovery.health.journey.service.journey.JourneyService;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyProgramRewardFacade;
import za.co.discovery.health.journey.strategy.ActivityCompletionValidator;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PreviewJourneyCategoryService {

    private final JourneyService journeyService;

    private final JourneyOrderService orderService;

    private final JourneyProgramService programService;

    private final JourneyProgramRewardFacade rewardFacade;

    private final JourneyEnrollmentMapper journeyEnrollmentMapper;

    private final ExtendedJourneyCategoryRepository categoryRepository;
    private final ActivityCompletionValidator activityCompletionValidator;

    public List<LimitedJourneyCategoryPreview> getLimitedJourneyCategoriesPreview(
            final Long entityId, final LocalDateTime time) {
        final var recommendations = journeyService.getRecommendedCategories(entityId);

        return recommendations.stream()
                .map(recommendation -> {
                    final var category = categoryRepository
                            .findByCategoryCode(recommendation.getCategory())
                            .orElseThrow(() -> new CoreException(
                                    ReasonCode.VALIDATION_ERROR,
                                    "No category found for code: " + recommendation.getCategory()));

                    final CategoryConfigurationRuleResult.CategoryConfiguration categoryConfiguration =
                            journeyService.getCategoryConfiguration(entityId, category);

                    final var categoryPrograms =
                            programService.getProgramsByCategory(category.getJourneyCategoryId(), time);

                    if (categoryPrograms.isEmpty()) {
                        return null;
                    }

                    final long totalJourneyDuration = categoryPrograms.stream()
                            .collect(Collectors.summarizingLong(
                                    it -> it.getJourneyProgramBehaviour().getProgramDuration()))
                            .getSum();

                    final String durationUnit = categoryPrograms.stream()
                            .map(it -> it.getJourneyProgramBehaviour()
                                    .getJourneyMilestone()
                                    .getDescription())
                            .findFirst()
                            .orElseThrow(() -> new CoreException(
                                    ReasonCode.VALIDATION_ERROR,
                                    "No duration unit found for category: " + category.getCategoryCode()));

                    final String activityPrecondition = categoryConfiguration.getActivityPrecondition();

                    return LimitedJourneyCategoryPreview.builder()
                            .id(category.getJourneyCategoryId())
                            .name(category.getName())
                            .categoryType(category.getJourneyCategoryType().getName())
                            .externalReference(category.getExternalReference())
                            .journeyDuration(totalJourneyDuration)
                            .journeyDurationUnit(ChronoUnit.valueOf(durationUnit))
                            .enrollmentStartTime(categoryConfiguration.getEnrollmentStartTime())
                            .enrollmentEndTime(categoryConfiguration.getEnrollmentEndTime())
                            .startTime(categoryConfiguration.getJourneyStartTime())
                            .preconditions(
                                    activityPrecondition == null ? null : List.of(activityPrecondition.split(",")))
                            .maxParticipants(categoryConfiguration.getMaxParticipants())
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public Optional<LimitedJourneyCategoryPreview> getLimitedJourneyCategoryPreview(
            final Long entityId, final Long categoryId, final LocalDateTime time) {
        return getLimitedJourneyCategoriesPreview(entityId, time).stream()
                .filter(it -> it.getId().equals(categoryId))
                .findFirst();
    }

    public Optional<JourneyCategoryPreview> getJourneyCategoryPreview(
            final Long entityId, final Long categoryId, final LocalDateTime time) {
        return getLimitedJourneyCategoryPreview(entityId, categoryId, time)
                .map(categoryPreview -> new JourneyCategoryPreview(categoryPreview.toBuilder())
                        .toBuilder()
                                .programs(getJourneyPrograms(entityId, categoryId, time))
                                .build());
    }

    private List<JourneyProgramPreview> getJourneyPrograms(
            final long entityId, final long categoryId, final LocalDateTime time) {
        return programService.getProgramsByCategory(categoryId, time).parallelStream()
                .map(it -> JourneyProgramPreview.builder()
                        .programId(it.getJourneyProgramId())
                        .programName(it.getName())
                        .order(orderService.getOrderByCategoryAndProgram(it.getJourneyProgramId(), categoryId, time))
                        .behaviour(journeyEnrollmentMapper.getJourneyBehaviourDto(it.getJourneyProgramBehaviour()))
                        .rewards(getProgramRewards(entityId, it.getJourneyProgramId(), time))
                        .milestones(getMilestones(entityId, it.getJourneyProgramId(), time))
                        .build())
                .sorted(Comparator.comparing(JourneyProgramPreview::getOrder))
                .collect(Collectors.toList());
    }

    private List<JourneyRewardPreview> getProgramRewards(
            final Long entityId, final Long programId, final LocalDateTime time) {
        return rewardFacade.getProgramRewards(entityId, programId, time).parallelStream()
                .map(it -> JourneyRewardPreview.builder()
                        .rewardType(it.getRewardType())
                        .rewardValue(it.getRewardValue())
                        .build())
                .collect(Collectors.toList());
    }

    private List<JourneyMilestonePreview> getMilestones(
            final long entityId, final long programId, final LocalDateTime time) {
        return programService.getAllMilestoneActivities(programId, entityId).entrySet().parallelStream()
                .map(it -> {
                    final List<JourneyMilestonePreview.ActivityDetails> activityDetails = it.getValue().stream()
                            .map(activity -> JourneyMilestonePreview.ActivityDetails.builder()
                                    .activityAmount(activity.getCount())
                                    .type(activity.getType())
                                    .mnemonic(activity.getMnemonic())
                                    .icon(activity.getIcon())
                                    .name(activity.getName())
                                    .isMandatory(activity.getFlexibility().equals(Flexibility.MANDATORY))
                                    .preconditionResults(activityCompletionValidator.evaluateActivityCompletion(
                                            entityId,
                                            it.getKey(),
                                            activity.getMnemonic(),
                                            activity.getType(),
                                            time,
                                            programId))
                                    .build())
                            .collect(Collectors.toList());

                    final List<JourneyRewardPreview> rewards =
                            rewardFacade.getMilestoneReward(entityId, it.getKey(), programId, time).stream()
                                    .map(rewardProjection -> JourneyRewardPreview.builder()
                                            .rewardType(rewardProjection.getRewardType())
                                            .rewardValue(rewardProjection.getRewardValue())
                                            .build())
                                    .collect(Collectors.toList());
                    return JourneyMilestonePreview.builder()
                            .iteration(it.getKey())
                            .activities(activityDetails)
                            .rewards(rewards)
                            .build();
                })
                .collect(Collectors.toList());
    }
}
