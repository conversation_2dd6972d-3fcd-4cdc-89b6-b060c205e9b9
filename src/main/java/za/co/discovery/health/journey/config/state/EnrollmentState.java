package za.co.discovery.health.journey.config.state;

public enum EnrollmentState {
    // Initial state before any processing
    INITIAL,

    // Active enrollment with ongoing milestones
    ACTIVE,

    // Currently evaluating milestones/completion criteria
    EVALUATING,

    // Successfully completed all requirements
    COMPLETED,

    // Enrollment expired without completion
    EXPIRED,

    // Temporary pause in progression
    SUSPENDED,

    // Error state for invalid transitions
    ERROR;

    public boolean isTerminal() {
        return this == COMPLETED || this == EXPIRED || this == ERROR;
    }
}
