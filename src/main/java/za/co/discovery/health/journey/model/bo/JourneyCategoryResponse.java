package za.co.discovery.health.journey.model.bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class JourneyCategoryResponse {

    private Long id;
    private String categoryCode;
    private String name;
    private List<CategoryDetail> categoryDetails;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryDetail {
        private String activityPrecondition;
        private LocalDateTime startEnrollment;
        private LocalDateTime endEnrollment;
        private LocalDateTime startJourney;
    }
}
