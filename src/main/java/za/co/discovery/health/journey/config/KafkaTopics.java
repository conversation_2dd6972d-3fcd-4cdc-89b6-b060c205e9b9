package za.co.discovery.health.journey.config;

import lombok.experimental.UtilityClass;

@UtilityClass
public class KafkaTopics {
    public static final String ACTIVITY_TRANSACTION_EVENT = "activity-transaction-event";
    public static final String MEMBER_JOINED_CALL_EVENT = "member-joined-call";
    public static final String MEMBER_APPOINTMENT_RECONCILIATION_EVENT = "gc-attendee-state-update";
    public static final String JOURNEY_ENROLLMENT_EVENT = "journey-enrollment-event";
    public static final String NOTIFICATION_MANAGER_SEND = "notification-manager-send";
    public static final String SPIN_REWARD_PAYOFF_REQUEST = "spin-reward-payoff-request";
    public static final String POINT_REWARD_PAYOFF_REQUEST = "point-reward-payoff-request";
    public static final String GIFT_CARD_REWARD_PAYOFF_REQUEST = "gift-card-reward-payoff-request";
    public static final String GIFT_CARD_ADDED_TO_WHEEL_REWARD_PAYOFF_REQUEST =
            "gift-card-added-to-wheel-reward-payoff-request";
    public static final String ACTIVITY_BASED_POINT_REWARD_PAYOFF_REQUEST =
            "activity_based_point-reward-payoff-request";
    public static final String ACTIVITY_COMPLETION_REQUEST = "activity-completion-request";
}
