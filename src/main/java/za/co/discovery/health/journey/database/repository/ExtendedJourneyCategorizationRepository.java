package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategorization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyCategorizationRepository extends JourneyCategorizationRepository {

    @Query("SELECT jc FROM JourneyCategorization jc "
            + "WHERE jc.journeyProgram = ?1 "
            + "AND jc.journeyCategory = ?2 "
            + "AND jc.id.effFrom = ?3")
    Optional<JourneyCategorization> findByKey(
            JourneyProgram journeyProgram, JourneyCategory journeyCategory, LocalDateTime effFrom);

    @Query("SELECT jc FROM JourneyCategorization jc "
            + "WHERE jc.journeyCategory.journeyCategoryId = ?1 "
            + "AND jc.id.effFrom <= ?2 AND jc.status = 'ACTIVE'")
    List<JourneyCategorization> findByJourneyCategory(Long journeyCategoryId, LocalDateTime effFrom);
}
