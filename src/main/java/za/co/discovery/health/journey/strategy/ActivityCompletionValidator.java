package za.co.discovery.health.journey.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramRepository;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.ActivityStatus;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;
import za.co.discovery.health.journey.service.precondition.ActivityPreconditionEvaluator;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ActivityCompletionValidator {

    private final ActivityPreconditionEvaluator preconditionEvaluator;
    private final ExtendedJourneyProgramRepository extendedJourneyProgramRepository;

    @Transactional
    public boolean canActivityBeCompleted(final JourneyEnrollmentMilestoneActivity activity, final LocalDateTime time) {
        return evaluateActivityCompletion(activity, time).isEmpty();
    }

    @Transactional
    public List<PreconditionEvaluationResult> evaluateActivityCompletion(
            final JourneyEnrollmentMilestoneActivity activity, final LocalDateTime time) {
        final ActivityStatus activityStatus = ActivityStatus.fromValue(activity.getActivityStatus());

        if (activityStatus.equals(ActivityStatus.COMPLETED) || activityStatus.equals(ActivityStatus.LATE_COMPLETED)) {
            return List.of();
        }

        final JourneyProgramBehaviour behaviour = extractBehaviour(activity);
        final JourneyEnrollmentMilestone journeyEnrollmentMilestone = activity.getJourneyEnrollmentMilestone();
        final Long iteration = journeyEnrollmentMilestone.getMilestoneIteration();
        final Long entityId = journeyEnrollmentMilestone.getJourneyEnrollment().getEntityId();
        final String mnemonic = activity.getActivityMnemonicId();
        final ActivityDetailsType activityType = ActivityDetailsType.fromValue(activity.getActivityType());

        final List<PreconditionEvaluationResult> results = preconditionEvaluator.evaluateAllPreconditions(
                entityId, iteration, mnemonic, activityType, time, behaviour);

        return results.stream().filter(result -> !result.isSatisfied()).collect(Collectors.toList());
    }

    @Transactional
    public List<PreconditionEvaluationResult> evaluateActivityCompletion(
            final Long entityId,
            final Long iteration,
            final String mnemonic,
            final ActivityDetailsType activityType,
            final LocalDateTime time,
            final Long programId) {
        return extendedJourneyProgramRepository
                .findById(programId)
                .map(program -> preconditionEvaluator.evaluateAllPreconditions(
                        entityId, iteration, mnemonic, activityType, time, program.getJourneyProgramBehaviour()))
                .orElse(List.of())
                .stream()
                .filter(result -> !result.isSatisfied())
                .collect(Collectors.toList());
    }

    private JourneyProgramBehaviour extractBehaviour(final JourneyEnrollmentMilestoneActivity activity) {
        return activity.getJourneyEnrollmentMilestone()
                .getJourneyEnrollment()
                .getJourneyProgram()
                .getJourneyProgramBehaviour();
    }
}
