package za.co.discovery.health.journey.rule.dmn.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.databaseMapping.UserJourneyNotification;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.rule.dmn.service.CategoryConfigurationUpdateService;
import za.co.discovery.health.journey.service.journey.JourneyService;
import za.co.discovery.health.journey.service.notification.UserJourneyNotificationService;

import javax.transaction.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CategoryConfigurationUpdateServiceImpl implements CategoryConfigurationUpdateService {

    private final UserJourneyNotificationService notificationService;
    private final JourneyService journeyService;

    @Override
    @Transactional
    public void processAllCategoryConfigurationUpdates() {
        final List<UserJourneyNotification> notifiableCategories = notificationService.findScheduledNotifications();

        notifiableCategories.forEach(this::processCategoryConfigurationUpdate);
    }

    private void processCategoryConfigurationUpdate(final UserJourneyNotification notification) {
        final CategoryConfigurationRuleResult.CategoryConfiguration newConfig = journeyService.getCategoryConfiguration(
                notification.getAlliance(),
                notification.getCustomer(),
                notification.getBranch(),
                notification.getJourneyCategory());

        if (newConfig == null) {
            notificationService.cancelNotification(notification);
            return;
        }
        processNotificationUpdate(notification, newConfig);
    }

    private void processNotificationUpdate(
            final UserJourneyNotification notification,
            final CategoryConfigurationRuleResult.CategoryConfiguration newConfig) {
        final LocalDateTime newStartTime = newConfig.getEnrollmentStartTime();
        final LocalDateTime newEndTime = newConfig.getEnrollmentEndTime();

        // Check if dates actually changed
        if (notification.getEnrollmentStartTime().equals(newStartTime)
                && notification.getEnrollmentEndTime().equals(newEndTime)) {
            return;
        }

        notificationService.updateNotificationStartEndTimes(
                notification, newConfig.getEnrollmentStartTime(), newConfig.getEnrollmentEndTime());
    }
}
