package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRecommendationOrder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyRecommendationOrderRepository extends JourneyRecommendationOrderRepository {
    @Query("SELECT jb FROM JourneyRecommendationOrder jb "
            + "WHERE jb.journeyProgram = :program AND jb.journeyCategory = :category "
            + "and jb.status = 'ACTIVE' "
            + "and jb.effFrom <= current_timestamp and jb.effTo >= current_timestamp ")
    Optional<JourneyRecommendationOrder> findByProgramAndCategory(JourneyProgram program, JourneyCategory category);

    @Query("SELECT jb FROM JourneyRecommendationOrder jb "
            + "WHERE jb.journeyProgram.journeyProgramId = :program "
            + "AND jb.journeyCategory.journeyCategoryId = :category "
            + "and jb.status = 'ACTIVE' "
            + "and jb.effFrom <= :time and jb.effTo >= :time ")
    Optional<JourneyRecommendationOrder> findByProgramIdAndCategoryId(Long program, Long category, LocalDateTime time);

    @Query("SELECT o FROM JourneyRecommendationOrder o "
            + "WHERE o.id.journeyCategoryId = :categoryId "
            + "  AND o.status = 'ACTIVE'")
    List<JourneyRecommendationOrder> findCurrentProgramsForCategory(@Param("categoryId") Long categoryId);

    @Query("SELECT jb FROM JourneyRecommendationOrder jb "
            + "WHERE jb.journeyProgram.journeyProgramId in (:programIds) "
            + "AND jb.journeyCategory.journeyCategoryId = :category "
            + "and jb.status = 'ACTIVE' "
            + "and jb.effFrom <= :time and jb.effTo >= :time ")
    List<JourneyRecommendationOrder> findByProgramIdInAndCategoryId(
            List<Long> programIds, long category, LocalDateTime time);
}
