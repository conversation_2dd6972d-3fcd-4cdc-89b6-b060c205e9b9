package za.co.discovery.health.journey.service.bo;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategorization;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategorizationRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategoryRepository;
import za.co.discovery.health.journey.model.bo.ActivityDetails;
import za.co.discovery.health.journey.model.bo.ActivityPlacement;
import za.co.discovery.health.journey.model.bo.JourneyActivityResponse;
import za.co.discovery.health.journey.model.bo.JourneyCategoryResponse;
import za.co.discovery.health.journey.model.bo.JourneyProgramResponse;
import za.co.discovery.health.journey.model.bo.dmn.ActivityRecommendationDmnRuleDto;
import za.co.discovery.health.journey.model.bo.dmn.JourneyActivationDmnDto;
import za.co.discovery.health.journey.remote.pacman.service.PacManService;
import za.co.discovery.health.journey.resolver.rule.model.result.GetActivityRuleResult;
import za.co.discovery.health.journey.service.bo.dmn.JourneyDmnMapper;
import za.co.discovery.health.journey.service.journey.JourneyProgramService;
import za.co.discovery.health.journey.service.journey.JourneyService;
import za.co.discovery.health.pacman.domain.Activity;
import za.co.discovery.health.pacman.domain.ActivityAssocAttribute;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@SuppressWarnings({"PMD.MethodArgumentCouldBeFinal", "PMD.ExcessiveImports"})
public class BoJourneyService {
    private final PacManService pacManService;
    private final JourneyService journeyService;
    private final JourneyDmnMapper journeyDmnMapper;
    private final JourneyProgramService journeyProgramService;
    private final ExtendedJourneyCategorizationRepository extendedJourneyCategorizationRepository;
    private final ExtendedJourneyCategoryRepository journeyCategoryRepository;

    public JourneyActivityResponse getJourneyActivities(final Long entityId, final Long categoryId) {
        final List<JourneyCategorization> byJourneyCategory =
                extendedJourneyCategorizationRepository.findByJourneyCategory(categoryId, LocalDateTime.now());

        final AtomicReference<String> categoryName = new AtomicReference<>();

        final Map<JourneyCategory, List<JourneyProgram>> categoryProgramMap = byJourneyCategory.stream()
                .collect(Collectors.groupingBy(
                        JourneyCategorization::getJourneyCategory,
                        Collectors.mapping(JourneyCategorization::getJourneyProgram, Collectors.toList())));

        final List<ActivityDetails> activityResponses = categoryProgramMap.entrySet().stream()
                .flatMap(
                        entrySet -> {
                            final JourneyCategory journeyCategory = entrySet.getKey();
                            final List<JourneyProgram> programs = entrySet.getValue();
                            categoryName.set(journeyCategory.getName());

                            final String activityPrecondition = journeyService
                                    .getCategoryConfiguration(entityId, journeyCategory)
                                    .getActivityPrecondition();

                            final ActivityDetails enrollmentActivity = ActivityDetails.builder()
                                    .mnemonic(activityPrecondition)
                                    .placement(ActivityPlacement.PRE_ENROLLMENT)
                                    .build();
                            final Stream<ActivityDetails> programActivities = programs.stream()
                                    .flatMap(program -> getProgramActivities(
                                            program, entityId, ActivityRecommendationDmnRuleDto.ActivityType.ACTIVITY)
                                            .stream());
                            return Stream.concat(Stream.of(enrollmentActivity), programActivities);
                        })
                .distinct()
                .collect(Collectors.toList());

        final Map<String, List<ActivityDetails>> collect =
                activityResponses.stream().collect(Collectors.groupingBy(ActivityDetails::getMnemonic));

        final Map<String, Activity> activityMap =
                pacManService.filterActivity(collect.keySet()).block(Duration.of(10, ChronoUnit.SECONDS));

        final List<ActivityDetails> activityDetails = activityResponses.stream()
                .peek(activity -> {
                    if (activityMap != null && activityMap.containsKey(activity.getMnemonic())) {
                        activity.setActivityName(mapActivityName(activityMap.get(activity.getMnemonic())));
                        activity.setSurveyName(mapSurveyName(activityMap.get(activity.getMnemonic())));
                        activity.setRootCategoryName(
                                activityMap.get(activity.getMnemonic()).getRootCategory());
                    }
                })
                .collect(Collectors.toList());
        return JourneyActivityResponse.builder()
                .activities(activityDetails)
                .categoryName(categoryName.get())
                .categoryId(categoryId)
                .build();
    }

    public JourneyActivityResponse getJourneyActivities(final Long categoryId) {
        final List<JourneyCategorization> byJourneyCategory =
                extendedJourneyCategorizationRepository.findByJourneyCategory(categoryId, LocalDateTime.now());

        final AtomicReference<String> categoryName = new AtomicReference<>();

        final Map<JourneyCategory, List<JourneyProgram>> categoryProgramMap = byJourneyCategory.stream()
                .collect(Collectors.groupingBy(
                        JourneyCategorization::getJourneyCategory,
                        Collectors.mapping(JourneyCategorization::getJourneyProgram, Collectors.toList())));

        final List<ActivityDetails> activityResponses = categoryProgramMap.entrySet().stream()
                .flatMap(
                        entrySet -> {
                            final JourneyCategory journeyCategory = entrySet.getKey();
                            final List<JourneyProgram> programs = entrySet.getValue();
                            categoryName.set(journeyCategory.getName());

                            final List<ActivityDetails> enrollmentActivities = journeyDmnMapper
                                    .getCategoryConfigurations(
                                            journeyCategory.getJourneyRules().getRuleSetName())
                                    .stream()
                                    .map(it -> ActivityDetails.builder()
                                            .mnemonic(it.getActivityPrecondition())
                                            .placement(ActivityPlacement.PRE_ENROLLMENT)
                                            .build())
                                    .collect(Collectors.toList());

                            final List<ActivityDetails> programActivities = programs.stream()
                                    .flatMap(program ->
                                            getProgramActivities(
                                                    program, ActivityRecommendationDmnRuleDto.ActivityType.ACTIVITY)
                                                    .stream())
                                    .collect(Collectors.toList());

                            enrollmentActivities.addAll(programActivities);

                            // Return a stream instead of a list
                            return enrollmentActivities.stream();
                        })
                .distinct()
                .collect(Collectors.toList());

        final Map<String, List<ActivityDetails>> collect =
                activityResponses.stream().collect(Collectors.groupingBy(ActivityDetails::getMnemonic));

        final Map<String, Activity> activityMap =
                pacManService.filterActivity(collect.keySet()).block(Duration.of(10, ChronoUnit.SECONDS));

        final List<ActivityDetails> activityDetails = activityResponses.stream()
                .peek(activity -> {
                    if (activityMap != null && activityMap.containsKey(activity.getMnemonic())) {
                        activity.setActivityName(mapActivityName(activityMap.get(activity.getMnemonic())));
                        activity.setSurveyName(mapSurveyName(activityMap.get(activity.getMnemonic())));
                        activity.setRootCategoryName(
                                activityMap.get(activity.getMnemonic()).getRootCategory());
                    }
                })
                .collect(Collectors.toList());
        return JourneyActivityResponse.builder()
                .activities(activityDetails)
                .categoryName(categoryName.get())
                .categoryId(categoryId)
                .build();
    }

    @Transactional(readOnly = true)
    public Page<JourneyCategoryResponse> getAllJourneyCategories(final Pageable pageable) {
        final Map<String, List<JourneyActivationDmnDto>> activityPrecondition =
                journeyDmnMapper.getJourneyActivation().stream()
                        .collect(Collectors.groupingBy(JourneyActivationDmnDto::getJourneyCategory));

        return journeyCategoryRepository.findAll(pageable).map(category -> JourneyCategoryResponse.builder()
                .id(category.getJourneyCategoryId())
                .categoryCode(category.getCategoryCode())
                .name(category.getName())
                .categoryDetails(
                        activityPrecondition.getOrDefault(category.getCategoryCode(), new ArrayList<>()).stream()
                                .map(dto -> JourneyCategoryResponse.CategoryDetail.builder()
                                        .activityPrecondition(dto.getActivityPrecondition())
                                        .startEnrollment(dto.getEnrollmentStartTime())
                                        .endEnrollment(dto.getEnrollmentEndTime())
                                        .startJourney(dto.getJourneyStartTime())
                                        .build())
                                .collect(Collectors.toList()))
                .build());
    }

    @Transactional(readOnly = true)
    public List<JourneyProgramResponse> getJourneyPrograms(final Long categoryId) {
        return extendedJourneyCategorizationRepository.findByJourneyCategory(categoryId, LocalDateTime.now()).stream()
                .collect(Collectors.groupingBy(
                        JourneyCategorization::getJourneyCategory,
                        Collectors.mapping(JourneyCategorization::getJourneyProgram, Collectors.toList())))
                .values()
                .stream()
                .flatMap(Collection::stream)
                .map(program -> JourneyProgramResponse.builder()
                        .id(program.getJourneyProgramId())
                        .name(program.getName())
                        .description(program.getDescription())
                        .duration(program.getJourneyProgramBehaviour().getProgramDuration())
                        .durationUnit(ChronoUnit.valueOf(program.getJourneyProgramBehaviour()
                                .getJourneyMilestone()
                                .getDescription()))
                        .activities(getProgramActivities(program, null))
                        .build())
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<JourneyProgramResponse> getJourneyPrograms(final Long categoryId, final Long entityId) {
        return extendedJourneyCategorizationRepository.findByJourneyCategory(categoryId, LocalDateTime.now()).stream()
                .collect(Collectors.groupingBy(
                        JourneyCategorization::getJourneyCategory,
                        Collectors.mapping(JourneyCategorization::getJourneyProgram, Collectors.toList())))
                .values()
                .stream()
                .flatMap(Collection::stream)
                .map(program -> JourneyProgramResponse.builder()
                        .id(program.getJourneyProgramId())
                        .name(program.getName())
                        .description(program.getDescription())
                        .duration(program.getJourneyProgramBehaviour().getProgramDuration())
                        .durationUnit(ChronoUnit.valueOf(program.getJourneyProgramBehaviour()
                                .getJourneyMilestone()
                                .getDescription()))
                        .activities(getProgramActivities(program, entityId, null))
                        .build())
                .collect(Collectors.toList());
    }

    private List<ActivityDetails> getProgramActivities(
            final JourneyProgram program, final ActivityRecommendationDmnRuleDto.ActivityType activityType) {
        final String ruleSetName = program.getJourneyProgramBehaviour()
                .getJourneyRulesByProgramActivityRecommendationRulesId()
                .getRuleSetName();
        final List<ActivityRecommendationDmnRuleDto> activityRecommendations;

        if (activityType == null) {
            activityRecommendations = journeyDmnMapper.getActivityRecommendations(ruleSetName);
        } else {
            activityRecommendations = journeyDmnMapper.getActivityRecommendations(ruleSetName, activityType);
        }

        return activityRecommendations.stream()
                .map(dmnRuleDto -> ActivityDetails.builder()
                        .mnemonic(dmnRuleDto.getActivity())
                        .programName(program.getName())
                        .programId(program.getJourneyProgramId())
                        .placement(ActivityPlacement.PROGRAM)
                        .iteration(Long.valueOf(dmnRuleDto.getMilestone()))
                        .activityType(dmnRuleDto.getActivityType())
                        .build())
                .distinct()
                .collect(Collectors.toList());
    }

    public List<ActivityDetails> getProgramActivities(
            final JourneyProgram program,
            final Long entityId,
            final ActivityRecommendationDmnRuleDto.ActivityType activityType) {
        final Map<Long, List<GetActivityRuleResult.RecommendedActivities>> activities =
                journeyProgramService.getAllMilestoneActivities(program.getJourneyProgramId(), entityId);

        return activities.entrySet().stream()
                .flatMap(entry -> {
                    final Long milestoneId = entry.getKey();
                    final List<GetActivityRuleResult.RecommendedActivities> recommendedActivities = entry.getValue();

                    return recommendedActivities.stream().map(activity -> ActivityDetails.builder()
                            .mnemonic(activity.getMnemonic())
                            .programName(program.getName())
                            .programId(program.getJourneyProgramId())
                            .placement(ActivityPlacement.PROGRAM)
                            .iteration(milestoneId)
                            .activityType(
                                    ActivityRecommendationDmnRuleDto.ActivityType.fromActivityType(activity.getType()))
                            .build());
                })
                .filter(it -> activityType == null || it.getActivityType().equals(activityType))
                .distinct()
                .collect(Collectors.toList());
    }

    private String mapSurveyName(final Activity activity) {
        if (activity == null) {
            return null;
        }
        return Objects.requireNonNullElse(
                        activity.getActivityAssocAttributes(), new ArrayList<ActivityAssocAttribute>())
                .stream()
                .filter(attribute -> attribute.getActivityAttribute() != null
                        && "survey_name".equals(attribute.getActivityAttribute().getDescr()))
                .findFirst()
                .map(attribute -> {
                    if (attribute.getValue() != null) {
                        return attribute.getValue();
                    }
                    return null;
                })
                .orElse(null);
    }

    private String mapActivityName(final Activity activity) {
        if (activity == null) {
            return "Unknown Activity";
        }
        return Objects.requireNonNullElse(
                        activity.getActivityAssocAttributes(), new ArrayList<ActivityAssocAttribute>())
                .stream()
                .filter(attribute -> attribute.getActivityAttribute() != null
                        && "activity_name_override"
                                .equals(attribute.getActivityAttribute().getDescr()))
                .findFirst()
                .map(attribute -> {
                    if (attribute.getValue() != null) {
                        return attribute.getValue();
                    }
                    return activity.getActivityEvent();
                })
                .orElse(activity.getDescr());
    }
}
