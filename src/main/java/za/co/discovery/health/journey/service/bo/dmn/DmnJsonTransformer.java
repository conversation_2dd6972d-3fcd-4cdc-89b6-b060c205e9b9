package za.co.discovery.health.journey.service.bo.dmn;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import za.co.discovery.health.journey.model.bo.dmn.DecisionRule;
import za.co.discovery.health.journey.model.bo.dmn.DmnDecisionTable;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import java.io.ByteArrayInputStream;
import java.io.StringWriter;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * Bidirectional transformer between JSON decision rules and DMN XML format
 */
@Component
@RequiredArgsConstructor
@SuppressWarnings("PMD")
public class DmnJsonTransformer {

    private final ObjectMapper objectMapper;
    private static final String DMN_NAMESPACE = "https://www.omg.org/spec/DMN/20191111/MODEL/";
    private static final String FEEL_NAMESPACE = "https://www.omg.org/spec/DMN/20191111/FEEL/";

    public DmnDecisionTable jsonToDmnTable(String jsonString) throws Exception {
        JsonNode jsonNode = objectMapper.readTree(jsonString);

        DmnDecisionTable table = new DmnDecisionTable();
        table.setId(jsonNode.get("decisionId").asText());
        table.setHitPolicy(jsonNode.get("hitPolicy").asText("FIRST"));
        if (jsonNode.has("aggregation")) {
            table.setAggregation(jsonNode.get("aggregation").asText());
        }
        if (jsonNode.has("decisionName")) {
            table.setName(jsonNode.get("decisionName").asText());
        } else {
            // Default name if not provided
            table.setName("Decision Table");
        }

        Set<String> allInputs = new HashSet<>();
        Set<String> allOutputs = new HashSet<>();

        // First pass: collect all input and output column names
        for (JsonNode ruleNode : jsonNode.get("rules")) {
            if (ruleNode.has("inputs")) {
                JsonNode inputs = ruleNode.get("inputs");
                inputs.fieldNames().forEachRemaining(allInputs::add);
            }
            if (ruleNode.has("outputs")) {
                JsonNode outputs = ruleNode.get("outputs");
                outputs.fieldNames().forEachRemaining(allOutputs::add);
            }
        }

        for (String columnName : allInputs) {
            // Default type is string, can be customized later
            table.getInputColumns().put(columnName, "string");
        }

        for (String columnName : allOutputs) {
            // Default type is string, can be customized later
            table.getOutputColumns().put(columnName, "string");
        }

        // Second pass: create decision rules
        for (JsonNode ruleNode : jsonNode.get("rules")) {
            DecisionRule rule = new DecisionRule();

            if (ruleNode.has("inputs")) {
                JsonNode inputs = ruleNode.get("inputs");
                inputs.fieldNames().forEachRemaining(fieldName -> {
                    JsonNode value = inputs.get(fieldName);
                    rule.getInputs().put(fieldName, convertJsonValue(value));
                });
            }

            if (ruleNode.has("outputs")) {
                JsonNode outputs = ruleNode.get("outputs");
                outputs.fieldNames().forEachRemaining(fieldName -> {
                    JsonNode value = outputs.get(fieldName);
                    rule.getOutputs().put(fieldName, convertJsonValue(value));
                });
            }

            table.getRules().add(rule);
        }

        return table;
    }

    /**
     * Transform DMN decision table to JSON string
     */
    public String dmnTableToJson(DmnDecisionTable table) throws Exception {
        final ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("decisionId", table.getId());
        objectNode.put("decisionName", table.getName());
        objectNode.put("hitPolicy", table.getHitPolicy());
        if (table.getAggregation() != null && !table.getAggregation().isEmpty()) {
            objectNode.put("aggregation", table.getAggregation());
        }
        ArrayNode rulesArray = objectMapper.createArrayNode();

        for (DecisionRule rule : table.getRules()) {
            ObjectNode ruleNode = objectMapper.createObjectNode();

            // Add inputs
            ObjectNode inputsNode = objectMapper.createObjectNode();
            for (Map.Entry<String, String> inputColumn : table.getInputColumns().entrySet()) {
                Object value = rule.getInputs().get(inputColumn.getKey());
                inputsNode.set(inputColumn.getKey(), objectMapper.valueToTree(value));
            }
            ruleNode.set("inputs", inputsNode);

            // Add outputs
            ObjectNode outputsNode = objectMapper.createObjectNode();
            for (Map.Entry<String, String> outputColumn :
                    table.getOutputColumns().entrySet()) {
                Object value = rule.getOutputs().get(outputColumn.getKey());
                outputsNode.set(outputColumn.getKey(), objectMapper.valueToTree(value));
            }
            ruleNode.set("outputs", outputsNode);

            rulesArray.add(ruleNode);
        }

        // Add rules array to the main object
        objectNode.set("rules", rulesArray);

        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(objectNode);
    }

    /**
     * Transform JSON to DMN XML format
     */
    public String jsonToDmnXml(String jsonString) throws Exception {
        DmnDecisionTable table = jsonToDmnTable(jsonString);
        return dmnTableToXml(table);
    }

    /**
     * Transform DMN decision table to XML string
     */
    @SneakyThrows
    public String dmnTableToXml(DmnDecisionTable table) {
        DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
        Document doc = docBuilder.newDocument();

        // Root element
        Element definitions = doc.createElement("definitions");
        definitions.setAttribute("xmlns", DMN_NAMESPACE);
        definitions.setAttribute("xmlns:feel", FEEL_NAMESPACE);
        definitions.setAttribute("id", "definitions_" + table.getId());
        definitions.setAttribute("name", "Decision Model");
        definitions.setAttribute("namespace", "http://example.org/dmn");
        doc.appendChild(definitions);

        // Decision element
        Element decision = doc.createElement("decision");
        decision.setAttribute("id", table.getId());
        decision.setAttribute("name", table.getName());
        definitions.appendChild(decision);

        // Decision table element
        Element decisionTable = doc.createElement("decisionTable");
        decisionTable.setAttribute("id", table.getId() + "_table");
        decisionTable.setAttribute("hitPolicy", table.getHitPolicy());

        // Add aggregation attribute for COLLECT hit policy if specified
        if ("COLLECT".equals(table.getHitPolicy())
                && table.getAggregation() != null
                && !table.getAggregation().isEmpty()) {
            decisionTable.setAttribute("aggregation", table.getAggregation());
        }

        decision.appendChild(decisionTable);

        // Input clauses
        for (Map.Entry<String, String> inputColumn : table.getInputColumns().entrySet()) {
            Element input = doc.createElement("input");
            input.setAttribute("id", "input_" + inputColumn.getKey());

            Element inputExpression = doc.createElement("inputExpression");
            inputExpression.setAttribute("typeRef", inputColumn.getValue() == null ? "string" : inputColumn.getValue());

            Element text = doc.createElement("text");
            text.setTextContent(inputColumn.getKey());
            inputExpression.appendChild(text);

            input.appendChild(inputExpression);
            decisionTable.appendChild(input);
        }

        // Output clauses
        for (Map.Entry<String, String> outputColumn : table.getOutputColumns().entrySet()) {
            Element output = doc.createElement("output");
            output.setAttribute("id", "output_" + outputColumn.getKey());
            output.setAttribute("name", outputColumn.getKey());
            output.setAttribute("typeRef", outputColumn.getValue() == null ? "string" : outputColumn.getValue());
            decisionTable.appendChild(output);
        }

        // Rules
        int ruleIndex = 1;
        for (DecisionRule rule : table.getRules()) {
            Element ruleElement = doc.createElement("rule");
            ruleElement.setAttribute("id", "rule_" + ruleIndex);

            // Input entries
            for (Map.Entry<String, String> inputColumn : table.getInputColumns().entrySet()) {
                Element inputEntry = doc.createElement("inputEntry");
                Element text = doc.createElement("text");
                Object value = rule.getInputs().get(inputColumn.getKey());
                text.setTextContent(formatValueForDmn(value));
                inputEntry.appendChild(text);
                ruleElement.appendChild(inputEntry);
            }

            // Output entries
            for (Map.Entry<String, String> outputColumn :
                    table.getOutputColumns().entrySet()) {
                Element outputEntry = doc.createElement("outputEntry");
                Element text = doc.createElement("text");
                Object value = rule.getOutputs().get(outputColumn.getKey());
                text.setTextContent(formatValueForDmn(value));
                outputEntry.appendChild(text);
                ruleElement.appendChild(outputEntry);
            }

            decisionTable.appendChild(ruleElement);
            ruleIndex++;
        }

        // Transform to string
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes");
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));

        return writer.toString();
    }

    /**
     * Transform DMN XML to JSON
     */
    public String dmnXmlToJson(String xmlString) throws Exception {
        DmnDecisionTable table = getDmnDecisionTable(xmlString);
        return dmnTableToJson(table);
    }

    @SneakyThrows
    public DmnDecisionTable getDmnDecisionTable(final String xmlString) {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new ByteArrayInputStream(xmlString.getBytes()));

        return parseDmnXml(doc);
    }

    /**
     * Parse DMN XML document to decision table object
     * Enhanced to handle Camunda DMN format and various attribute patterns
     */
    public DmnDecisionTable parseDmnXml(Document doc) {
        DmnDecisionTable table = new DmnDecisionTable();

        // Find decision table element
        final NodeList decision = doc.getElementsByTagName("decision");
        final NodeList decisionTables = doc.getElementsByTagName("decisionTable");
        if (decisionTables.getLength() == 0) {
            throw new IllegalArgumentException("No decision table found in DMN XML");
        }

        Element decisionTableElement = (Element) decisionTables.item(0);
        Element decisionElement = (Element) decision.item(0);
        table.setId(decisionElement.getAttribute("id"));
        table.setName(decisionElement.getAttribute("name"));
        String hitPolicy = decisionTableElement.getAttribute("hitPolicy");
        table.setHitPolicy(hitPolicy.isEmpty() ? "FIRST" : hitPolicy);

        // Get aggregation attribute if present (for COLLECT hit policy)
        String aggregation = decisionTableElement.getAttribute("aggregation");
        if (!aggregation.isEmpty()) {
            table.setAggregation(aggregation);
        }

        // Parse input columns - handle both 'label' and 'text' content
        NodeList inputs = decisionTableElement.getElementsByTagName("input");
        for (int i = 0; i < inputs.getLength(); i++) {
            Element input = (Element) inputs.item(i);
            String inputName = input.getAttribute("label");

            if (inputName.isEmpty()) {
                // Try to get from inputExpression text
                NodeList inputExpressions = input.getElementsByTagName("inputExpression");
                if (inputExpressions.getLength() > 0) {
                    Element inputExpression = (Element) inputExpressions.item(0);
                    NodeList textNodes = inputExpression.getElementsByTagName("text");
                    if (textNodes.getLength() > 0) {
                        inputName = textNodes.item(0).getTextContent().trim();
                    }
                }
            }

            if (inputName.isEmpty()) {
                inputName = input.getAttribute("id");
            }

            if (!inputName.isEmpty()) {
                table.getInputColumns().put(inputName.trim(), inputName.trim());
            }
        }

        // Parse output columns - prioritize 'name' attribute, fallback to 'label'
        NodeList outputs = decisionTableElement.getElementsByTagName("output");
        for (int i = 0; i < outputs.getLength(); i++) {
            Element output = (Element) outputs.item(i);
            String outputName = output.getAttribute("name");

            if (outputName.isEmpty()) {
                outputName = output.getAttribute("label");
            }

            if (outputName.isEmpty()) {
                outputName = output.getAttribute("id");
            }

            if (!outputName.isEmpty()) {
                table.getOutputColumns().put(outputName, outputName.trim());
            }
        }

        // Parse rules
        NodeList rules = decisionTableElement.getElementsByTagName("rule");
        for (int i = 0; i < rules.getLength(); i++) {
            Element ruleElement = (Element) rules.item(i);
            DecisionRule rule = new DecisionRule();

            // Parse input entries
            NodeList inputEntries = ruleElement.getElementsByTagName("inputEntry");
            for (int j = 0;
                    j < inputEntries.getLength() && j < table.getInputColumns().size();
                    j++) {
                Element inputEntry = (Element) inputEntries.item(j);
                NodeList textNodes = inputEntry.getElementsByTagName("text");
                String value = "";
                if (textNodes.getLength() > 0) {
                    value = textNodes.item(0).getTextContent().trim();
                }
                String columnName = getByIndex(table.getInputColumns(), j);
                rule.getInputs().put(columnName, parseValueFromDmn(value));
            }

            // Parse output entries
            NodeList outputEntries = ruleElement.getElementsByTagName("outputEntry");
            for (int j = 0;
                    j < outputEntries.getLength()
                            && j < table.getOutputColumns().size();
                    j++) {
                Element outputEntry = (Element) outputEntries.item(j);
                NodeList textNodes = outputEntry.getElementsByTagName("text");
                String value = "";
                if (textNodes.getLength() > 0) {
                    value = textNodes.item(0).getTextContent().trim();
                }
                String columnName = getByIndex(table.getOutputColumns(), j);
                rule.getOutputs().put(columnName, parseValueFromDmn(value));
            }

            table.getRules().add(rule);
        }

        return table;
    }

    /**
     * Convert JSON value to appropriate Java object
     */
    private Object convertJsonValue(JsonNode value) {
        if (value.isNull()) {
            return null;
        } else if (value.isBoolean()) {
            return value.asBoolean();
        } else if (value.isNumber()) {
            if (value.isInt()) {
                return value.asInt();
            } else {
                return value.asDouble();
            }
        } else {
            return value.asText();
        }
    }

    /**
     * Format value for DMN XML
     */
    private String formatValueForDmn(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof String) {
            String str = (String) value;
            if (str.startsWith("'") && str.endsWith("'") || str.startsWith("\"") && str.endsWith("\"")) {
                return str; // Already quoted
            }
            return "\"" + str + "\"";
        } else if (value instanceof Boolean) {
            return value.toString();
        } else if (value instanceof Number) {
            return value.toString();
        } else {
            return "\"" + value + "\"";
        }
    }

    /**
     * Parse value from DMN XML
     */
    private Object parseValueFromDmn(String value) {
        if (value == null || value.equals("-") || value.trim().isEmpty()) {
            return null;
        }

        value = value.trim();

        // Remove quotes if present
        if ((value.startsWith("\"") && value.endsWith("\"")) || (value.startsWith("'") && value.endsWith("'"))) {
            return value.substring(1, value.length() - 1);
        }

        // Try to parse as boolean
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }

        // Try to parse as number
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            // Not a number, return as string
        }

        return value;
    }

    public String getByIndex(LinkedHashMap<String, String> map, int index) {
        return map.keySet().toArray()[index].toString();
    }
}
