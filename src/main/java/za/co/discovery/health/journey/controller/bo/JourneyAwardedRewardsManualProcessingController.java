package za.co.discovery.health.journey.controller.bo;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyRewardAwardingFacade;

@RestController
@RequestMapping("/api/bo/process/awarded")
@RequiredArgsConstructor
public class JourneyAwardedRewardsManualProcessingController {
    private final JourneyRewardAwardingFacade journeyRewardAwardingFacade;

    @GetMapping("/program")
    public ResponseEntity<Void> processAwardedProgramRewards() {
        journeyRewardAwardingFacade.processAwardedProgramRewards();
        return ResponseEntity.ok().build();
    }

    @GetMapping("/milestone")
    public ResponseEntity<Void> processAwardedMilestoneRewards() {
        journeyRewardAwardingFacade.processAwardedProgramRewards();
        return ResponseEntity.ok().build();
    }
}
