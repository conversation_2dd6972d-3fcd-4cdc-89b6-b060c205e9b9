package za.co.discovery.health.journey.messaging.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JourneyEnrollmentEvent {
    private Long entityId;
    private Long programId;
    private Long categoryId;
    private Long enrollmentId;
    private String categoryType;
    private String externalReference;
    private LocalDateTime enrollmentDate;
}
