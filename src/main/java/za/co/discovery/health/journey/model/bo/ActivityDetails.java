package za.co.discovery.health.journey.model.bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import za.co.discovery.health.journey.model.bo.dmn.ActivityRecommendationDmnRuleDto;

@Data
@Builder
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class ActivityDetails {
    private String programName;
    private Long programId;
    private String activityName;
    private String surveyName;
    private String rootCategoryName;
    private String mnemonic;
    private ActivityRecommendationDmnRuleDto.ActivityType activityType;
    private ActivityPlacement placement;
    private Long iteration;
}
