package za.co.discovery.health.journey.service.notification.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.entity.api.EntityApi;
import za.co.discovery.health.journey.config.KafkaTopics;
import za.co.discovery.health.journey.database.databaseMapping.UserJourneyNotification;
import za.co.discovery.health.journey.messaging.model.NotificationTemplate;
import za.co.discovery.health.journey.service.journey.CoachingJourneyService;
import za.co.discovery.health.journey.service.notification.NotificationProcessingService;
import za.co.discovery.health.journey.service.notification.UserJourneyNotificationService;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("PMD.AvoidCatchingGenericException")
public class NotificationProcessingServiceImpl implements NotificationProcessingService {

    private final UserJourneyNotificationService notificationService;
    private final CoachingJourneyService coachingJourneyService;
    private final EntityApi entityApi;
    private final KafkaTemplate<String, NotificationTemplate> notificationTemplateKafkaTemplate;

    @Value("${notification.processing.chunk-size:100}")
    private int chunkSize;

    @Override
    @Transactional
    public void processScheduledNotifications() {
        final LocalDateTime currentTime = LocalDateTime.now();

        final List<UserJourneyNotification> scheduledNotifications =
                notificationService.findNotificationToSend(currentTime);

        log.info("Found {} scheduled notifications to process", scheduledNotifications.size());

        scheduledNotifications.forEach(notification -> processNotification(notification, currentTime));
    }

    private void processNotification(final UserJourneyNotification notification, final LocalDateTime currentTime) {
        try {
            log.info(
                    "Processing notification for alliance: {}, customer: {}, branch: {}, journey: {}",
                    notification.getAlliance(),
                    notification.getCustomer(),
                    notification.getBranch(),
                    notification.getJourneyCategory().getName());

            entityApi
                    .streamMemberEntityNos(
                            notification.getAlliance() == null ? null : Integer.valueOf(notification.getAlliance()),
                            notification.getCustomer() == null ? null : Long.valueOf(notification.getCustomer()),
                            notification.getBranch())
                    .buffer(chunkSize)
                    .subscribe(
                            batch -> processBatch(batch, notification),
                            error -> {
                                log.error(
                                        "Error processing notification batch for alliance: {}, customer: {}, branch: {}",
                                        notification.getAlliance(),
                                        notification.getCustomer(),
                                        notification.getBranch(),
                                        error);
                            },
                            () -> {
                                log.info(
                                        "Completed processing notification for alliance: {}, customer: {}, branch: {}",
                                        notification.getAlliance(),
                                        notification.getCustomer(),
                                        notification.getBranch());
                                notificationService.updateSentNotification(notification, currentTime);
                            });

        } catch (Exception e) {
            log.error("Error processing notification with id: {}", notification.getUserJourneyNotificationId(), e);
        }
    }

    private void processBatch(final List<Long> entityIds, final UserJourneyNotification notification) {
        entityIds.forEach(entityId -> processEntityNotification(entityId, notification));
    }

    private void processEntityNotification(final Long entityId, final UserJourneyNotification notification) {
        try {
            final NotificationTemplate template = coachingJourneyService.createEnrollmentPeriodOpenedNotification(
                    entityId, notification.getJourneyCategory(), notification.getEnrollmentEndTime());

            notificationTemplateKafkaTemplate.send(KafkaTopics.NOTIFICATION_MANAGER_SEND, template);

            log.debug("Successfully created notification for entityId: {}", entityId);

        } catch (Exception e) {
            log.error(
                    "Error creating notification for entityId: {} in journey: {}",
                    entityId,
                    notification.getJourneyCategory().getName(),
                    e);
        }
    }
}
