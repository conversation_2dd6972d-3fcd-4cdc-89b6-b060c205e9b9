package za.co.discovery.health.journey.rule.conversion;

import com.za.disocvery.health.configflag.domain.ConfigurationFlagDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.remote.config.ConfigurationFlagService;

import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class ConfigFlagJexlRunner {

    private final JexlRunner runner;

    private final ConfigurationFlagService configurationFlagService;

    public boolean run(final Long entityId, final String expression) {
        final Map<String, Object> configFlags = configurationFlagService.getConfigFlags(entityId).stream()
                .filter(flag -> flag.getValue() != null && flag.getKey() != null)
                .collect(Collectors.toMap(ConfigurationFlagDto::getKey, ConfigurationFlagDto::getValue));

        return runner.run(expression, configFlags);
    }

    public boolean run(final Long entityId, final String expression, final Map<String, Object> additionalContext) {
        final Map<String, Object> configFlags = configurationFlagService.getConfigFlags(entityId).stream()
                .filter(flag -> flag.getValue() != null && flag.getKey() != null)
                .collect(Collectors.toMap(ConfigurationFlagDto::getKey, ConfigurationFlagDto::getValue));

        configFlags.putAll(additionalContext);
        return runner.run(expression, configFlags);
    }
}
