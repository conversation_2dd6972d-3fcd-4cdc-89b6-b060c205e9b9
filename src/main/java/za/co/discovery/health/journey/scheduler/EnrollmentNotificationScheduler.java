package za.co.discovery.health.journey.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import za.co.discovery.health.journey.rule.dmn.service.CategoryConfigurationUpdateService;
import za.co.discovery.health.journey.rule.dmn.service.JourneyChangeDetectionService;
import za.co.discovery.health.journey.service.notification.NotificationProcessingService;

@Slf4j
@Component
@RequiredArgsConstructor
public class EnrollmentNotificationScheduler {

    private final NotificationProcessingService notificationProcessingService;
    private final CategoryConfigurationUpdateService configUpdateService;
    private final JourneyChangeDetectionService journeyChangeDetectionService;

    @Scheduled(cron = "${scheduler.journey-reward.cron:0 0,30 * * * *}")
    @SchedulerLock(name = "hs-personal-journey:enrollment-open-notifications-scheduling")
    public void scheduleNotifications() {
        log.info("Started Enrollment Notification scheduling ...");
        final StopWatch stopWatch = new StopWatch("Enrollment Notification Scheduling");
        stopWatch.start();

        journeyChangeDetectionService.processJourneyDmnUpdates();

        stopWatch.stop();
        log.info("Enrollment Notification scheduling completed in {} ms", stopWatch.getTotalTimeMillis());
    }

    @Scheduled(cron = "${scheduler.journey-reward.cron:0 5,35 * * * *}")
    @SchedulerLock(name = "hs-personal-journey:journey-category-update-handler")
    public void handleConfigurationUpdates() {
        log.info("Started Configuration updates processing ...");
        final StopWatch stopWatch = new StopWatch("Journey configuration update handler");
        stopWatch.start();

        configUpdateService.processAllCategoryConfigurationUpdates();

        stopWatch.stop();
        log.info("Configuration update handling completed in {} ms", stopWatch.getTotalTimeMillis());
    }

    @Scheduled(cron = "${scheduler.journey-reward.cron:0 10,40 * * * *}")
    @SchedulerLock(name = "hs-personal-journey:send-enrollment-notifications")
    public void sendNotifications() {
        log.info("Started Enrollment Notification sending ...");
        final StopWatch stopWatch = new StopWatch("Enrollment Notification Sending");
        stopWatch.start();

        notificationProcessingService.processScheduledNotifications();

        stopWatch.stop();
        log.info("Notification sending completed in {} ms", stopWatch.getTotalTimeMillis());
    }
}
