package za.co.discovery.health.journey.resolver.rule.model.output;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import za.co.discovery.health.journey.util.DMNConstants;
import za.co.discovery.health.journey.util.DMNUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Builder(access = AccessLevel.PRIVATE)
public class JourneyActivationDMNRuleOutput {

    private final List<DMNOutput> outputs;

    /**
     * Creates an {@link JourneyActivationDMNRuleOutput} from a list of result maps.
     * Each map is expected to contain keys defined in {@link DMNConstants}.
     *
     * @param result List of maps containing DMN rule output data
     * @return An instance of {@link JourneyActivationDMNRuleOutput}, or an empty one if invalid input
     */
    public static JourneyActivationDMNRuleOutput of(final List<Map<String, Object>> result) {
        // No match found or timePeriod not configured
        if (result == null || result.isEmpty()) {
            return JourneyActivationDMNRuleOutput.builder()
                    .outputs(Collections.emptyList())
                    .build();
        }

        final List<DMNOutput> outputs = result.stream()
                .map(JourneyActivationDMNRuleOutput::convertToDMNOutput)
                .filter(JourneyActivationDMNRuleOutput::hasValidCategory)
                .collect(Collectors.toList());

        return JourneyActivationDMNRuleOutput.builder().outputs(outputs).build();
    }

    /**
     * Converts a single map of DMN data to a {@link DMNOutput} object.
     *
     * @param categoryMap Map containing activity data
     * @return {@link DMNOutput} built from the map; never null (fields may be null)
     */
    private static DMNOutput convertToDMNOutput(final Map<String, Object> categoryMap) {
        // Safely convert map values to the correct data types
        final String category = DMNUtils.toStringOrNull(categoryMap.get(DMNConstants.JOURNEY_CATEGORY));

        return DMNOutput.builder().category(category).build();
    }

    /**
     * Checks if the DMNOutput has a non-null category.
     * Logs an error if the mnemonic is null.
     *
     * @param output DMNOutput to check
     * @return true if mnemonic is not null, false otherwise
     */
    private static boolean hasValidCategory(final DMNOutput output) {
        if (Objects.isNull(output.getCategory())) {
            log.error("Category is null. Skipping DMNOutput: {}", output);
            return false;
        }
        return true;
    }

    @Data
    @Builder
    public static class DMNOutput {
        private final String category;
    }
}
