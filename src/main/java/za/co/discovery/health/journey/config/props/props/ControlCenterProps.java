package za.co.discovery.health.journey.config.props.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;

@Data
@Configuration
@ConfigurationProperties(prefix = "control-center")
public class ControlCenterProps {
    private Set<String> groups = new HashSet<>();
}
