package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
import za.co.discovery.health.journey.service.journey.processor.JourneyEnrollmentProcessor;
import za.co.discovery.health.journey.service.lock.LockingTaskExecutorFacade;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class JourneyProcessor {
    private final JourneyService journeyService;
    private final JourneyEnrollmentProcessor enrollmentProcessor;
    private final JourneyEnrollmentService journeyEnrollmentService;
    private final LockingTaskExecutorFacade lockingTaskExecutorFacade;
    private static final String JOURNEY_PROCESSOR = "PROCESS_JOURNEY_ENROLLMENTS_FOR_ENTITY_ID_";

    public List<UserCategoryEnrollmentDto> getUserEnrollments(final long entityId, final LocalDateTime time) {
        process(entityId, time);

        return journeyEnrollmentService.getDetailedEnrollments(entityId, time);
    }

    public Boolean process(final long entityId, final LocalDateTime time) {
        return lockingTaskExecutorFacade.executeWithLockWait(
                () -> {
                    final var recommendations = journeyService.getRecommendedCategories(entityId);

                    recommendations.forEach(
                            recommendation -> enrollmentProcessor.process(entityId, recommendation, time));
                    return true;
                },
                JOURNEY_PROCESSOR + entityId);
    }
}
