package za.co.discovery.health.journey.event;

import lombok.Getter;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.model.enums.ActivityStatus;

import java.time.LocalDateTime;

@Getter
public class ActivityCompletedEvent extends JourneyEvent {
    private final JourneyEnrollment enrollment;
    private final JourneyEnrollmentMilestone milestone;
    private final JourneyEnrollmentMilestoneActivity activity;
    private final ActivityStatus status;
    private final LocalDateTime completedAt;

    public ActivityCompletedEvent(
            final JourneyEnrollmentMilestoneActivity activity,
            final ActivityStatus status,
            final LocalDateTime completedAt) {
        super(activity.getJourneyEnrollmentMilestone().getJourneyEnrollment().getEntityId(), LocalDateTime.now());
        final JourneyEnrollmentMilestone milestone = activity.getJourneyEnrollmentMilestone();
        this.activity = activity;
        this.enrollment = milestone.getJourneyEnrollment();
        this.milestone = milestone;
        this.status = status;
        this.completedAt = completedAt;
    }
}
