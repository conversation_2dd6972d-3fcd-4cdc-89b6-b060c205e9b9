package za.co.discovery.health.journey.service.journey.reward;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.projection.RewardProjection;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramMilestoneRewardCustomizationRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyProgramRewardCustomizationRepository;
import za.co.discovery.health.journey.util.model.Audience;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ProgramRewardService {
    private final ExtendedJourneyProgramRewardCustomizationRepository programRewardRepo;
    private final ExtendedJourneyProgramMilestoneRewardCustomizationRepository milestoneRewardRepo;

    public List<RewardProjection> getProgramRewards(
            final Audience audience, final long programId, final LocalDateTime time) {
        return programRewardRepo.findRewardsByCriteria(
                programId, audience.getAllianceNo(), audience.getGroupNo(), audience.getBranchNo(), time);
    }

    public List<RewardProjection> getMilestoneRewards(
            final Audience audience, final long iteration, final long programId, final LocalDateTime time) {
        return milestoneRewardRepo.findRewardsByCriteria(
                programId, iteration, audience.getAllianceNo(), audience.getGroupNo(), audience.getBranchNo(), time);
    }
}
