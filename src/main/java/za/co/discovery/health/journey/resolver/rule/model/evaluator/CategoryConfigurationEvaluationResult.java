package za.co.discovery.health.journey.resolver.rule.model.evaluator;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import za.co.discovery.health.journey.resolver.rule.model.output.CategoryConfigurationDMNRuleOutput;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class CategoryConfigurationEvaluationResult extends RuleEvaluationResult {
    private CategoryConfigurationResult result;

    public static CategoryConfigurationEvaluationResult of(
            final CategoryConfigurationDMNRuleOutput results, final boolean success) {

        if (success) {
            return CategoryConfigurationEvaluationResult.builder()
                    .result(CategoryConfigurationResult.of(results.getOutput()))
                    .success(true)
                    .build();
        } else {
            return CategoryConfigurationEvaluationResult.builder()
                    .result(null)
                    .success(false)
                    .build();
        }
    }

    @Data
    @SuperBuilder(toBuilder = true)
    public static class CategoryConfigurationResult {
        private LocalDateTime enrollmentStartTime;
        private LocalDateTime enrollmentEndTime;
        private LocalDateTime journeyStartTime;
        private String enrollmentPrecondition;
        private String activityPrecondition;
        private Long maxParticipants;
        private Duration monitoringPeriodDuration;

        public static CategoryConfigurationResult of(final CategoryConfigurationDMNRuleOutput.DMNOutput output) {
            if (output == null) {
                return CategoryConfigurationResult.builder().build();
            }
            return CategoryConfigurationResult.builder()
                    .enrollmentStartTime(parseDateTime(output.getEnrollmentStartTime()))
                    .enrollmentEndTime(parseDateTime(output.getEnrollmentEndTime()))
                    .journeyStartTime(parseDateTime(output.getJourneyStartTime()))
                    .enrollmentPrecondition(output.getEnrollmentPrecondition())
                    .activityPrecondition(output.getActivityPrecondition())
                    .maxParticipants(output.getMaxParticipants())
                    .monitoringPeriodDuration(
                            output.getMonitoringPeriodDuration() == null
                                    ? Duration.ZERO
                                    : Duration.ofDays(output.getMonitoringPeriodDuration()))
                    .build();
        }

        private static LocalDateTime parseDateTime(final String dateTimeStr) {
            if (StringUtils.isEmpty(dateTimeStr)) {
                return null;
            }

            try {
                // Handle format with time: yyyy-MM-dd'T'HH:mm:ss
                if (dateTimeStr.contains("T")) {
                    return LocalDateTime.parse(dateTimeStr);
                }
                // Handle date-only format: yyyy-MM-dd (converts to start of day)
                else {
                    return LocalDate.parse(dateTimeStr).atStartOfDay();
                }
            } catch (DateTimeParseException e) {
                // If standard formats fail, try with formatter
                try {
                    final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    return LocalDateTime.parse(dateTimeStr, formatter);
                } catch (DateTimeParseException ex) {
                    // Log the error or handle as needed
                    return null;
                }
            }
        }
    }
}
