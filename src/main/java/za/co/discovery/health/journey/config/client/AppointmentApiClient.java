package za.co.discovery.health.journey.config.client;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import za.co.discovery.health.group.coaching.api.AppointmentControllerApi;
import za.co.discovery.health.group.coaching.domain.AppointmentResponseDto;

import java.util.Set;

@Service
@RequiredArgsConstructor
public class AppointmentApiClient {

    private final AppointmentControllerApi appointmentControllerApi;

    public Flux<AppointmentResponseDto> getAppointmentDetails(final Set<Long> appointmentIds) {
        return appointmentControllerApi.getAppointmentByIds(appointmentIds, false);
    }
}
