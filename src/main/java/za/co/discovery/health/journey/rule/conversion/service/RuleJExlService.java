package za.co.discovery.health.journey.rule.conversion.service;

import za.co.discovery.health.journey.rule.conversion.model.JourneyJExlModel;

import java.util.List;

public interface RuleJExlService {

    List<JourneyJExlModel> getAll();

    JourneyJExlModel getByName(String name);

    JourneyJExlModel add(String name, String rule);

    JourneyJExlModel update(String name, String rule);

    void delete(String name);
}
