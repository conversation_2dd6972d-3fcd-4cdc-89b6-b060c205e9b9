package za.co.discovery.health.journey.config.cache;

import lombok.RequiredArgsConstructor;
import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.cfg.ProcessEnginePlugin;
import org.camunda.bpm.engine.impl.db.sql.DbSqlSessionFactory;
import org.camunda.bpm.spring.boot.starter.configuration.Ordering;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Order(Ordering.DEFAULT_ORDER + 2)
public class H2SqlConfigurationProcessEnginePlugin implements ProcessEnginePlugin {

    @Override
    public void preInit(final ProcessEngineConfigurationImpl processEngineConfiguration) {
        DbSqlSessionFactory.databaseSpecificTrueConstant.put("h2", "true");
        DbSqlSessionFactory.databaseSpecificFalseConstant.put("h2", "false");
        DbSqlSessionFactory.databaseSpecificBitAnd2.put("h2", ",CAST(");
        DbSqlSessionFactory.databaseSpecificBitAnd3.put("h2", " AS BIGINT))");
    }

    @Override
    public void postInit(final ProcessEngineConfigurationImpl processEngineConfiguration) {
        // in this case only preInit is necessary
    }

    @Override
    public void postProcessEngineBuild(final ProcessEngine processEngine) {
        // in this case only preInit is necessary
    }
}
