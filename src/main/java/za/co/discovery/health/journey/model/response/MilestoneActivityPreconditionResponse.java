package za.co.discovery.health.journey.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import za.co.discovery.health.journey.model.precondition.PreconditionEvaluationResult;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MilestoneActivityPreconditionResponse {
    private long journeyCategoryId;
    private String journeyCategoryName;
    private Long milestoneActivityId;
    private List<PreconditionEvaluationResult> preconditionEvaluationResultList;
}
