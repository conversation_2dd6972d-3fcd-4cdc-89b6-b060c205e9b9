package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyEnrollmentMilestoneActivityRepository
        extends JourneyEnrollmentMilestoneActivityRepository {

    @Query(
            value = "from JourneyEnrollmentMilestoneActivity jema where "
                    + "jema.journeyEnrollmentMilestone.journeyEnrollment.journeyProgram.journeyProgramId = :programId "
                    + "and jema.journeyEnrollmentMilestone.journeyEnrollment.entityId = :entityId "
                    + "and jema.journeyEnrollmentMilestone.milestoneIteration = :iteration "
                    + "and jema.activityMnemonicId = :mnemonic ")
    Optional<JourneyEnrollmentMilestoneActivity> getActivityByIteration(
            @Param("programId") Long programId,
            @Param("entityId") Long entityId,
            @Param("iteration") Long iteration,
            @Param("mnemonic") String activityMnemonicId);

    @Query("SELECT COUNT(jema) FROM JourneyEnrollmentMilestoneActivity jema "
            + "WHERE jema.journeyEnrollmentMilestone.journeyEnrollment.journeyProgram.journeyProgramId = :programId "
            + "AND jema.journeyEnrollmentMilestone.journeyEnrollment.entityId = :entityId "
            + "AND jema.activityType = :activityType "
            + "AND jema.activityStatus = :status "
            + "AND jema.completedAt between :startTimestamp and :endTimestamp")
    int countCompletedActivitiesByMnemonic(
            Long programId,
            Long entityId,
            String activityType,
            String status,
            LocalDateTime startTimestamp,
            LocalDateTime endTimestamp);

    @Query("SELECT jema FROM JourneyEnrollmentMilestoneActivity jema "
            + "WHERE jema.journeyEnrollmentMilestone.journeyEnrollment.entityId = :entityId "
            + "AND jema.activityMnemonicId = :activityMnemonicId "
            + "AND jema.activityStatus in (:status) "
            + "ORDER BY jema.completedAt DESC ")
    List<JourneyEnrollmentMilestoneActivity> findAllActivitiesByMnemonic(
            Long entityId, String activityMnemonicId, List<String> status);
}
