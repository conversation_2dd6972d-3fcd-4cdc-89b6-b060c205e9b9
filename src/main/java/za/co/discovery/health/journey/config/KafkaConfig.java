package za.co.discovery.health.journey.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;
import za.co.discovery.health.journey.messaging.model.ActivityCompletionRequestMessage;
import za.co.discovery.health.journey.messaging.model.JourneyEnrollmentEvent;
import za.co.discovery.health.journey.messaging.model.NotificationTemplate;
import za.co.discovery.health.journey.messaging.model.payoff.PayoffEvent;

@EnableKafka
@Configuration
@Profile("kafka")
@RequiredArgsConstructor
public class KafkaConfig {
    private final ObjectMapper objectMapper;

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> defaultContainerFactory(
            final KafkaProperties kafkaProperties) {
        return createStringListenerContainerFactory(kafkaProperties);
    }

    private ConcurrentKafkaListenerContainerFactory<String, String> createStringListenerContainerFactory(
            final KafkaProperties kafkaProperties) {
        final var consumerFactory = new DefaultKafkaConsumerFactory<>(
                kafkaProperties.buildConsumerProperties(), new StringDeserializer(), new StringDeserializer());
        final var factory = new ConcurrentKafkaListenerContainerFactory<String, String>();
        factory.setConsumerFactory(consumerFactory);
        return factory;
    }

    @Bean
    public KafkaTemplate<String, JourneyEnrollmentEvent> journeyEnrollmentEventKafkaTemplate(
            final ProducerFactory<String, JourneyEnrollmentEvent> producerFactory) {
        return new KafkaTemplate<>(producerFactory);
    }

    @Bean
    public ProducerFactory<String, JourneyEnrollmentEvent> journeyEnrollmentEventProducerFactory(
            final KafkaProperties kafkaProperties) {
        try (JsonSerializer<JourneyEnrollmentEvent> serializer = new JsonSerializer<>(objectMapper)) {
            return new DefaultKafkaProducerFactory<>(
                    kafkaProperties.buildProducerProperties(), new StringSerializer(), serializer);
        }
    }

    @Bean
    public KafkaTemplate<String, ActivityCompletionRequestMessage> activityCompletionRequestMessageKafkaTemplate(
            final ProducerFactory<String, ActivityCompletionRequestMessage> producerFactory) {
        return new KafkaTemplate<>(producerFactory);
    }

    @Bean
    public ProducerFactory<String, ActivityCompletionRequestMessage> activityCompletionRequestMessageProducerFactory(
            final KafkaProperties kafkaProperties) {
        try (JsonSerializer<ActivityCompletionRequestMessage> serializer = new JsonSerializer<>(objectMapper)) {
            return new DefaultKafkaProducerFactory<>(
                    kafkaProperties.buildProducerProperties(), new StringSerializer(), serializer);
        }
    }

    @Bean
    public KafkaTemplate<String, NotificationTemplate> notificationTemplateKafkaTemplate(
            final ProducerFactory<String, NotificationTemplate> producerFactory) {
        return new KafkaTemplate<>(producerFactory);
    }

    @Bean
    public ProducerFactory<String, NotificationTemplate> notificationTemplateProducerFactory(
            final KafkaProperties kafkaProperties) {
        try (JsonSerializer<NotificationTemplate> serializer = new JsonSerializer<>(objectMapper)) {
            return new DefaultKafkaProducerFactory<>(
                    kafkaProperties.buildProducerProperties(), new StringSerializer(), serializer);
        }
    }

    @Bean
    public ProducerFactory<String, PayoffEvent> rewardPayoffRequestProducerFactory(
            final KafkaProperties kafkaProperties) {
        try (JsonSerializer<PayoffEvent> serializer = new JsonSerializer<>(objectMapper)) {
            return new DefaultKafkaProducerFactory<>(
                    kafkaProperties.buildProducerProperties(), new StringSerializer(), serializer);
        }
    }

    @Bean
    public KafkaTemplate<String, PayoffEvent> rewardPayoffRequestKafkaTemplate(
            final ProducerFactory<String, PayoffEvent> producerFactory) {
        return new KafkaTemplate<>(producerFactory);
    }
}
