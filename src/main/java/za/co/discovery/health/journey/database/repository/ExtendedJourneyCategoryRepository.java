package za.co.discovery.health.journey.database.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExtendedJourneyCategoryRepository
        extends JourneyCategoryRepository,
                JpaRepository<JourneyCategory, Long>,
                PagingAndSortingRepository<JourneyCategory, Long> {

    Optional<JourneyCategory> findByCategoryCode(String code);

    List<JourneyCategory> findAllByJourneyCategoryType_name(String typeName);

    @Query("SELECT jc FROM JourneyCategory jc "
            + "JOIN jc.journeyCategoryType jct "
            + "WHERE jc.name = :name "
            + "AND jct.name = :type")
    Optional<JourneyCategory> findByNameAndType(String name, String type);

    @Query("SELECT jc FROM JourneyCategory jc" + " WHERE jc.journeyCategoryId = :categoryId")
    Optional<JourneyCategory> findJourneyCategoryById(@Param("categoryId") Long categoryId);
}
