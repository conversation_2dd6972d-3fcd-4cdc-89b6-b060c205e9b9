package za.co.discovery.health.journey.config;

import lombok.RequiredArgsConstructor;
import org.h2.tools.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.sql.SQLException;

/**
 * provides possibility to connect to h2 through datagrip/intelij
 * url: jdbc:h2:tcp://localhost:9090/mem:PROGRAM
 */
@Configuration
@Profile("local")
@RequiredArgsConstructor
public class H2Config {

    @Bean(initMethod = "start", destroyMethod = "stop")
    public Server inMemoryH2DatabaseaServer() throws SQLException {
        return Server.createTcpServer("-tcp", "-tcpAllowOthers", "-tcpPort", "9090");
    }
}
