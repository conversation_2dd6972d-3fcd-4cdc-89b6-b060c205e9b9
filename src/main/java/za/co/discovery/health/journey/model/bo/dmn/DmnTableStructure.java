package za.co.discovery.health.journey.model.bo.dmn;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Data
@Setter
@Getter
public class DmnTableStructure {
    private String decisionKey;
    private String decisionName;
    private List<DmnColumn> inputColumns = new ArrayList<>();
    private List<DmnColumn> outputColumns = new ArrayList<>();
    private List<DmnTableRow> rows = new ArrayList<>();
}
