package za.co.discovery.health.journey.model.bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.time.temporal.ChronoUnit;
import java.util.List;

@Data
@Builder
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class JourneyProgramResponse {

    private Long id;
    private String name;
    private String description;
    private long duration;
    private ChronoUnit durationUnit;
    private List<ActivityDetails> activities;
}
