package za.co.discovery.health.journey.service.journey.reward.facade;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.constant.JourneyRewardAwardStatusEnum;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneAward;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentProgramAward;
import za.co.discovery.health.journey.service.journey.reward.RewardAwardService;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@RequiredArgsConstructor
public class JourneyRewardAwardingFacade {
    private final RewardAwardService rewardAwardService;
    private final RewardPayoffFacade rewardPayoffFacade;

    public void processAwardedMilestoneRewards() {
        final List<JourneyEnrollmentMilestoneAward> milestoneAwardsToProcess =
                rewardAwardService.getAllMilestoneRewardsByStatus(JourneyRewardAwardStatusEnum.PENDING);
        log.info("{} Milestone Awards fetched to Process", milestoneAwardsToProcess.size());

        final AtomicInteger count = new AtomicInteger();
        milestoneAwardsToProcess.forEach(it -> {
            if (it.getExtRewardRef() != null && it.getExtRewardRef().isBlank()) {
                rewardPayoffFacade.processRewardPayoff(it);
                rewardAwardService.markMilestoneAwardAsProcessed(it.getJourneyEnrollmentMilestoneAwardId());
                count.set(count.get() + 1);
            }
        });
        log.info("{} Milestone Awards processed", count.get());
    }

    public void processAwardedProgramRewards() {
        final List<JourneyEnrollmentProgramAward> programAwardsToProcess =
                rewardAwardService.getAllProgramRewardsByStatus(JourneyRewardAwardStatusEnum.PENDING);
        log.info("{} Program Awards fetched to Process", programAwardsToProcess.size());

        final AtomicInteger count = new AtomicInteger();
        programAwardsToProcess.forEach(it -> {
            if (it.getExtRewardRef() != null && it.getExtRewardRef().isBlank()) {
                rewardPayoffFacade.processRewardPayoff(it);
                rewardAwardService.markMilestoneAwardAsProcessed(it.getJourneyEnrollmentProgramAwardId());
                count.set(count.get() + 1);
            }
        });
        log.info("{} Program Awards processed", count.get());
    }
}
