package za.co.discovery.health.journey.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.model.JourneyDetailsResponse;
import za.co.discovery.health.journey.model.preview.LimitedUserCategoryEnrollmentPreview;
import za.co.discovery.health.journey.model.user.LimitedUserCategoryEnrollmentDto;
import za.co.discovery.health.journey.service.JourneyFacade;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/v1/journey", produces = MediaType.APPLICATION_JSON_VALUE)
public class JourneyController {

    private final JourneyFacade journeyFacade;

    @GetMapping("/categories")
    public List<LimitedUserCategoryEnrollmentPreview> getJourneyCategories(
            @RequestParam final Long entityId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {
        return journeyFacade.getLimitedJourneyCategoryPreview(
                entityId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
    }

    @GetMapping("/categories/active")
    public List<LimitedUserCategoryEnrollmentDto> getActiveLimitedEnrollments(
            @RequestParam final Long entityId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {
        return journeyFacade.getActiveLimitedEnrollments(
                entityId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
    }

    @GetMapping("/categories/{categoryId}/details")
    public Optional<JourneyDetailsResponse> getJourneyDetails(
            @RequestParam final Long entityId,
            @PathVariable final Long categoryId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {
        return journeyFacade.getJourneyDetails(
                entityId, categoryId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
    }
}
