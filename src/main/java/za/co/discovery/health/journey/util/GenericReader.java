package za.co.discovery.health.journey.util;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class GenericReader<T> {

    private final ObjectReader objectReader;
    private final Class<T> type;
    private char delimiter = ',';

    @SuppressWarnings("PMD.ConstructorCallsOverridableMethod")
    public GenericReader(final Class<T> objectType) {
        this.type = objectType;
        this.objectReader = this.createObjectReader();
    }

    public GenericReader(final Class<T> objectType, final char delimiter) {
        this(objectType);
        this.delimiter = delimiter;
    }

    public CsvSchema genericCsvSchema() {
        final CsvSchema.Builder builder = CsvSchema.builder()
                .setUseHeader(true)
                .setColumnSeparator(delimiter)
                .setSkipFirstDataRow(false);
        final Field[] fields = type.getDeclaredFields();
        for (final Field field : fields) {
            if (field.getType().isArray()) {
                builder.addColumn(field.getName(), CsvSchema.ColumnType.ARRAY);
            } else {
                final JsonIgnore jsonIgnoreAnnotation = field.getAnnotation(JsonIgnore.class);
                final JsonProperty jsonPropertyAnnotation = field.getAnnotation(JsonProperty.class);
                if (jsonIgnoreAnnotation == null) {
                    if (jsonPropertyAnnotation == null) {
                        builder.addColumn(field.getName(), CsvSchema.ColumnType.STRING);
                    } else {
                        builder.addColumn(jsonPropertyAnnotation.value(), CsvSchema.ColumnType.STRING);
                    }
                }
            }
        }
        return builder.build();
    }

    @SuppressWarnings("PMD.AvoidUsingObjectMapperAsALocalVariable")
    private ObjectReader createObjectReader() {
        final CsvSchema schema = genericCsvSchema();
        final CsvMapper mapper = new CsvMapper();

        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, true);
        //        Ensures that too many or too few columns aren't present in any of the rows
        mapper.configure(CsvParser.Feature.FAIL_ON_MISSING_COLUMNS, true);
        mapper.configure(CsvParser.Feature.TRIM_SPACES, true);
        return mapper.readerFor(type).with(schema);
    }

    public List<T> readMe(final String fileName) {
        final ArrayList<T> list = new ArrayList<>();
        try (Reader reader = new InputStreamReader(Files.newInputStream(Paths.get(fileName)));
                MappingIterator<T> rules = objectReader.readValues(reader)) {
            while (rules.hasNext()) {
                final T ruleLine = rules.next();
                list.add(ruleLine);
            }
        } catch (IOException e) {
            log.error("bad things happened", e);
        }
        return list;
    }

    @SuppressWarnings({"PMD.AvoidCatchingGenericException", "PMD.AvoidThrowingRawExceptionTypes"})
    public List<T> readMe(final byte[] content) throws IOException {
        final ArrayList<T> list = new ArrayList<>();
        try (MappingIterator<T> rows = objectReader.readValues(content)) {
            Integer rowCount = 1;
            while (rows.hasNext()) {
                try {
                    rowCount++;
                    final T entry = rows.next();
                    list.add(entry);
                } catch (Exception e) {
                    log.error("Error Occurred: Stopping Import");
                    throw new RuntimeException(String.format("Failed to read line: %s", rowCount), e);
                }
            }
            log.info("Entries Read From Content {}: ", list.size());
        }

        return list;
    }

    @SuppressWarnings("PMD.AvoidCatchingThrowable")
    public List<T> readMe(final InputStream content) throws IOException {
        final List<T> list = new ArrayList<>();
        final AtomicInteger lineCounter = new AtomicInteger(0);
        try (MappingIterator<T> rules = objectReader.readValues(content)) {
            while (rules.hasNext()) {
                final int lineCounterItem = lineCounter.getAndIncrement();
                try {
                    final T ruleLine = rules.next();
                    list.add(ruleLine);
                } catch (Throwable ex) {
                    log.error("Error reading line [{}}] with error [{}]%n", lineCounterItem, ex.getMessage());
                    throw ex;
                }
            }
        }
        return list;
    }
}
