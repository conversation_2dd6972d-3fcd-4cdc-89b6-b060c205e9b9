{"content": [{"programId": "10245", "programName": "Vitality", "programEnrolmentId": "97513", "programEnrolmentCohortActivityId": "6547372", "cohortName": "Male and female sex at birth 65 years and older \nOR members diagnosed with diabetes\nOR members who have said they smoke cigarettes in the health assessment", "activityId": "6482509", "activityName": "The most important things to know about building savings", "activityStatus": "PENDING", "activityPoints": {"rewardType": "POINTS", "rewardAllocation": "10"}, "mnemonic": "BUSA", "effForm": "2023-08-01T05:00:00.000+00:00", "effTo": "2024-07-31T05:00:00.000+00:00", "content": {"id": 6482513, "category": "activity", "tag": "BUSA_6482509_REQUIREMENTS_MET", "activityState": "REQUIREMENTS_MET", "activityInfoCreationRequest": null}, "activityType": "Self reported", "categoryIds": [125, 98, 6, 41, 142], "frequency": "1 Calendar Year", "timeToComplete": null, "activityCompletedDate": null, "attributes": {"frequency_for_core": "OneAYear"}, "subActivities": [], "roles": ["PP", "SP"], "isPhysicalActivity": false, "entityId": "**********", "manuallyAdded": false}, {"programId": "10245", "programName": "Vitality", "programEnrolmentId": "97513", "programEnrolmentCohortActivityId": "212830", "cohortName": "Male and female sex at birth 65 years and older \nOR members diagnosed with diabetes\nOR members who have said they smoke cigarettes in the health assessment", "activityId": "199548", "activityName": "Goal: Prepare meals at home to prevent extra costs of eating out", "activityStatus": "PENDING", "activityPoints": {"rewardType": "POINTS", "rewardAllocation": "5"}, "mnemonic": "GCIE", "effForm": "2023-08-01T05:00:00.000+00:00", "effTo": "2024-07-31T05:00:00.000+00:00", "content": {"id": 6423185, "category": "activity", "tag": "GCIE_199548_REQUIREMENTS_MET", "activityState": "REQUIREMENTS_MET", "activityInfoCreationRequest": null}, "activityType": "Goals", "categoryIds": [125, 6, 141, 42, 100], "frequency": "1 Day", "timeToComplete": null, "activityCompletedDate": null, "attributes": {"group_for_core": "81", "frequency_for_core": "ThreePerDayWithinSameGroup", "goal_id": "**********"}, "subActivities": [], "roles": ["SP", "PP"], "isPhysicalActivity": false, "entityId": "**********", "manuallyAdded": false}], "pageable": {"sort": {"sorted": false, "unsorted": true, "empty": true}, "pageNumber": 0, "offset": 0, "pageSize": 10, "unpaged": false, "paged": true}, "totalElements": 210, "totalPages": 21, "last": false, "first": true, "sort": {"sorted": false, "unsorted": true, "empty": true}, "size": 10, "number": 0, "numberOfElements": 10, "empty": false}