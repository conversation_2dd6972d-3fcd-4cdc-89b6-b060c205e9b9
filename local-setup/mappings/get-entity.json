{"request": {"method": "GET", "urlPattern": "/v3/entity/api/getByEntityId\\?entityId=.*", "queryParameters": {"entityId": {"matches": ".*"}}}, "response": {"status": 200, "jsonBody": {"allianceId": 1, "empEntNo": 1, "curntBrncdA": "1", "memberEnityNo": 1424540472, "empInternalId": 408428, "empExtId": "VDG0001110", "empName": "VITALITY 3 TEST", "empContractNo": 3, "contractStartDate": "20240801", "contractEndDate": "20250731", "subCompany": 4200005, "allianceName": "VSA", "memNo": 5066272654, "memDepNo": 0, "coverageEffDate": "20230101", "wdActualDate": "00000000", "employeeNo": "VTHREE22", "particStat": "P", "riskModelId": 2, "riskModelDescription": "Full Risk Bill <PERSON>", "firstName": "TWENTYTWO", "lastName": "VTHREEMOBILE", "polCardNo": "VS01117475", "dateOfBirth": "1980-01-22T00:00:00.000Z", "gender": "M", "countryCd": "US", "employmentDate": "00000000", "email": "<EMAIL>"}, "headers": {"Content-Type": "application/json"}}}