{"request": {"method": "POST", "url": "/v3/program/api/v1/activity-transaction/filter?page=0&size=10000", "headers": {"Content-Type": {"equalTo": "application/json"}, "accept": {"equalTo": "application/json"}}, "bodyPatterns": [{"matches": ".*"}]}, "response": {"status": 200, "jsonBody": {"content": [{"id": 173742696, "activityAllocationStatus": "Pending", "pacProgramActivity": "Initial assessment", "transactionDate": "2025-06-26T03:36:29.577-05:00", "policyId": "5070665692", "entityNo": 1, "eventId": "NSBO", "postPointAward": 0, "accumFrom": "2025-06-26T03:36:29.747749-05:00", "accumTo": "9000-01-01T01:01:00-06:00", "createdDate": "2025-06-26T03:36:29.747749-05:00"}, {"id": 173852965, "activityAllocationStatus": "Pending", "pacProgramActivity": "Initial assessment", "transactionDate": "2025-06-26T15:48:59.449-05:00", "policyId": "5070665692", "entityNo": 1, "eventId": "NSBO", "postPointAward": 0, "accumFrom": "2025-06-26T15:48:59.696757-05:00", "accumTo": "9000-01-01T01:01:00-06:00", "createdDate": "2025-06-26T15:48:59.696757-05:00"}], "pageable": {"sort": {"sorted": false, "unsorted": true, "empty": true}, "pageNumber": 0, "offset": 0, "pageSize": 1000, "unpaged": false, "paged": true}, "totalElements": 2, "last": true, "totalPages": 1, "first": true, "sort": {"sorted": false, "unsorted": true, "empty": true}, "size": 1000, "number": 0, "numberOfElements": 2, "empty": false}, "headers": {"Content-Type": "application/json"}}}