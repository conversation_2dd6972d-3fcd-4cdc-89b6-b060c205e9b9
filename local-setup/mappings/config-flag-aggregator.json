{"request": {"method": "POST", "urlPattern": "/v3/config-flag-aggregator/api/configuration-flags"}, "response": {"status": 200, "jsonBody": [{"key": "MALL", "value": "Enabled"}, {"key": "FMAR", "value": "Enabled"}, {"key": "FRLV", "value": "High"}, {"key": "SVBU", "value": "Enabled"}, {"key": "MLDV", "value": "Enabled"}, {"key": "RBSB", "value": "Enabled"}, {"key": "SMKR", "value": "Enabled"}, {"key": "WMNR", "value": "Standard"}, {"key": "NGRB", "value": "Enabled"}, {"key": "GRSO", "value": "Enabled"}, {"key": "VCHV", "value": "WCS"}, {"key": "WFSS", "value": "Disabled"}, {"key": "VCSS", "value": "Yes"}, {"key": "VCSI", "value": "Employee ID"}, {"key": "QVCT", "value": "test"}, {"key": "CCHG", "value": "Trestle Tree"}, {"key": "HTCO", "value": "Enabled"}, {"key": "COVH", "value": "Enabled"}, {"key": "COVC", "value": "Enabled"}, {"key": "HTAD", "value": "Enabled"}, {"key": "ADVH", "value": "Enabled"}, {"key": "ADVC", "value": "Enabled"}, {"key": "LSCO", "value": "Disabled"}, {"key": "DEAN", "value": "Disabled"}, {"key": "TAVC", "value": "Disabled"}, {"key": "HOLI", "value": "Enabled"}, {"key": "NUEX", "value": "Enabled"}, {"key": "SLRE", "value": "Enabled"}, {"key": "STRE", "value": "Enabled"}, {"key": "CMCO", "value": "Disabled"}, {"key": "PDIA", "value": "Enabled"}, {"key": "DIAB", "value": "Enabled"}, {"key": "HICL", "value": "Enabled"}, {"key": "HYPE", "value": "Enabled"}, {"key": "WEMA", "value": "Enabled"}, {"key": "MECO", "value": "Enabled"}, {"key": "VSRS", "value": "Enabled"}, {"key": "VCHL", "value": "Enabled"}, {"key": "VCHE", "value": "Employee + Spouse"}, {"key": "VCHC", "value": "Enabled"}, {"key": "VCPP", "value": "Enabled"}, {"key": "TBCA", "value": "Enabled"}, {"key": "HLAZ", "value": "Enabled"}, {"key": "COAC", "value": "Enabled"}, {"key": "CONS", "value": "Enabled"}, {"key": "CCCH", "value": "Enabled"}, {"key": "EXPS", "value": "Enabled"}, {"key": "OURR", "value": "Enabled"}, {"key": "ACFD", "value": null}, {"key": "BBPC", "value": "Plan A"}, {"key": "THR1", "value": ""}, {"key": "PRC1", "value": ""}, {"key": "THR2", "value": ""}, {"key": "PRC2", "value": ""}, {"key": "PHID", "value": "Yes"}, {"key": "CFPE", "value": "Never award"}, {"key": "ACRS", "value": "EE and SP"}, {"key": "APWT", "value": "EE and SP"}, {"key": "AWNP", "value": "Enabled"}, {"key": "AWDD", "value": "7"}, {"key": "OAWL", "value": "True"}, {"key": "SRPR", "value": "Disabled"}, {"key": "PF01", "value": ""}, {"key": "PF02", "value": ""}, {"key": "GRBN", "value": null}, {"key": "RPRO", "value": null}, {"key": "ADMC", "value": "Enabled"}, {"key": "PHP", "value": "Enabled"}, {"key": "FTUJ", "value": null}, {"key": "LFCR", "value": null}, {"key": "NWYK", "value": null}, {"key": "OBCL", "value": null}, {"key": "IPGC", "value": "Disabled"}, {"key": "MEDC", "value": "True"}, {"key": "CPER", "value": "0"}, {"key": "COUN", "value": "US"}, {"key": "AITR", "value": "Enabled"}, {"key": "BNFT", "value": "Enabled"}, {"key": "VTUP", "value": "Enabled"}, {"key": "VTST", "value": "Enabled"}, {"key": "VTPL", "value": "Enabled"}, {"key": "VTPD", "value": "Enabled"}, {"key": "TXTM", "value": "Disabled"}, {"key": "CCUI", "value": "Enabled"}, {"key": "CCTN", "value": "1234-567-8900"}, {"key": "CCFN", "value": ""}, {"key": "CCEA", "value": "<EMAIL>"}, {"key": "CCHO", "value": "8-5"}, {"key": "SFCO", "value": "Enabled"}, {"key": "SFHA", "value": "Enabled"}, {"key": "SFBI", "value": "Enabled"}, {"key": "EVCM", "value": "Yes"}, {"key": "SVCM", "value": "Yes"}, {"key": "CVCM", "value": "Disabled"}, {"key": "OTRC", "value": "Yes"}, {"key": "BILB", "value": "0"}, {"key": "BWLP", "value": "5"}, {"key": "BILP", "value": "0"}, {"key": "PRBC", "value": "Disabled"}, {"key": "PRBP", "value": "0"}, {"key": "RFRI", "value": "Employee ID"}, {"key": "MLFC", "value": "Standard"}, {"key": "MLMP", "value": "0.01"}, {"key": "REON", "value": "Enabled"}, {"key": "TRNS", "value": "Enabled"}, {"key": "DEVC", "value": "Disabled"}, {"key": "HSEN", "value": "Enabled"}, {"key": "NPSF", "value": "Disabled"}, {"key": "EIP", "value": false}, {"key": "ALLIANCE", "value": 5}, {"key": "BRANCH", "value": "CORE"}, {"key": "GROUP", "value": "1423116563"}, {"key": "ROLE", "value": "P"}, {"key": "group_coaching", "value": true}, {"key": "coach_chat", "value": true}, {"key": "help_center_test_oura", "value": true}, {"key": "chronic_disease_activity_enabled", "value": true}, {"key": "GLP1_feature_flag", "value": true}, {"key": "journey_enabled", "value": true}, {"key": "help_center_test_flag", "value": true}, {"key": "OnboardingFocusQuittingSmoking", "value": "No"}, {"key": "OnboardingFocusDrinkingSensibly", "value": "No"}, {"key": "OnboardingFocusEatingBetter", "value": "No"}, {"key": "OnboardingFocusSleepingBetter", "value": "No"}, {"key": "OnboardingFocusCopingWithStress", "value": "No"}, {"key": "OnboardingFocusPreferNo", "value": "No"}, {"key": "MentalWellbeingGoalLessLonely", "value": "No"}, {"key": "MentalWellbeingGoalLiveInMoment", "value": "No"}, {"key": "MentalWellbeingGoalOther", "value": "No"}, {"key": "MentalWellbeingGoalPurpose", "value": "No"}, {"key": "MentalWellbeingGoalCalm", "value": "No"}, {"key": "MentalWellbeingGoalTherapist", "value": "No"}, {"key": "MentalWellbeingGoalNone", "value": "No"}, {"key": "MentalWellbeingGoalDealWithStress", "value": "No"}, {"key": "MentalWellbeingGoalLessAngry", "value": "No"}, {"key": "MentalWellbeingActivities", "value": "No"}, {"key": "CaregivingOther", "value": "No"}, {"key": "CaregivingDisabled", "value": "No"}, {"key": "ExerciseGoalBeHealthy", "value": "No"}, {"key": "ExerciseGoalNone", "value": "No"}, {"key": "ExerciseGoalSpecificTraining", "value": "No"}, {"key": "ExerciseGoalFeelBetter", "value": "No"}, {"key": "ExerciseGoalWeight", "value": "No"}, {"key": "ExerciseLimitationDisability", "value": "No"}, {"key": "ExerciseLimitationNone", "value": "No"}, {"key": "ExerciseLimitationSurgeryInjury", "value": "No"}, {"key": "CaregivingNone", "value": "No"}, {"key": "Caregiving<PERSON><PERSON><PERSON><PERSON>", "value": "No"}, {"key": "HealthyEatingLimitationAllergies", "value": "No"}, {"key": "HealthyEatingLimitationNone", "value": "No"}, {"key": "HealthyEatingLimitationMedicalCondition", "value": "No"}, {"key": "HealthyEatingGoalFeelBetter", "value": "No"}, {"key": "HealthyEatingGoalAthletics", "value": "No"}, {"key": "HealthyEatingGoalNone", "value": "No"}, {"key": "HealthyEatingGoalBeHealthy", "value": "No"}, {"key": "OnboardingFocusExercising", "value": "Yes"}, {"key": "MentalWellbeingLifeChange", "value": "Prefer not to answer"}, {"key": "MentalWellbeingEffort", "value": "6"}, {"key": "MentalWellbeingTraumaticEvent", "value": "Yes"}, {"key": "MentalWellbeingBarriers", "value": "I have a challenge not listed"}, {"key": "MentalWellbeingConfidence", "value": "9"}, {"key": "MentalWellbeingGoalNotFeelDown", "value": "Yes"}, {"key": "ExerciseGoalAppearance", "value": "Yes"}, {"key": "ExerciseConfidence", "value": "5"}, {"key": "ExerciseBarriers", "value": "I don’t have time"}, {"key": "ExerciseLimitationMedicalCondition", "value": "Yes"}, {"key": "ExerciseEnjoyActivities", "value": "Yes"}, {"key": "ExerciseEffort", "value": "6"}, {"key": "HealthyEatingRecipe", "value": "Yes"}, {"key": "HealthyEatingEffort", "value": "5"}, {"key": "HealthyEatingConfidence", "value": "4"}, {"key": "HealthyEatingLimitationPersonal", "value": "Yes"}, {"key": "HealthyEatingGoalWeight", "value": "Yes"}, {"key": "HealthyEatingBarriers", "value": "It costs too much money"}, {"key": "CaregivingAgingOrSick", "value": "Yes"}, {"key": "CaregivingEffort", "value": "5"}, {"key": "CaregivingPreferredActivity", "value": "Yes"}, {"key": "MuscleJointPainFrequency", "value": "Sometimes"}, {"key": "MuscleJointStretch", "value": "Yes"}, {"key": "WHO5Cheerful", "value": "1SomeOfTheTime"}, {"key": "WHO5Interest", "value": "4MostOfTheTime"}, {"key": "WHO5Active", "value": "2LessThanHalf"}, {"key": "WHO5Calm", "value": "3MoreThanHalf"}, {"key": "WHO5Rested", "value": "1SomeOfTheTime"}, {"key": "GLP1InsuranceReferral", "value": "Yes"}, {"key": "GLP1FreeScaleRequest", "value": "No"}, {"key": "HappyWithStress", "value": "2Disagree"}, {"key": "ManagedHealthConditions", "value": "3Neutral"}, {"key": "PerceivedStressScaleDate", "value": "2025-06-24T05:00:00.000Z"}, {"key": "LastBiometrics12Weeks", "value": "Yes"}, {"key": "HappyWithEating", "value": "2Disagree"}, {"key": "BelongingAndPurpose", "value": "2Disagree"}, {"key": "HappyWithMentalHealth", "value": "3Neutral"}, {"key": "PerceivedStressScale12Weeks", "value": "No"}, {"key": "MedicalCOPD", "value": "No"}, {"key": "HappyWithOtherHabits", "value": "4Agree"}, {"key": "HappyWithActivity", "value": "3Neutral"}, {"key": "PerceivedStressScaleScore", "value": "1"}, {"key": "HappyWithBody", "value": "2Disagree"}, {"key": "CloseToOtherPeople", "value": "2Disagree"}, {"key": "LastBiometricsDate", "value": "2025-06-26T05:00:00.000Z"}, {"key": "CoachingFocusHumanConnection", "value": "Yes"}, {"key": "CoachingFocusS<PERSON>s", "value": "No"}, {"key": "CoachingFocusWeight", "value": "Yes"}, {"key": "CoachingFocusH<PERSON><PERSON>", "value": "No"}, {"key": "CoachingFocusMentalHealth", "value": "No"}, {"key": "CoachingFocusNutrition", "value": "Yes"}, {"key": "CoachingFocusConditionManagement", "value": "No"}, {"key": "CoachingFocusPhysicalActivity", "value": "Yes"}, {"key": "CoachingFocusNotApplicable", "value": "No"}, {"key": "StickToGoalsPastMonth", "value": "2LessThanHalf"}, {"key": "KnowActionsToReachGoals", "value": "4KnowQuiteaBit"}, {"key": "FollowRecommendationsPhysicalActivity", "value": "3MoreThanHalf"}, {"key": "CoachingReadinessToChange", "value": "Preparation"}, {"key": "InspiredToReachGoals", "value": "0NoneOfTheTime"}, {"key": "TrackToReachGoals", "value": "5AllOfTheTime"}, {"key": "FollowRecommendationsNutrition", "value": "2LessThanHalf"}, {"key": "SleepSatisfaction", "value": "5"}, {"key": "InactiveTimePerDay", "value": "SeventoEightHours"}, {"key": "PhysicalActivityExerciseMinutes", "value": "FortyFiveMinute"}, {"key": "FluVaccineLastYear", "value": "Yes"}, {"key": "MedicalSystolic", "value": "180"}, {"key": "PhysicalActivityMotivationToImprove", "value": "4"}, {"key": "DemographicQualificationHighest", "value": "PostgraduateDegreeFromUniversity"}, {"key": "LifestyleWorkingHoursExpectedByEmployerInTypicalWeek", "value": "8"}, {"key": "SexAtBirth", "value": "Female"}, {"key": "MentalHealthNervousAnxiousPastTwoWeeks", "value": "Several"}, {"key": "MedicalHeightValue", "value": "68"}, {"key": "PhysicalActivityExerciseDaysPerWeek", "value": "3"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "No"}, {"key": "MedicalWeightValue", "value": "100"}, {"key": "MealsAndSnacksFruitPerDay", "value": "OneandHalf"}, {"key": "LifestyleWellnessWorkHoursMissedInLast7Days", "value": "1"}, {"key": "LifestyleSleepHoursPerDay", "value": "8"}, {"key": "LifestyleWellnessProductivityInLast7Days", "value": "5"}, {"key": "MentalHealthStopWorryingPastTwoWeeks", "value": "Several"}, {"key": "TableSaltAtMealtime", "value": "Sometimes"}, {"key": "MedicalCareDiabetes", "value": "Yes"}, {"key": "MedicalHistoryChronicPain", "value": "No"}, {"key": "MedicalStroke", "value": "No"}, {"key": "MedicalOsteoporosis", "value": "No"}, {"key": "MedicalCancer", "value": "No"}, {"key": "MedicalChronicLungDisease", "value": "No"}, {"key": "MedicalDiabetes", "value": "Yes"}, {"key": "MedicalMenopause", "value": "No"}, {"key": "MentalHealthAnxiety", "value": "No"}, {"key": "MedicalHeartDisease", "value": "No"}, {"key": "MedicalCholesterolHigh", "value": "No"}, {"key": "MentalHealthDepression", "value": "No"}, {"key": "MedicalPrediabetes", "value": "No"}, {"key": "MedicalAsthma", "value": "No"}, {"key": "MedicalHistoryBackPain", "value": "No"}, {"key": "MedicalHypertension", "value": "Yes"}, {"key": "MealsAndSnacksVegPerDay", "value": "OneandHalf"}, {"key": "CervicalScreenLast3Years", "value": "Yes"}, {"key": "PhysicalActivitySatisfaction", "value": "4"}, {"key": "CurrentLifeSatisfaction", "value": "5"}, {"key": "MentalWellbeingSatisfaction", "value": "4"}, {"key": "MealsAndSnacksWholeGrainsPerDay", "value": "LessThanOne"}, {"key": "MealsAndSnacksSugarBeveragesPerDay", "value": "Two"}, {"key": "MealsAndSnacksRedMeatPerDay", "value": "One"}, {"key": "LifestyleAlcoholDrinker", "value": "No"}, {"key": "MentalWellbeingMotivationToImprove", "value": "4"}, {"key": "MealsAndSnacksProcessedMeatPerDay", "value": "One"}, {"key": "MedicalDiastolic", "value": "80"}, {"key": "PhysicalActivityExerciseIntensity", "value": "Moderate"}, {"key": "MentalHealthLittleInterestPastTwoWeeks", "value": "Several"}, {"key": "WorkSatisfaction", "value": "5"}, {"key": "DietMotivationToImprove", "value": "5"}, {"key": "LifestyleTobaccoOtherThanCigarettes", "value": "Never"}, {"key": "LifestyleWorkingHoursInPast7Days", "value": "8"}, {"key": "LifestyleDoYouSmokeCigarettes", "value": "Never"}, {"key": "GenderIdentification", "value": "Yes"}, {"key": "DietSatisfaction", "value": "5"}, {"key": "MedicalBloodPressureTreatment", "value": "Yes"}, {"key": "LifestyleHealthAssessment", "value": "VeryGood"}, {"key": "MealsAndSnacksLegumesPerWeek", "value": "OneandHalf"}, {"key": "MealsAndSnacksNutsPerWeek", "value": "OneandHalf"}, {"key": "WeightSatisfaction", "value": "5"}, {"key": "SleepMotivationToImprove", "value": "6"}, {"key": "DemographicHispanicLatinoSpanishOrigin", "value": "No"}, {"key": "WeightMotivationToImprove", "value": "5"}, {"key": "MedicalCareHighBloodPressure", "value": "Yes"}, {"key": "MentalHealthDepressedHopelessPastTwoWeeks", "value": "Several"}, {"key": "VitalityAge", "value": "45.822663931208496"}, {"key": "GAD2Score", "value": "2"}, {"key": "PHQ2Score", "value": "2"}, {"key": "MedicalBodyMassIndexGrade", "value": "15.203287197231832"}, {"key": "CoachingBurnoutLevel", "value": "4ManySymptoms"}, {"key": "StressedOrOverwhelmedPastTwoWeeks", "value": "Always"}, {"key": "CoachingBurnoutKnowledgeable", "value": "5StronglyDisagree"}, {"key": "CoachingBurnoutConfident", "value": "5StronglyDisagree"}, {"key": "CoachingBurnoutRecommend", "value": "6"}, {"key": "FinancialBarriers", "value": "I accumulate credit card debt"}, {"key": "FinancialCalculateMinimum", "value": "Yes"}, {"key": "FinancialSpendOrSave", "value": "I’m a spender"}, {"key": "N/A", "value": "No"}, {"key": "FinancialConfidence", "value": "4"}, {"key": "FinancialEffort", "value": "6"}, {"key": "CoachingOutcomeActive", "value": "0NoneOfTheTime"}, {"key": "CoachingOutcomeRested", "value": "0NoneOfTheTime"}, {"key": "CoachingOutcomeInterest", "value": "0NoneOfTheTime"}, {"key": "CoachingOutcomeCheerful", "value": "0NoneOfTheTime"}, {"key": "CoachingOutcomeCalm", "value": "0NoneOfTheTime"}, {"key": "IRP", "value": true}], "headers": {"Content-Type": "application/json"}}}