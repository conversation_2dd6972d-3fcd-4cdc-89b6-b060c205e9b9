{"request": {"method": "GET", "urlPattern": "/v3/features/api/v1/features/member-configuration-flags\\?entityNumber=.*", "queryParameters": {"entityNumber": {"matches": ".*"}}}, "response": {"status": 200, "jsonBody": [{"key": "HRAC", "value": "Y"}, {"key": "MALL", "value": "Enabled"}, {"key": "FMAR", "value": "Enabled"}, {"key": "FRLV", "value": "High"}, {"key": "SVBU", "value": "Enabled"}, {"key": "MLDV", "value": "Disabled"}, {"key": "RBSB", "value": "Enabled"}, {"key": "SMKR", "value": "Enabled"}, {"key": "WMNR", "value": "Standard"}, {"key": "NGRB", "value": "Enabled"}, {"key": "GRSO", "value": "Enabled"}, {"key": "VCHV", "value": "CRL"}, {"key": "WFSS", "value": "Disabled"}, {"key": "VCSS", "value": "Yes"}, {"key": "VCSI", "value": "Employee ID"}, {"key": "QVCT", "value": "123"}, {"key": "CCHG", "value": "Healthworks Coaching"}, {"key": "HTCO", "value": "Disabled"}, {"key": "COVH", "value": "Disabled"}, {"key": "COVC", "value": "Disabled"}, {"key": "HTAD", "value": "Disabled"}, {"key": "ADVH", "value": "Enabled"}, {"key": "ADVC", "value": "Disabled"}, {"key": "LSCO", "value": "Disabled"}, {"key": "DEAN", "value": "Disabled"}, {"key": "TAVC", "value": "Disabled"}, {"key": "HOLI", "value": "Enabled"}, {"key": "NUEX", "value": "Enabled"}, {"key": "SLRE", "value": "Enabled"}, {"key": "STRE", "value": "Enabled"}, {"key": "CMCO", "value": "Disabled"}, {"key": "PDIA", "value": "Enabled"}, {"key": "DIAB", "value": "Enabled"}, {"key": "HICL", "value": "Enabled"}, {"key": "HYPE", "value": "Enabled"}, {"key": "WEMA", "value": "Enabled"}, {"key": "MECO", "value": "Enabled"}, {"key": "VSRS", "value": "Disabled"}, {"key": "VCHL", "value": "Enabled"}, {"key": "VCHE", "value": "Employee + Spouse"}, {"key": "VCHC", "value": "Enabled"}, {"key": "VCPP", "value": "Enabled"}, {"key": "TBCA", "value": "Enabled"}, {"key": "HLAZ", "value": "Enabled"}, {"key": "COAC", "value": "Enabled"}, {"key": "CONS", "value": "Enabled"}, {"key": "CCCH", "value": "Disabled"}, {"key": "EXPS", "value": "Disabled"}, {"key": "OURR", "value": "Enabled"}, {"key": "ACFD", "value": null}, {"key": "BBPC", "value": "Plan A"}, {"key": "THR1", "value": ""}, {"key": "PRC1", "value": ""}, {"key": "THR2", "value": ""}, {"key": "PRC2", "value": ""}, {"key": "PHID", "value": "Yes"}, {"key": "CFPE", "value": "Never award"}, {"key": "ACRS", "value": "EE and SP"}, {"key": "APWT", "value": "EE and SP"}, {"key": "AWNP", "value": "Enabled"}, {"key": "AWDD", "value": "7"}, {"key": "OAWL", "value": "False"}, {"key": "SRPR", "value": "Disabled"}, {"key": "PF01", "value": ""}, {"key": "PF02", "value": ""}, {"key": "GRBN", "value": null}, {"key": "RPRO", "value": null}, {"key": "ADMC", "value": "Enabled"}, {"key": "PHP", "value": "Enabled"}, {"key": "FTUJ", "value": null}, {"key": "LFCR", "value": null}, {"key": "NWYK", "value": null}, {"key": "OBCL", "value": null}, {"key": "MEDC", "value": "False"}, {"key": "CPER", "value": "10"}, {"key": "COUN", "value": "US"}, {"key": "AITR", "value": "Enabled"}, {"key": "BNFT", "value": "Enabled"}, {"key": "VTUP", "value": "Enabled"}, {"key": "VTST", "value": "Enabled"}, {"key": "VTPL", "value": "Enabled"}, {"key": "VTPD", "value": "Enabled"}, {"key": "TXTM", "value": "Disabled"}, {"key": "CCUI", "value": "Enabled"}, {"key": "CCTN", "value": "71845678953"}, {"key": "CCFN", "value": "71845678953"}, {"key": "CCEA", "value": "<EMAIL>"}, {"key": "CCHO", "value": "9-6"}, {"key": "SFCO", "value": "Enabled"}, {"key": "SFHA", "value": "Enabled"}, {"key": "SFBI", "value": "Enabled"}, {"key": "EVCM", "value": "No"}, {"key": "SVCM", "value": "No"}, {"key": "CVCM", "value": "Disabled"}, {"key": "OTRC", "value": "No"}, {"key": "BILB", "value": "0"}, {"key": "BWLP", "value": "5"}, {"key": "BILP", "value": "0"}, {"key": "PRBC", "value": "Disabled"}, {"key": "PRBP", "value": "0"}, {"key": "RFRI", "value": "Employee ID"}, {"key": "MLFC", "value": "Standard"}, {"key": "MLMP", "value": "0.01"}, {"key": "REON", "value": "Disabled"}, {"key": "TRNS", "value": "Enabled"}, {"key": "DEVC", "value": "Enabled"}, {"key": "HSEN", "value": "Enabled"}], "headers": {"Content-Type": "application/json"}}}